version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:7.0
    container_name: shoeby-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: shoeby_conversations
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongodb-init.js:/docker-entrypoint-initdb.d/mongodb-init.js:ro
    networks:
      - shoeby-network

  # Weaviate vector database service
  weaviate:
    image: semitechnologies/weaviate:1.22.4
    container_name: shoeby-weaviate
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "50051:50051"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
      LOG_LEVEL: 'debug'
    volumes:
      - weaviate_data:/var/lib/weaviate
    networks:
      - shoeby-network

  # Optional: Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   container_name: shoeby-redis
  #   restart: unless-stopped
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   networks:
  #     - shoeby-network

volumes:
  mongodb_data:
    driver: local
  weaviate_data:
    driver: local
  # redis_data:
  #   driver: local

networks:
  shoeby-network:
    driver: bridge
