"""
Pydantic models for the Shoeby Agent API.

This package contains all request/response models organized by domain.
"""

from .chat import (
    ChatRequest,
    ChatResponse,
    SessionCreateRequest,
    SessionCreateResponse,
    HistoryMessage,
    HistoryResponse,
)
from .basket import (
    BasketItem,
    BasketResponse,
    AddToBasketRequest,
    RemoveFromBasketRequest,
    UpdateQuantityRequest,
)
from .common import ProviderSwitchRequest, ProviderSwitchResponse

__all__ = [
    # Chat models
    "ChatRequest",
    "ChatResponse",
    "SessionCreateRequest",
    "SessionCreateResponse",
    "HistoryMessage",
    "HistoryResponse",
    # Basket models
    "BasketItem",
    "BasketResponse",
    "AddToBasketRequest",
    "RemoveFromBasketRequest",
    "UpdateQuantityRequest",
    # Common models
    "ProviderSwitchRequest",
    "ProviderSwitchResponse",
]
