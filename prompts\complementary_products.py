"""
Complementary products prompts for the <PERSON><PERSON>by agent.
Handles suggesting complementary products that work well with items the customer has selected.
"""


def get_complementary_products_search_prompt(base_item: str, focused_instructions: str) -> str:
    """
    Generate a prompt for determining what complementary products to search for.
    
    Args:
        base_item (str): The main product the customer added to their basket
        focused_instructions (str): Focused, relevant instructions for the current turn
        
    Returns:
        str: Formatted prompt for complementary products search strategy
    """
    return f"""
    A customer has just added "{base_item}" to their shopping basket.

    Instructions: {focused_instructions}

    As a fashion styling expert, determine what specific complementary products to search for.
    CRITICAL: Do NOT suggest products in the same category as the added item.

    For example:
    - If they added a hoodie (tops category), suggest bottoms, shoes, accessories
    - If they added jeans (bottoms category), suggest tops, shoes, accessories  
    - If they added shoes (footwear category), suggest tops, bottoms, accessories
    - If they added a dress (dresses category), suggest shoes, accessories, outerwear

    Respond with a JSON object containing:
    {{
        "search_query": "specific search terms for complementary products",
        "categories": ["category1", "category2", "category3"],
        "reasoning": "why these products complement the base item",
        "pairing_notes": "specific styling advice for pairing",
        "style_focus": "the overall style direction (e.g., casual, formal, edgy)"
    }}

    Keep the search query specific and actionable for product search.
    Ensure categories are different from the added item's category.
    """


def get_complementary_products_presentation_prompt(
    base_item: str, 
    focused_instructions: str, 
    style_focus: str, 
    reasoning: str, 
    pairing_notes: str, 
    complementary_products: str
    ) -> str:
    """
    Generate a prompt for presenting complementary products to the customer.
    
    Args:
        base_item (str): The main product the customer added to their basket
        focused_instructions (str): Focused, relevant instructions for the current turn
        style_focus (str): The overall style direction
        reasoning (str): Why these products complement the base item
        pairing_notes (str): Specific styling advice for pairing
        complementary_products (str): Formatted list of complementary products
        
    Returns:
        str: Formatted prompt for complementary products presentation
    """
    return f"""
        A customer has added "{base_item}" to their basket and is looking for complementary products.

        Instructions: {focused_instructions}

        Style Focus: {style_focus}
        Reasoning: {reasoning}
        Pairing Notes: {pairing_notes}

        Found complementary products:
        {complementary_products}

        Present these complementary products in a helpful, enthusiastic way that:
        - Acknowledges their great choice with {base_item}
        - Explains why each suggested item works well together
        - Highlights the value of creating a complete look
        - Includes specific styling advice
        - Encourages them to consider adding items to their basket
        - Uses a warm, personal tone

        Be specific about why each suggested item works well with {base_item}.
        Always include the product links when mentioning specific items.
        """
