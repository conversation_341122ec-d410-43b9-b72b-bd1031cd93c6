import logging
from typing import Any, Callable

from ..types import ProviderFunctions


def _init() -> None:
    logging.info("Null provider initialised.")


def _update_trace(*args, **kwargs) -> bool:
    return True


def _do_nothing(*args, **kwargs) -> Callable[..., Any]:
    def decorator(func):
        return func

    return decorator


def _observe(
    func: Callable[..., Any],
    *args: Any,
    **kwargs: Any,
) -> Callable[..., Any]:
    return _do_nothing()(func)


def get_provider_functions() -> ProviderFunctions:
    return {
        "update_trace": _update_trace,
        "observe": _observe,
        "name": "null",
        "init": _init,
    }
