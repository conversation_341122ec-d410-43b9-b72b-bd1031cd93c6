import asyncio
import logging
from dataclasses import dataclass
from typing import Any, Dict, List
from bson import CodecOptions, UuidRepresentation
from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorCollection,
    AsyncIOMotorDatabase,
)

from .metrics import async_time_history
from .conversational_schema import (
    ConversationData,
    ConversationHistoryDocument,
)


@dataclass
class ChatHistoryService:
    mongo_client: AsyncIOMotorClient
    mongo_database: str
    mongo_collection: str
    codec_options: CodecOptions = CodecOptions(
        uuid_representation=UuidRepresentation.STANDARD,
    )

    def __post_init__(self) -> None:
        self.db: AsyncIOMotorDatabase = self.mongo_client[self.mongo_database]
        self.hist_collection: AsyncIOMotorCollection = self.db.get_collection(
            self.mongo_collection,
        )
        asyncio.create_task(self.ensure_indexes())

    async def ensure_indexes(self) -> None:
        """
        Ensure database indexes are created for optimal query performance.
        
        Creates compound index on user_id and session_id for efficient lookups.
        """
        try:
            # Create compound index on user_id and session_id
            await self.hist_collection.create_index([
                ("user_id", 1),
                ("session_id", 1)
            ])
            logging.info("History service indexes ensured")
        except Exception as e:
            logging.error(f"Failed to create history service indexes: {e}")

    @async_time_history
    async def save_history(
        self,
        user_id: str,
        session_id: str,  
        data: ConversationData,
    ) -> None:
        """Save user conversation history

        Args:
            user_id (str): user id
            session_id (str): session id as string
            data (ConversationData): data to be stored
        """

        conversation_data = data
        user_history = await self.hist_collection.find_one(
            {"user_id": user_id, "session_id": session_id},
            {"data": 1},
        )

        if user_history is None:
            hist_doc = ConversationHistoryDocument(
                user_id=user_id,
                session_id=session_id,
                data=[conversation_data],
            )
            await self.hist_collection.insert_one(hist_doc.model_dump())

        else:
            user_history["data"].append(conversation_data.model_dump())
            await self.hist_collection.update_one(
                {"_id": user_history["_id"]},
                {"$set": {"data": user_history["data"]}},
            )

    @async_time_history
    async def get_last_conversations(
        self,
        user_id: str,
        session_id: str, 
        count: int,
        include_obsolete: bool = False,
    ) -> List[Dict[str, Any]]:
        """Get the last X conversation messages for a session.

        Args:
            user_id (str): user id
            session_id (str): session id as string
            count (int): number of last messages to retrieve
            include_obsolete (bool): whether to include obsolete messages

        Returns:
            List[Dict[str, Any]]: list of conversation data entries
        """
        user_history = await self.hist_collection.find_one(
            {"user_id": user_id, "session_id": session_id},
            {"_id": 0, "data": 1},
        )

        chat_histories: List[dict] = user_history["data"] if user_history else []

        if not include_obsolete:
            chat_histories = [
                chat_history
                for chat_history in chat_histories
                if not chat_history.get("obsolete")
            ]

        return chat_histories[-count:]

    @async_time_history
    async def get_history(
        self,
        user_id: str,
        session_id: str, 
        include_obsolete: bool = False,
    ) -> List[Dict[str, Any]]:
        user_history = await self.hist_collection.find_one(
            {"user_id": user_id, "session_id": session_id},
            {"_id": 0, "data": 1},
        )
        logging.info(msg="User history from mongo")
        chat_histories: List[dict] = user_history["data"] if user_history else []

        if include_obsolete:
            return chat_histories

        chat_histories = [
            chat_history
            for chat_history in chat_histories
            if not chat_history.get("obsolete")
        ]

        return chat_histories

    @async_time_history
    async def mark_history_as_obsolete(self, user_id: str, session_id: str) -> None:  # Changed from UUID4 to str
        """Mark all conversation data as obsolete in the user's history.

        Args:
            user_id (str): user id
            session_id (str): session id as string
        """
        await self.hist_collection.update_one(
            {"user_id": user_id, "session_id": session_id},
            {"$set": {"data.$[].obsolete": True}},
        )
