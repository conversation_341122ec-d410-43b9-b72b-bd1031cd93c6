# Architecture Patterns and Design Guidelines

## Overall Architecture
Multi-agent system with pluggable frameworks, following a layered architecture:
- **API Layer**: FastAPI routers handling HTTP requests
- **Orchestration Layer**: LangGraph-based agent coordination
- **Agent Layer**: Specialized agents for different tasks
- **Tool Layer**: Concrete implementations for external operations
- **Service Layer**: Core services (LLM, Conversation, DateTime)
- **Data Layer**: MongoDB for persistence, Weaviate for search

## Key Design Patterns

### 1. Registry Pattern
- `AgentsRegistry` and `ToolsRegistry` provide centralized management
- Dynamic registration of agents and tools
- Lookup by name or capability

### 2. Abstract Base Classes
- `BaseAgent` defines interface for all agents
- `BaseTool` defines interface for all tools
- Ensures consistent implementation across components

### 3. Dependency Injection
- Services injected through constructors
- Configuration passed as Config objects
- Promotes testability and loose coupling

### 4. Factory Pattern
- Framework factory for creating orchestrators
- Agent/Tool factories via registries

### 5. Strategy Pattern
- Different agents implement different strategies
- Strategy selection based on user intent
- LLM service abstracts different providers

### 6. Singleton Services
- Shared service instances (LLMService, ConversationService)
- Managed lifecycle through ShoebyAgent

## Agent Types and Responsibilities

### Search Agents
- `ConstructWeaviateQueryAgent`: Builds search queries
- `EvaluateProductsAgent`: Evaluates search results

### Presentation Agents
- `PresentProductsAgent`: Formats product displays

### Conversation Agents
- `AskFollowUpQuestionAgent`: Generates clarifying questions
- `RedirectIrrelevantQueryAgent`: Handles off-topic queries

### Purchase Agents
- `SuggestComplementaryProductsAgent`: Recommends related items

## Tool Categories

### Database Tools
- Weaviate integration for semantic search
- MongoDB for conversation persistence
- BrainPowa LLM communication

### Product Tools
- Product details retrieval
- Basket management
- Complementary product suggestions

## State Management
- LangGraph manages conversation state
- Session-based caching in MongoDB
- Stateless agent execution

## Error Handling Strategy
- Try-catch at service boundaries
- Graceful degradation for external service failures
- Logging for debugging and monitoring

## Configuration Management
- Environment variables via .env files
- Config classes for type-safe configuration
- Merchant-specific config in JSON files