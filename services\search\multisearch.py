# cc_multiagentic/search/multi_search.py
from __future__ import annotations
import os
from typing import Any, Dict, Iterable, Optional, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

VI_BASE = "https://search.visenze.com"
MULTISEARCH_PATH = "/v1/product/multisearch"

def _resolve_auth(app_key: Optional[str], placement_id: Optional[str]) -> Tuple[str, str]:
    ak = app_key or os.getenv("VISENZE_APP_KEY")
    pid = placement_id or os.getenv("VISENZE_PLACEMENT_ID_SEARCH") or os.getenv("VISENZE_PLACEMENT_ID")
    if not ak or not pid:
        raise ValueError(
            "ViSenze app_key and placement_id are required. "
            "Pass as args or set VISENZE_APP_KEY and VISENZE_PLACEMENT_ID[_SEARCH]."
        )
    return ak, pid

def _session() -> requests.Session:
    s = requests.Session()
    s.headers.update({"Accept": "application/json", "Accept-Encoding": "gzip"})
    retries = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=(429, 500, 502, 503, 504),
        allowed_methods=frozenset(["POST"]),
        raise_on_status=False,
    )
    s.mount("https://", HTTPAdapter(max_retries=retries))
    return s

def _put(d: Dict[str, Any], key: str, val: Any, *, bool_as_str=False, join_list=False) -> None:
    """Utility to add a key only when a value is present, with optional conversions."""
    if val is None:
        return
    if bool_as_str:
        val = str(bool(val)).lower()
    if join_list:
        val = ",".join(val)
    d[key] = val

def vi_multisearch(
    *,
    # ---- auth ----
    app_key: Optional[str] = None,
    placement_id: Optional[str] = None,

    # ---- at least one of these must be provided ----
    q: Optional[str] = None,                  # QUERY PARAM
    im_url: Optional[str] = None,             # BODY
    im_id: Optional[str] = None,              # BODY
    image_path: Optional[str] = None,         # BODY (file)
    image_bytes: Optional[bytes] = None,      # BODY (file)
    pid: Optional[str] = None,                # BODY

    # ---- image upload details ----
    image_field_name: str = "image",

    # ---- query params (as per docs) ----
    box: Optional[str] = None,
    detection: Optional[str] = None,          # defaults to 'all' if omitted by API
    page: Optional[int] = None,
    limit: Optional[int] = None,
    filters: Optional[str] = None,
    facets: Optional[Iterable[str]] = None,
    facets_limit: Optional[int] = None,
    facets_show_count: Optional[bool] = None,
    attrs_to_get: Optional[Iterable[str]] = None,
    score: Optional[bool] = None,
    score_min: Optional[float] = None,
    similar_score_min: Optional[float] = None,
    va_uid: Optional[str] = None,
    va_sid: Optional[str] = None,
    return_fields_mapping: Optional[bool] = None,
    sayt: Optional[bool] = None,
    boosts: Optional[str] = None,
    spell_correction: Optional[bool] = None,
    locale: Optional[str] = None,

    # ---- body-only params (rules/controls) ----
    boost_attrs_v2: Optional[str] = None,
    cond_filters: Optional[str] = None,
    global_filter: Optional[str] = None,
    pin_hide: Optional[str] = None,
    syn_rules: Optional[str] = None,
    pairing_rules: Optional[str] = None,

    # ---- forward-compat passthroughs ----
    extra_query: Optional[Dict[str, Any]] = None,
    extra_body: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    ViSenze Product Multi Search (POST /v1/product/multisearch).

    Provide any one of: q, im_url, im_id, image_path/image_bytes, pid.
    If an image is supplied (path/bytes), the request is sent as multipart/form-data.
    Returns the parsed JSON dict; raises on non-2xx after retries.
    """
    # Validate inputs
    if not any([q, im_url, im_id, image_path, image_bytes, pid]):
        raise ValueError("One of im_url, im_id, image_path/image_bytes, q, or pid must be provided.")

    app_key, placement_id = _resolve_auth(app_key, placement_id)
    url = f"{VI_BASE}{MULTISEARCH_PATH}"

    # Query string
    params: Dict[str, Any] = {
        "app_key": app_key,
        "placement_id": placement_id,
    }
    _put(params, "q", q)
    _put(params, "box", box)
    _put(params, "detection", detection)
    _put(params, "page", page)
    _put(params, "limit", limit)
    _put(params, "filters", filters)
    _put(params, "facets", facets, join_list=True)
    _put(params, "facets_limit", facets_limit)
    _put(params, "facets_show_count", facets_show_count, bool_as_str=True)
    _put(params, "attrs_to_get", attrs_to_get, join_list=True)
    _put(params, "score", score, bool_as_str=True)
    _put(params, "score_min", score_min)
    _put(params, "similar_score_min", similar_score_min)
    _put(params, "va_uid", va_uid)
    _put(params, "va_sid", va_sid)
    _put(params, "return_fields_mapping", return_fields_mapping, bool_as_str=True)
    _put(params, "sayt", sayt, bool_as_str=True)
    _put(params, "boosts", boosts)
    _put(params, "spell_correction", spell_correction, bool_as_str=True)
    _put(params, "locale", locale)

    if extra_query:
        for k, v in extra_query.items():
            _put(params, k, v)

    # Body (form fields)
    data: Dict[str, Any] = {}
    _put(data, "im_url", im_url)
    _put(data, "im_id", im_id)
    _put(data, "pid", pid)
    _put(data, "boost_attrs_v2", boost_attrs_v2)
    _put(data, "cond_filters", cond_filters)
    _put(data, "global_filter", global_filter)
    _put(data, "pin_hide", pin_hide)
    _put(data, "syn_rules", syn_rules)
    _put(data, "pairing_rules", pairing_rules)

    if extra_body:
        for k, v in extra_body.items():
            _put(data, k, v)

    s = _session()

    # Handle image upload (multipart) if supplied
    if image_bytes is not None:
        files = {image_field_name: ("upload.jpg", image_bytes, "image/jpeg")}
        resp = s.post(url, params=params, data=data, files=files, timeout=60)
        resp.raise_for_status()
        return resp.json()

    if image_path is not None:
        with open(image_path, "rb") as f:
            files = {image_field_name: (os.path.basename(image_path), f, "application/octet-stream")}
            resp = s.post(url, params=params, data=data, files=files, timeout=60)
            resp.raise_for_status()
            return resp.json()

    # No image → regular form (x-www-form-urlencoded)
    resp = s.post(url, params=params, data=data, timeout=60)
    resp.raise_for_status()
    return resp.json()
    