"""
DateTime context service for the <PERSON><PERSON>by agent.
Provides time-aware information for greetings and seasonal fashion recommendations.
"""
import logging
from datetime import datetime, timezone
from typing import Dict, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TimeContext:
    """Time context information for the agent."""
    current_time: datetime
    hour: int
    minute: int
    day_of_week: str
    day_name: str
    month: str
    season: str
    is_weekend: bool
    is_business_hours: bool
    greeting_style: str
    fashion_context: str


class DateTimeContextService:
    """
    Service for providing time-aware context to the agent.
    Handles greetings, seasonal fashion tips, and time-based recommendations.
    """
    
    def __init__(self, timezone_name: str = "UTC"):
        """
        initialise the datetime context service.
        
        Args:
            timezone_name: Timezone to use for context (defaults to UTC)
        """
        self.timezone_name = timezone_name
        logger.info(f"DateTime context service initialised with timezone: {timezone_name}")
    
    def get_current_time_context(self) -> TimeContext:
        """
        Get current time context information.
        
        Returns:
            TimeContext object with current time information
        """
        now = datetime.now(timezone.utc)
        
        # Extract time components
        hour = now.hour
        minute = now.minute
        day_of_week = now.strftime("%A")  # Full day name
        day_name = now.strftime("%a")      # Abbreviated day name
        month = now.strftime("%B")         # Full month name
        
        # Determine season
        season = self._get_season(now.month)
        
        # Determine if it's weekend
        is_weekend = now.weekday() >= 5  # Saturday = 5, Sunday = 6
        
        # Determine if it's business hours (9 AM - 6 PM)
        is_business_hours = 9 <= hour < 18
        
        # Determine greeting style based on time
        greeting_style = self._get_greeting_style(hour)
        
        # Get fashion context based on season and time
        fashion_context = self._get_fashion_context(season, hour, is_weekend)
        
        return TimeContext(
            current_time=now,
            hour=hour,
            minute=minute,
            day_of_week=day_of_week,
            day_name=day_name,
            month=month,
            season=season,
            is_weekend=is_weekend,
            is_business_hours=is_business_hours,
            greeting_style=greeting_style,
            fashion_context=fashion_context
        )
    
    def _get_season(self, month: int) -> str:
        """
        Determine the season based on month number.
        
        Args:
            month (int): Month number (1-12)
            
        Returns:
            str: Season name (winter, spring, summer, autumn)
        """
        if month in [12, 1, 2]:
            return "winter"
        elif month in [3, 4, 5]:
            return "spring"
        elif month in [6, 7, 8]:
            return "summer"
        else:  # 9, 10, 11
            return "autumn"
    
    def _get_greeting_style(self, hour: int) -> str:
        """
        Determine greeting style based on time of day.
        
        Args:
            hour (int): Hour of the day (0-23)
            
        Returns:
            str: Greeting style (morning, afternoon, evening, night)
        """
        if 5 <= hour < 12:
            return "morning"
        elif 12 <= hour < 17:
            return "afternoon"
        elif 17 <= hour < 21:
            return "evening"
        else:  # 21-24 or 0-5
            return "night"
    
    def _get_fashion_context(self, season: str, hour: int, is_weekend: bool) -> str:
        """
        Get fashion context based on season, time, and day type.
        
        Args:
            season (str): Current season (winter, spring, summer, autumn)
            hour (int): Hour of the day (0-23)
            is_weekend (bool): Whether it's a weekend day
            
        Returns:
            str: Fashion context description combining seasonal and time-based styling advice
        """
        contexts = []
        
        # Seasonal context
        if season == "winter":
            contexts.append("winter fashion with warm layers, cosy jumpers, and stylish coats")
        elif season == "spring":
            contexts.append("spring fashion with light layers, pastel colours, and transitional pieces")
        elif season == "summer":
            contexts.append("summer fashion with breathable fabrics, bright colours, and lightweight styles")
        else:  # autumn
            contexts.append("autumn fashion with rich colours, textured fabrics, and layered looks")
        
        # Time-based context
        if 9 <= hour < 18:
            contexts.append("perfect for workwear and professional styling")
        elif 18 <= hour < 22:
            contexts.append("great for evening wear and going out outfits")
        else:
            contexts.append("ideal for comfortable loungewear and cosy pieces")
        
        # Weekend vs weekday context
        if is_weekend:
            contexts.append("weekend casual and relaxed styling")
        else:
            contexts.append("weekday professional and polished looks")
        
        return ". ".join(contexts)
    
    def get_time_aware_greeting(self, user_name: str = None, style: str = "natural") -> str:
        """
        Generate a time-aware greeting based on current context.
        
        Args:
            user_name: Optional user name for personalized greeting
            style: Greeting style - "natural" (simple) or "detailed" (with fashion context)
            
        Returns:
            Time-appropriate greeting message
        """
        context = self.get_current_time_context()
        
        # Base greeting based on time of day
        if context.greeting_style == "morning":
            greeting = "Good morning"
        elif context.greeting_style == "afternoon":
            greeting = "Good afternoon"
        elif context.greeting_style == "evening":
            greeting = "Good evening"
        else:  # night
            greeting = "Hello"
        
        # Add personalization
        if user_name:
            greeting += f" {user_name}"
        
        # Add day context
        if context.is_weekend:
            greeting += f", happy {context.day_of_week}!"
        else:
            greeting += f", happy {context.day_of_week}!"
        
        # Add seasonal fashion note only if detailed style is requested
        if style == "detailed":
            greeting += f" It's {context.season} time, so {context.fashion_context}."
        
        return greeting
    
    def get_simple_greeting(self, user_name: str = None) -> str:
        """
        Generate a simple, natural time-aware greeting.
        
        Args:
            user_name: Optional user name for personalized greeting
            
        Returns:
            Simple time-appropriate greeting message
        """
        context = self.get_current_time_context()
        
        # Simple greeting based on time of day
        if context.greeting_style == "morning":
            greeting = "Good morning"
        elif context.greeting_style == "afternoon":
            greeting = "Good afternoon"
        elif context.greeting_style == "evening":
            greeting = "Good evening"
        else:  # night
            greeting = "Hello"
        
        # Add personalization
        if user_name:
            greeting += f" {user_name}"
        
        # Add day context and welcome message
        greeting += f", happy {context.day_of_week} and welcome to Shoeby! What can I assist you with today?"
        
        return greeting
    
    def get_seasonal_fashion_tips(self) -> Dict[str, Any]:
        """
        Get seasonal fashion tips and recommendations.
        
        Returns:
            Dictionary with seasonal fashion advice
        """
        context = self.get_current_time_context()
        
        tips = {
            "season": context.season,
            "current_month": context.month,
            "general_tips": [],
            "product_recommendations": [],
            "styling_advice": []
        }
        
        if context.season == "winter":
            tips["general_tips"] = [
                "Layer up with cosy knits and warm accessories",
                "Invest in a quality winter coat that goes with everything",
                "Don't forget stylish scarves and gloves for warmth"
            ]
            tips["product_recommendations"] = [
                "Wool jumpers and cardigans",
                "Warm coats and jackets",
                "Thermal underwear and base layers",
                "Stylish boots and warm footwear"
            ]
            tips["styling_advice"] = [
                "Mix textures like wool, cashmere, and leather",
                "Use dark colours as a base and add pops of colour",
                "Accessorize with warm, cosy pieces"
            ]
            
        elif context.season == "spring":
            tips["general_tips"] = [
                "Transition from winter layers to lighter pieces",
                "Embrace pastel colours and floral patterns",
                "Invest in versatile jackets for unpredictable weather"
            ]
            tips["product_recommendations"] = [
                "Lightweight jackets and blazers",
                "Pastel-coloured tops and dresses",
                "Transitional footwear like ankle boots",
                "Light scarves and accessories"
            ]
            tips["styling_advice"] = [
                "Layer light pieces for changing temperatures",
                "Mix winter and spring pieces during transition",
                "Add fresh, bright colours to your wardrobe"
            ]
            
        elif context.season == "summer":
            tips["general_tips"] = [
                "Choose breathable, lightweight fabrics",
                "Go for bright, vibrant colours",
                "Keep accessories minimal and practical"
            ]
            tips["product_recommendations"] = [
                "Cotton and linen dresses",
                "Lightweight tops and shorts",
                "Comfortable sandals and espadrilles",
                "Wide-brimmed hats and sunglasses"
            ]
            tips["styling_advice"] = [
                "Focus on comfort and breathability",
                "Use bright colours and bold patterns",
                "Keep outfits simple and easy to wear"
            ]
            
        else:  # autumn
            tips["general_tips"] = [
                "Embrace rich, warm colours and textures",
                "Layer pieces for changing temperatures",
                "Invest in versatile autumn staples"
            ]
            tips["product_recommendations"] = [
                "Rich-coloured jumpers and cardigans",
                "Textured jackets and coats",
                "Ankle boots and closed-toe shoes",
                "Warm accessories like scarves and hats"
            ]
            tips["styling_advice"] = [
                "Mix rich colours like burgundy, mustard, and forest green",
                "Layer different textures and weights",
                "Transition from summer to winter pieces"
            ]
        
        # Add time-specific advice
        if context.is_business_hours:
            tips["general_tips"].append("Perfect time for workwear shopping and professional styling")
        elif context.is_weekend:
            tips["general_tips"].append("Great time to explore casual and weekend styles")
        
        return tips
    
    def get_context_summary(self) -> str:
        """
        Get a summary of current time context for the agent.
        
        Returns:
            String summary of current context
        """
        context = self.get_current_time_context()
        
        summary = f"Current time: {context.current_time.strftime('%A, %B %d at %I:%M %p')}. "
        summary += f"It's {context.season} and {context.greeting_style} time. "
        
        if context.is_weekend:
            summary += "Weekend casual styling is perfect right now. "
        else:
            summary += "Weekday professional looks are ideal. "
        
        summary += f"Fashion context: {context.fashion_context}"
        
        return summary
