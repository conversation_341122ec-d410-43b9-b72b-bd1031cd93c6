"""
Prompts package for the <PERSON><PERSON>by agent.
Contains all prompt templates used by the strategy and tools.
"""

from .strategy import get_strategy_prompt
from .conversation import get_followup_query_prompt
from .context import get_context_extraction_prompt
from .query_construction import (
    get_weaviate_query_construction_prompt,
    get_weaviate_query_reconstruction_prompt
)

from .presentation import (
    get_results_presentation_prompt,
    get_product_details_presentation_prompt,
    get_complementary_products_presentation_prompt,
    get_multi_tool_presentation_prompt
)

from .purchase import get_complementary_products_prompt
from .complementary_products import (
    get_complementary_products_search_prompt,
    get_complementary_products_presentation_prompt as get_complementary_products_presentation_prompt_new
)
from .basket import get_basket_info_prompt

__all__ = [
    'get_strategy_prompt',
    'get_followup_query_prompt',
    'get_context_extraction_prompt',
    'get_weaviate_query_construction_prompt',
    'get_results_presentation_prompt',
    'get_product_details_presentation_prompt',
    'get_complementary_products_presentation_prompt',
    'get_multi_tool_presentation_prompt',
    'get_weaviate_query_reconstruction_prompt',

    'get_complementary_products_prompt',
    'get_complementary_products_search_prompt',
    'get_complementary_products_presentation_prompt_new',
    'get_basket_info_prompt',
]
