<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Frontend Test</title>
    <style>
        body { 
            margin: 0; 
            padding: 20px; 
            font-family: -apple-system, BlinkMacSystemFont, sans-serif; 
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Frontend Test Page</h1>
        <p>This page tests basic frontend functionality to isolate the issue.</p>
        
        <h3>Basic JavaScript Test</h3>
        <button class="test-button" onclick="testBasicJS()">Test Basic JavaScript</button>
        <div id="basic-js-result" class="result"></div>
        
        <h3>API Connection Test</h3>
        <button class="test-button" onclick="testAPIConnection()">Test API Connection</button>
        <div id="api-result" class="result"></div>
        
        <h3>React App Test</h3>
        <button class="test-button" onclick="testReactApp()">Test React App</button>
        <div id="react-result" class="result"></div>
        
        <h3>Console Log Test</h3>
        <button class="test-button" onclick="testConsoleLog()">Test Console Log</button>
        <div id="console-result" class="result"></div>
    </div>

    <script>
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        function testBasicJS() {
            try {
                const testArray = [1, 2, 3];
                const doubled = testArray.map(x => x * 2);
                showResult('basic-js-result', `Basic JS working: ${doubled.join(', ')}`);
            } catch (error) {
                showResult('basic-js-result', `Basic JS error: ${error.message}`, true);
            }
        }

        async function testAPIConnection() {
            try {
                const response = await fetch('http://localhost:8000/api/health');
                const data = await response.json();
                showResult('api-result', `API working: ${JSON.stringify(data)}`);
            } catch (error) {
                showResult('api-result', `API error: ${error.message}`, true);
            }
        }

        function testReactApp() {
            try {
                // Check if React is loaded
                if (typeof React !== 'undefined') {
                    showResult('react-result', 'React is loaded successfully');
                } else {
                    showResult('react-result', 'React is not loaded', true);
                }
            } catch (error) {
                showResult('react-result', `React test error: ${error.message}`, true);
            }
        }

        function testConsoleLog() {
            try {
                console.log('Test console log message');
                console.error('Test console error message');
                console.warn('Test console warning message');
                showResult('console-result', 'Console logging working - check browser console');
            } catch (error) {
                showResult('console-result', `Console test error: ${error.message}`, true);
            }
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            testBasicJS();
            testConsoleLog();
        });
    </script>
</body>
</html>
