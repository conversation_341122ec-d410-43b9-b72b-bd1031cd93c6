"""
Chat and session-related Pydantic models.
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1, max_length=2000)
    session_id: Optional[str] = None
    user_id: Optional[str] = "demo_user"


class ChatResponse(BaseModel):
    response: str
    session_id: str
    user_id: str
    timestamp: float
    provider: Optional[str] = None
    reasoning: Optional[Dict[str, Any]] = None


class SessionCreateRequest(BaseModel):
    user_id: Optional[str] = "demo_user"


class SessionCreateResponse(BaseModel):
    session_id: str
    user_id: str
    timestamp: float


class HistoryMessage(BaseModel):
    role: str
    content: str
    timestamp: float


class HistoryResponse(BaseModel):
    session_id: str
    messages: List[HistoryMessage]
    total_messages: int
