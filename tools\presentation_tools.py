"""
Presentation tools for the <PERSON><PERSON>by agent.
These tools format and present information to users using LLM services.
"""
import logging

from .base_tool import <PERSON><PERSON><PERSON>
from typing import Dict, Any, List
from services.llm_service import LLMService

logger = logging.getLogger(__name__)


class PresentProductsTool(BaseTool):
    """Tool for presenting search results to customers based on context"""
    
    def __init__(self, llm_service: LLMService):
        super().__init__(
            name="present_results",
            description="Present search results to customers in an engaging and helpful way"
        )
        self.llm_service = llm_service
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the presentation tool"""
        try:
            # Extract parameters
            customer_requirements = params.get("customer_requirements", "")
            focused_instructions = params.get("focused_instructions", "")
            context = params.get("context", {})
            
            # Check if this is a multi-tool presentation
            is_multi_tool = context.get("is_multi_tool_presentation", False)
            executed_tools = context.get("executed_tools", [])
            tool_results = context.get("tool_results", {})
            
            # Check if we have product details context (from product_details agent)
            product_details = context.get("current_product_details")
            presentation_intent = context.get("presentation_intent")
            if not presentation_intent:
                try:
                    tp = context.get("tool_parameters", {})
                    sp = tp.get("search_products", {}) if isinstance(tp, dict) else {}
                    presentation_intent = sp.get("customer_requirements")
                except Exception:
                    presentation_intent = None
            
            # Establish common context strings used across presentation branches
            user_query = presentation_intent or context.get("user_input", "product search")
            conversation_history = context.get("conversation_history", "")

            if is_multi_tool and executed_tools and tool_results:
                # Multi-tool presentation - combine outputs from multiple tools
                logger.info(f"PresentProductsTool: Multi-tool presentation for: {executed_tools}")
                response_text = await self._present_multi_tool_results(
                    executed_tools, tool_results, context, user_query, conversation_history
                )
                logger.info(f"PresentProductsTool: Generated multi-tool response: {response_text[:100]}...")
            elif product_details:
                # Use product details presentation via LLM service
                logger.info("PresentProductsTool: Using product details presentation via LLM")
                
                # Convert product details to the format expected by present_search_results
                product = product_details["product"]
                
                # Handle both Product objects and dictionaries
                if hasattr(product, 'title'):
                    # Product object
                    results_dict = [{
                        "title": product.title,
                        "brand": product.brand,
                        "price": product.price,
                        "colour": product.colour,
                        "size": getattr(product, 'size_display', product.size),
                        "category": product.category,
                        "url": product.product_url,
                        "available_sizes": getattr(product, 'available_sizes', [product.size] if product.size else []),
                        "has_multiple_sizes": len(getattr(product, 'available_sizes', [product.size] if product.size else [])) > 1
                    }]
                else:
                    # Product dictionary
                    results_dict = [{
                        "title": product.get('title', 'Unknown'),
                        "brand": product.get('brand', 'Unknown'),
                        "price": product.get('price', 'N/A'),
                        "colour": product.get('colour', ''),
                        "size": product.get('size_display', product.get('size', '')),
                        "category": product.get('category', ''),
                        "url": product.get('url', product.get('product_url', '')),
                        "available_sizes": product.get('available_sizes', [product.get('size', '')] if product.get('size') else []),
                        "has_multiple_sizes": len(product.get('available_sizes', [product.get('size', '')] if product.get('size') else [])) > 1
                    }]
                
                # Get context for LLM presentation
                user_query = presentation_intent or context.get("user_input", "product details request")
                conversation_history = context.get("conversation_history", "")
                
                # Use LLM service to present product details
                response_text = await self.llm_service.present_search_results(
                    results_dict, user_query, conversation_history
                )
                
                # Add size-specific information if available
                if hasattr(product, 'has_multiple_sizes'):
                    # Product object
                    if getattr(product, 'has_multiple_sizes', False):
                        size_info = f"\n\n📏 **Size Options**: {getattr(product, 'size_display', product.size)}"
                        response_text += size_info
                        logger.info(f"PresentProductsTool: Added size info: {size_info}")
                    elif getattr(product, 'available_sizes', []):
                        size_info = f"\n\n📏 **Size**: {getattr(product, 'size_display', product.size)}"
                        response_text += size_info
                        logger.info(f"PresentProductsTool: Added single size info: {size_info}")
                else:
                    # Product dictionary
                    if product.get('has_multiple_sizes'):
                        size_info = f"\n\n📏 **Size Options**: {product.get('size_display', '')}"
                        response_text += size_info
                        logger.info(f"PresentProductsTool: Added size info: {size_info}")
                    elif product.get('available_sizes'):
                        size_info = f"\n\n📏 **Size**: {product.get('size_display', '')}"
                        response_text += size_info
                        logger.info(f"PresentProductsTool: Added single size info: {size_info}")
                
                logger.info(f"PresentProductsTool: Generated response text via LLM: {response_text[:100]}...")
                logger.info(f"PresentProductsTool: Final response with size info: {response_text}")
            else:
                # Prioritize current search results over enhanced results to ensure price accuracy
                search_results = context.get("current_search_results", [])
                enhanced_results = context.get("enhanced_search_results")
                
                # Use current search results if available, otherwise fall back to enhanced results
                if search_results:
                    logger.info("PresentProductsTool: Using current search results presentation")
                    logger.info(f"PresentProductsTool: Current search results count: {len(search_results)}")
                    logger.info(f"PresentProductsTool: First current result: {search_results[0] if search_results else 'None'}")
                    
                    user_query = presentation_intent or context.get("user_input", "product search")
                    conversation_history = context.get("conversation_history", "")
                    
                    response_text = await self._present_single_product_results(search_results, context, user_query, focused_instructions)
                        
                elif enhanced_results:
                    logger.info("PresentProductsTool: Using enhanced search results presentation (fallback)")
                    logger.info(f"PresentProductsTool: Enhanced results count: {len(enhanced_results)}")
                    logger.info(f"PresentProductsTool: First enhanced result: {enhanced_results[0] if enhanced_results else 'None'}")
                    
                    user_query = presentation_intent or context.get("user_input", "product search")
                    conversation_history = context.get("conversation_history", "")
                    
                    response_text = self._present_single_product_results(enhanced_results, context, user_query, focused_instructions)
                else:
                    logger.warning("PresentProductsTool: No search results found in context")
                    logger.warning(f"PresentProductsTool: Available context keys: {list(context.keys())}")
                    logger.warning(f"PresentProductsTool: current_search_results: {context.get('current_search_results', 'NOT_FOUND')}")
                    logger.warning(f"PresentProductsTool: enhanced_search_results: {context.get('enhanced_search_results', 'NOT_FOUND')}")
                    return {
                        "success": False,
                        "error": "No search results to present",
                        "message": "Search results not found"
                    }
                
                logger.info(f"PresentProductsTool: Generated response text: {response_text[:100]}...")
            
            return {
                "success": True,
                "response_text": response_text,
                "message": "Results presented successfully"
            }
            
        except Exception as e:
            logger.error(f"PresentProductsTool: Error presenting results: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Error presenting results"
            }
    
    
    async def _present_single_product_results(self, search_results: List, context: Dict[str, Any], user_query: str, focused_instructions: str) -> str:
        """Present results from single product searches."""
        # Use enhanced results with available sizes if available, otherwise convert products
        if context.get("enhanced_search_results"):
            results_dict = context["enhanced_search_results"]
            logger.info("PresentProductsTool: Using enhanced results with available sizes for single product")
            logger.info(f"PresentProductsTool: Enhanced results contain available_sizes: {[r.get('available_sizes', []) for r in results_dict]}")
        else:
            # Fallback to converting products manually
            results_dict = []
            for product in search_results:
                # Handle both object attributes and dictionary keys
                if hasattr(product, 'title'):
                    # Product object
                    results_dict.append({
                        "title": product.title,
                        "brand": product.brand,
                        "price": product.sale_price,  # Use sale_price for consistency with search filtering
                        "colour": product.colour,
                        "size": product.size,
                        "category": product.category,
                        "url": product.product_url,
                        "available_sizes": getattr(product, 'available_sizes', [product.size] if product.size else []),
                        "has_multiple_sizes": len(getattr(product, 'available_sizes', [product.size] if product.size else [])) > 1
                    })
                else:
                    # Product dictionary
                    results_dict.append({
                        "title": product.get('title', 'Unknown'),
                        "brand": product.get('brand', 'Unknown'),
                        "price": product.get('price', 'N/A'),
                        "colour": product.get('colour', ''),
                        "size": product.get('size', ''),
                        "category": product.get('category', ''),
                        "url": product.get('url', product.get('product_url', '')),
                        "available_sizes": product.get('available_sizes', [product.get('size', '')] if product.get('size') else []),
                        "has_multiple_sizes": len(product.get('available_sizes', [product.get('size', '')] if product.get('size') else [])) > 1
                    })
            logger.info(f"PresentProductsTool: Fallback to manual conversion, available_sizes: {[r.get('available_sizes', []) for r in results_dict]}")
        
        # This helper is sync; caller is async 'execute'. Use already awaited path in execute.
        # Fall back to async present for consistency.
        conversation_history = context.get("conversation_history", "")
        return await self.llm_service.present_search_results(
            results_dict, user_query, conversation_history
        )
    
    async def _present_multi_tool_results(
        self, 
        executed_tools: List[str], 
        tool_results: Dict[str, Any], 
        context: Dict[str, Any],
        user_query: str, 
        ) -> str:
        """Present results from multiple tool executions in a coherent, comprehensive way."""
        try:
            from prompts.presentation import get_multi_tool_presentation_prompt
            
            # Create a comprehensive prompt for multi-tool presentation
            focused_instructions = context.get("focused_instructions", "")
            multi_tool_prompt = get_multi_tool_presentation_prompt(
                executed_tools=executed_tools,
                tool_results=tool_results,
                user_query=user_query,
                focused_instructions=focused_instructions
            )
            print(f"get_multi_tool_presentation_prompt: {get_multi_tool_presentation_prompt}")
            
            # Use LLM to generate the combined response
            response_text = await self.llm_service.generate_response(multi_tool_prompt)
            
            return response_text
            
        except Exception as e:
            logger.error(f"PresentProductsTool: Error in multi-tool presentation: {e}")
            # Fallback: combine results manually
            fallback_response = f"I've completed your request! "
            for tool_name in executed_tools:
                tool_result = tool_results.get(tool_name, {})
                if tool_result.get("success"):
                    fallback_response += f"{tool_name} completed successfully. "
            return fallback_response
    
    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema for the LLM"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "customer_requirements": {
                        "type": "string",
                        "description": "The customer's requirements for the search"
                    },
                    "focused_instructions": {
                        "type": "string",
                        "description": "Focused, relevant instructions for the current turn"
                    },
                    "context": {
                        "type": "object",
                        "description": "Context containing search results and other relevant information"
                    }
                },
                "required": ["customer_requirements", "focused_instructions", "context"]
            }
        }
