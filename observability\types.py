from typing import Any, Callable, List, Literal, TypedDict, TypeVar

R = TypeVar("R")
T = TypeVar("T")
TraceableFunction = TypeVar("TraceableFunction", bound=Callable[..., Any])

ProviderName = Literal["langfuse", "langsmith", "otel"]
PROVIDER_LANGFUSE: ProviderName = "langfuse"
PROVIDER_LANGSMITH: ProviderName = "langsmith"
PROVIDER_OTEL: ProviderName = "otel"
PROVIDER_NULL: ProviderName = "null"


class ProviderFunctions(TypedDict):
    name: ProviderName
    update_trace: Callable[[str, str, List[str], dict], bool]
    observe: Callable[[Callable[..., R]], Callable[..., R]]
    init: Callable[[], None]
