"""
Utility functions for applying merchant configuration to prompts.
"""
from typing import Dict, Any, Optional
from .merchant_config import get_merchant_config


def get_balance_config() -> Dict[str, Any]:
    """
    Get the current conversation balance configuration from merchant settings.
    
    Returns:
        Dict containing conversation balance configuration parameters
    """
    config = get_merchant_config()
    return {
        'follow_up_vs_products_ratio': config.conversation_balance.follow_up_vs_products_ratio,
        'max_follow_up_questions': config.conversation_balance.max_follow_up_questions,
        'follow_up_aggressiveness': config.conversation_balance.follow_up_aggressiveness,
        'products_per_response': config.conversation_balance.products_per_response,
        'show_product_details': config.conversation_balance.show_product_details,
        'include_styling_suggestions': config.conversation_balance.include_styling_suggestions,
        'prefer_conversation': config.conversation_balance.prefer_conversation
    }


def get_temperature_config() -> Dict[str, Any]:
    """
    Get the current temperature configuration from merchant settings.
    
    Returns:
        Dict containing temperature configuration parameters
    """
    config = get_merchant_config()
    return {
        'strategy_temperature': config.temperature.strategy_temperature,
        'search_temperature': config.temperature.search_temperature,
        'evaluation_temperature': config.temperature.evaluation_temperature,
        'presentation_temperature': config.temperature.presentation_temperature,
        'followup_temperature': config.temperature.followup_temperature,
        'complementary_temperature': config.temperature.complementary_temperature,
        'irrelevant_temperature': config.temperature.irrelevant_temperature,
        'purchase_temperature': config.temperature.purchase_temperature,
        'default_temperature': config.temperature.default_temperature,
        'min_temperature': config.temperature.min_temperature,
        'max_temperature': config.temperature.max_temperature
    }


def get_temperature_for_task(task_type: str) -> float:
    """
    Get the appropriate temperature for a specific task type.
    
    Args:
        task_type (str): The type of task being performed
        
    Returns:
        float: The temperature value for the task
    """
    config = get_merchant_config()
    
    # Map task types to temperature settings
    task_temperature_map = {
        'strategy': config.temperature.strategy_temperature,
        'search': config.temperature.search_temperature,
        'evaluation': config.temperature.evaluation_temperature,
        'presentation': config.temperature.presentation_temperature,
        'followup': config.temperature.followup_temperature,
        'complementary': config.temperature.complementary_temperature,
        'irrelevant': config.temperature.irrelevant_temperature,
        'purchase': config.temperature.purchase_temperature,
    }
    
    # Return task-specific temperature or default
    return task_temperature_map.get(task_type, config.temperature.default_temperature)


def apply_merchant_config_to_prompt(
    prompt_func,
    *args,
    balance_config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> str:
    """
    Apply merchant configuration to a prompt function.
    
    Args:
        prompt_func: The prompt function to call
        *args: Positional arguments for the prompt function
        balance_config: Optional balance configuration override
        **kwargs: Additional keyword arguments for the prompt function
        
    Returns:
        str: The generated prompt with merchant configuration applied
    """
    # Get default configurations if not provided
    if balance_config is None:
        balance_config = get_balance_config()
    
    # Apply configurations to kwargs
    kwargs['balance_config'] = balance_config
    
    # Call the prompt function with updated arguments
    return prompt_func(*args, **kwargs)
