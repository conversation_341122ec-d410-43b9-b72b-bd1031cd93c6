import logging
from typing import Any, Callable

from langfuse import get_client
from langfuse import observe as langfuse_observe

from ..types import ProviderFunctions


def _init() -> None:
    get_client()
    logging.info("Langfuse provider initialised.")


def _update_trace(
    *args: Any,
    **kwargs: Any,
) -> bool:
    try:
        langfuse_client = get_client()
        langfuse_client.update_current_trace(**kwargs)
        logging.debug("Trace context updated successfully with Langfuse.")
        return True
    except Exception as e:
        logging.error("Failed to update trace with Langfuse: %s", e, exc_info=True)
        return False


def _observe(
    func: Callable[..., Any],
    *args: Any,
    **kwargs: Any,
) -> Callable[..., Any]:

    return langfuse_observe(**kwargs)(func)


def get_provider_functions() -> ProviderFunctions:
    return {
        "update_trace": _update_trace,
        "observe": _observe,
        "name": "langfuse",
        "init": _init,
    }
