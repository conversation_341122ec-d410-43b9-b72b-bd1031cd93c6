// MongoDB initialisation script for Shoe<PERSON> Agent
// This script runs when the MongoDB container starts for the first time

// Switch to the admin database to create user
db = db.getSiblingDB('admin');

// Create the shoeby_conversations database
db = db.getSiblingDB('shoeby_conversations');

// Create a user for the application
db.createUser({
  user: 'shoeby_user',
  pwd: 'shoeby_password',
  roles: [
    {
      role: 'readWrite',
      db: 'shoeby_conversations'
    }
  ]
});

// Create collections with validation
db.createCollection('chat_history', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['user_id', 'session_id', 'role', 'content', 'timestamp'],
      properties: {
        user_id: {
          bsonType: 'string',
          description: 'User identifier - required'
        },
        session_id: {
          bsonType: 'string',
          description: 'Session identifier - required'
        },
        role: {
          enum: ['user', 'assistant'],
          description: 'Message role - required'
        },
        content: {
          bsonType: 'string',
          description: 'Message content - required'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Message timestamp - required'
        }
      }
    }
  }
});

// Create indexes for better performance
db.chat_history.createIndex({ 'user_id': 1, 'session_id': 1 });
db.chat_history.createIndex({ 'timestamp': -1 });
db.chat_history.createIndex({ 'session_id': 1, 'timestamp': 1 });

// Create search cache collection
db.createCollection('search_cache', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['session_id', 'query', 'results', 'timestamp'],
      properties: {
        session_id: {
          bsonType: 'string',
          description: 'Session identifier - required'
        },
        query: {
          bsonType: 'string',
          description: 'Search query - required'
        },
        results: {
          bsonType: 'array',
          description: 'Search results - required'
        },
        timestamp: {
          bsonType: 'date',
          description: 'Cache timestamp - required'
        }
      }
    }
  }
});

// Create indexes for search cache
db.search_cache.createIndex({ 'session_id': 1 });
db.search_cache.createIndex({ 'timestamp': 1 });

// Insert a welcome message
db.chat_history.insertOne({
  user_id: 'system',
  session_id: 'welcome',
  role: 'assistant',
  content: 'Welcome to Shoeby Agent! The database has been initialised successfully.',
  timestamp: new Date()
});

print('✅ MongoDB initialised successfully for Shoeby Agent');
print('📊 Database: shoeby_conversations');
print('👤 User: shoeby_user');
print('🔐 Password: shoeby_password');
print('📝 Collections: chat_history, search_cache');
print('📈 Indexes created for optimal performance');
