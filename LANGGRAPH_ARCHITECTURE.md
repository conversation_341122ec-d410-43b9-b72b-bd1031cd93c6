# LangGraph Architecture Documentation

## Overview

This codebase uses **LangGraph 0.0.40+** as the primary orchestration framework for the conversational agent system. LangGraph provides a graph-based state machine for robust agent coordination and state management.

## Graph Structure

The LangGraph implementation consists of **9 nodes** and **7 edges** (including conditional routing):

### Nodes

1. **`receive_input`** - Receives and processes user input
2. **`get_context`** - Extracts conversation context and time information  
3. **`decide_strategy`** - Determines the appropriate action strategy
4. **`execute_search`** - Performs product search operations
5. **`evaluate_products`** - Evaluates and processes search results
6. **`present_products`** - Formats and presents product information
7. **`handle_conversation`** - Manages conversational interactions and basket operations
8. **`generate_response`** - Generates the final response
9. **`handle_error`** - Handles errors and exceptions

### Edges

- **Linear flow:** `receive_input` → `get_context` → `decide_strategy`
- **Conditional routing** from `decide_strategy`:
  - `"search"` → `execute_search` → `evaluate_products` → `present_products` → `generate_response`
  - `"conversation"` or `"basket"` → `handle_conversation` → `generate_response`
  - `"error"` → `handle_error` → `generate_response`
- **Final step:** `generate_response` → `END`

## State Management

The LangGraph uses a comprehensive state object (`ShoebyState`) that includes:

### Core State Properties
- **Messages**: Conversation history with LangChain message types
- **Context**: User input, conversation history, focused context
- **Strategy**: Decision data and actions
- **Search Results**: Current and enhanced search results
- **Response**: Generated response and reasoning data
- **Error Handling**: Error messages and state

### State Definition
```python
class ShoebyState(TypedDict):
    """State definition for the Shoeby LangGraph workflow."""
    messages: Annotated[List[BaseMessage], add_messages]
    user_input: str
    conversation_history: str
    focused_context: str
    focused_instructions: str
    time_context: str
    strategy_decision: Dict[str, Any]
    actions: List[str]
    response: str
    reasoning_data: Dict[str, Any]
    error: str
    current_search_results: List[Any]
    enhanced_search_results: List[Any]
```

## Key Features

### 1. Conditional Routing
The `_route_strategy` function determines the workflow path based on the strategy decision:
- **Search operations**: Routes to product search workflow
- **Conversation/Basket operations**: Routes to conversation handler
- **Error conditions**: Routes to error handler

### 2. State Persistence
- Uses `add_messages` annotation for automatic message management
- Maintains conversation context across the entire workflow
- Preserves user preferences and session data

### 3. Error Handling
- Dedicated error handling node with graceful fallbacks
- Comprehensive error logging and recovery mechanisms
- State preservation during error conditions

### 4. Tool Integration
- Each node can access registered tools and agents
- Seamless integration with tool and agent registries
- Dynamic tool selection based on context

### 5. Async Operations
- All nodes are async functions for better performance
- Non-blocking operations throughout the workflow
- Efficient resource utilisation

## Architecture Benefits

### Graph-based Workflow
- Clear visual representation of the agent flow
- Easy to understand and debug
- Modular design with single responsibility per node

### State Management
- Robust state persistence across the workflow
- Automatic message handling with LangChain integration
- Comprehensive context preservation

### Error Recovery
- Built-in error handling and recovery mechanisms
- Graceful degradation on failures
- Comprehensive logging for debugging

### Tool Integration
- Seamless integration with tool and agent registries
- Dynamic tool selection based on context
- Centralised tool management

## Implementation Details

### Workflow Construction
```python
def _build_workflow(self) -> StateGraph:
    """Build the LangGraph workflow for agent orchestration."""
    workflow = StateGraph(ShoebyState)
    
    # Add nodes
    workflow.add_node("receive_input", self._receive_input)
    workflow.add_node("get_context", self._get_context)
    # ... other nodes
    
    # Define edges
    workflow.set_entry_point("receive_input")
    workflow.add_edge("receive_input", "get_context")
    # ... other edges
    
    # Conditional routing
    workflow.add_conditional_edges(
        "decide_strategy",
        self._route_strategy,
        {
            "search": "execute_search",
            "conversation": "handle_conversation",
            "basket": "handle_conversation",
            "error": "handle_error"
        }
    )
    
    return workflow.compile()
```

### Node Implementation Pattern
Each node follows a consistent pattern:
```python
async def _node_name(self, state: ShoebyState) -> ShoebyState:
    """Node description."""
    start_time = time.time()
    try:
        # Node logic here
        return {**state, "updated_field": value}
    except Exception as e:
        logger.error(f"Error in node_name: {e}")
        return {**state, "error": str(e)}
```

## Dependencies

- **LangGraph**: `>=0.0.40`
- **LangChain Core**: For message types and state management
- **Python 3.8+**: For async/await support

## Related Documentation

- [LangGraph Basket Fix](./LANGGRAPH_BASKET_FIX.md)
- [LangGraph Method Signature Fix](./LANGGRAPH_METHOD_SIGNATURE_FIX.md)
- [Framework Switching](./FRAMEWORK_SWITCHING.md)
