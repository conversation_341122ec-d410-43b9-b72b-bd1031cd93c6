import logging
from typing import Callable, Dict, Optional, Union

import httpx
from fastapi import FastAP<PERSON>

from ..core import get_observability_provider
from ..types import (
    PROVIDER_LANGFUSE,
    PROVIDER_NULL,
    PROVIDER_OTEL,
    ProviderName,
)

from config.settings import AgentConfig

settings = AgentConfig()

if settings.observability_provider_name == PROVIDER_LANGFUSE:
    from langfuse.openai import AsyncAzureOpenAI, AsyncOpenAI
else:
    from openai import AsyncAzureOpenAI, AsyncOpenAI


class CustomOpenAIClient(AsyncOpenAI):
    def __init__(self, *args, default_model=settings.custom_llm_model_name, **kwargs):
        super().__init__(*args, **kwargs)
        self.default_model = default_model
        self._wrap_create()

    def _wrap_create(self):
        original_create = self.chat.completions.create

        async def wrapped_create(*args, **kwargs):
            return await original_create(*args, **kwargs)

        self.chat.completions.create = wrapped_create


CLIENT_STRATEGIES: Dict[ProviderName, Callable] = {
    PROVIDER_LANGFUSE: AsyncAzureOpenAI,
    PROVIDER_OTEL: AsyncAzureOpenAI,
    PROVIDER_NULL: AsyncAzureOpenAI,
}

CUSTOM_CLIENT_STRATEGIES: Dict[ProviderName, Callable] = {
    PROVIDER_LANGFUSE: CustomOpenAIClient,
    PROVIDER_OTEL: CustomOpenAIClient,
    PROVIDER_NULL: CustomOpenAIClient,
}


def get_client_params() -> Dict[str, str]:
    return {
        "api_key": settings.brainpowa_api_key,
        "base_url": settings.brainpowa_api_url,
        "default_headers": {"X-API-Key": settings.brainpowa_api_key},
    }


def get_azure_client_params() -> Dict[str, str]:
    return {
        "api_key": settings.openai_api_azure_key,
        "api_version": settings.openai_api_azure_version,
        "azure_endpoint": settings.openai_api_azure_endpoint,
    }


def get_configured_provider_name() -> Optional[str]:
    try:
        provider = get_observability_provider()
        return provider["name"]
    except Exception as e:
        logging.warning("Failed to get configured provider: %s", e)
        return None


def get_client_class(
    client_stategies: Dict[ProviderName, Callable],
    provider_name: Optional[ProviderName],
) -> Callable:
    if not provider_name:
        return AsyncOpenAI

    client_class = client_stategies.get(provider_name)
    if not client_class:
        logging.info(
            f"Provider {provider_name} not supported for OpenAI client, using default",
        )
        return AsyncOpenAI

    return client_class


def create_client(
    client_stategies: Dict[ProviderName, Callable],
    client_params: Dict[str, str],
    http_client: httpx.AsyncClient,
    provider_name: Optional[ProviderName] = None,
) -> Union[AsyncOpenAI, AsyncAzureOpenAI]:
    if provider_name is None:
        provider_name = get_configured_provider_name()

    client_class = get_client_class(client_stategies, provider_name)

    # Always add http_client to params
    client_params["http_client"] = http_client

    return client_class(**client_params)


def init_openai(
    app: FastAPI,
    with_tracing: bool = True,
    provider_name: Optional[ProviderName] = None,
) -> None:
    """initialise the OpenAI client on a FastAPI application.

    Args:
        app: The FastAPI application to initialise the client on.
        with_tracing: Whether to enable tracing with the configured provider.
        provider_name: Optional specific provider to use for tracing.
        If None and with_tracing is True, uses the globally configured provider.
    """
    # Create HTTP/2 clients with 5-minute timeout
    azure_http_client = httpx.AsyncClient(
        http2=True,
        timeout=300.0,
    )

    custom_http_client = httpx.AsyncClient(
        http2=True,
        timeout=300.0,
    )

    if not with_tracing:
        azure_params = get_azure_client_params()
        azure_params["http_client"] = azure_http_client
        app.state.openai_client = AsyncAzureOpenAI(**azure_params)

        custom_params = get_client_params()
        custom_params["http_client"] = custom_http_client
        app.state.custom_llm_client = CustomOpenAIClient(**custom_params)

        logging.info("initialised standard OpenAI clients with HTTP/2")
        return

    if not provider_name:
        provider_name = get_configured_provider_name()

    app.state.openai_client = create_client(
        CLIENT_STRATEGIES,
        get_azure_client_params(),
        azure_http_client,
        provider_name,
    )
    app.state.custom_llm_client = create_client(
        CUSTOM_CLIENT_STRATEGIES,
        get_client_params(),
        custom_http_client,
        provider_name,
    )
    logging.info(
        f"initialised LLM clients with HTTP/2 and tracing provider: {provider_name}",
    )
