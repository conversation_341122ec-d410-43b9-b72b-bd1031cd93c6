"""
Basket-related prompts for the <PERSON><PERSON>by agent.
Handles basket operations and dynamic responses about basket updates.
"""


def get_basket_info_prompt(
    action: str, 
    product_name: str, 
    focused_instructions: str, 
    basket_summary: str, 
    previous_summary: str
    ) -> str:
    """
    Generate a prompt for providing dynamic responses about basket operations.
    
    Args:
        action (str): The action performed on the basket
        product_name (str): The product involved in the action
        focused_instructions (str): Focused, relevant instructions for the current turn
        basket_summary (str): Current basket state summary
        previous_summary (str): Previous basket state summary
        
    Returns:
        str: Formatted prompt for basket info response
    """
    return f"""
    You are a helpful shopping assistant responding to a customer's basket update.

    Instructions: {focused_instructions}

    Action performed: {action}
    Product involved: {product_name if product_name else "N/A"}

    Previous basket state:
    {previous_summary}

    Current basket state:
    {basket_summary}

    Generate a helpful, friendly response that:
    - Acknowledges the action taken
    - Provides relevant information about the basket state
    - Offers helpful next steps or suggestions
    - Maintains a warm, professional tone
    - Is concise but informative

    Response should be 1-3 sentences, friendly and helpful.
    """


def get_update_basket_confirmation_prompt(
    action: str,
    product_name: str,
    size: str = "",
    quantity: int = 1,
    focused_instructions: str = ""
) -> str:
    """
    Generate a simple confirmation prompt for basket update operations.
    
    Args:
        action (str): The action being performed (add, remove, update_quantity, clear)
        product_name (str): The product involved in the action
        size (str): The size of the product (optional)
        quantity (int): The quantity involved (default: 1)
        focused_instructions (str): Focused, relevant instructions for the current turn
        
    Returns:
        str: Formatted prompt for basket update confirmation
    """
    size_info = f" (size {size})" if size else ""
    quantity_info = f" x{quantity}" if quantity > 1 else ""
    
    return f"""
    You are a helpful shopping assistant confirming a basket update.

    Instructions: {focused_instructions}

    Action: {action}
    Product: {product_name}{size_info}{quantity_info}

    Generate a brief, friendly confirmation message that:
    - Acknowledges the specific action taken
    - Mentions the product and any relevant details (size, quantity)
    - Uses a warm, professional tone
    - Is concise and clear

    Response should be 1-2 sentences, friendly and direct.
    """
