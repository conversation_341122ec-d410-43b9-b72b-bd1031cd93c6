"""
Merchant-configurable settings for the Shoeby agent.
Contains configurable parameters for conversation balance and temperature settings.
"""
import json
import os
from typing import Dict, Any
from dataclasses import dataclass, asdict


@dataclass
class ConversationBalanceConfig:
    """Configuration for balancing follow-up questions vs product presentation."""
    
    # Primary balance setting
    follow_up_vs_products_ratio: str = "follow_up_heavy"  # Options: follow_up_heavy, balanced, product_heavy
    
    # Follow-up question settings
    max_follow_up_questions: int = 3
    follow_up_aggressiveness: str = "medium"  # Options: low, medium, high
    
    # Product presentation settings
    products_per_response: int = 5
    show_product_details: bool = True
    include_styling_suggestions: bool = True
    
    # Conversation flow
    prefer_conversation: bool = True  # Whether to prioritise conversation over immediate product search


@dataclass
class TemperatureConfig:
    """Configuration for LLM temperature settings for different task types."""
    
    # Task-specific temperature settings
    strategy_temperature: float = 1      # Strategy decisions
    search_temperature: float = 0.4        # Query construction (more focused)
    evaluation_temperature: float = 0.3    # Result evaluation (very focused)
    presentation_temperature: float = 0.9   # Product presentation (creative)
    followup_temperature: float = 1      # Follow-up questions (conversational)
    complementary_temperature: float = 0.8  # Complementary products
    irrelevant_temperature: float = 0.9    # Irrelevant query handling
    purchase_temperature: float = 0.7      # Purchase-related tasks
    
    # Default temperature for unspecified tasks
    default_temperature: float = 1
    
    # Temperature range validation
    min_temperature: float = 0.0
    max_temperature: float = 2.0


@dataclass
class MerchantConfig:
    """Main merchant configuration combining all configurable aspects."""
    
    conversation_balance: ConversationBalanceConfig = None
    temperature: TemperatureConfig = None
    
    def __post_init__(self):
        """Set default configurations if none provided."""
        if self.conversation_balance is None:
            self.conversation_balance = ConversationBalanceConfig()
        if self.temperature is None:
            self.temperature = TemperatureConfig()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for easy serialisation."""
        return asdict(self)
    
    def save_to_file(self, filepath: str = "merchant_config.json") -> None:
        """Save configuration to a JSON file."""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load_from_file(cls, filepath: str = "merchant_config.json") -> 'MerchantConfig':
        """Load configuration from a JSON file."""
        if not os.path.exists(filepath):
            return cls()
        
        with open(filepath, 'r') as f:
            config_data = json.load(f)
        
        # Handle legacy config files that might have tonality config
        if 'tonality' in config_data:
            del config_data['tonality']
        
        # Reconstruct nested dataclass objects
        if 'conversation_balance' in config_data:
            config_data['conversation_balance'] = ConversationBalanceConfig(**config_data['conversation_balance'])
        if 'temperature' in config_data:
            config_data['temperature'] = TemperatureConfig(**config_data['temperature'])
        
        return cls(**config_data)
    
    @classmethod
    def get_default(cls) -> 'MerchantConfig':
        """Get a default configuration instance."""
        return cls()


def get_merchant_config() -> MerchantConfig:
    """Get the current merchant configuration, loading from file if available."""
    return MerchantConfig.load_from_file()
