import logging
import threading
from typing import Callable, Dict, Optional

from .types import (
    PROVIDER_LANGFUSE,
    PROVIDER_NULL,
    PROVIDER_OTEL,
    ProviderFunctions,
    ProviderName,
)


def _load_langfuse_provider() -> ProviderFunctions:
    # Lazily import to avoid hard dependency when not used
    from .providers import langfuse_provider

    return langfuse_provider.get_provider_functions()


def _load_otel_provider() -> ProviderFunctions:
    from .providers import otel_provider

    return otel_provider.get_provider_functions()


def _load_null_provider() -> ProviderFunctions:
    from .providers import null_provider

    return null_provider.get_provider_functions()


PROVIDER_LOADERS: Dict[ProviderName, Callable[[], ProviderFunctions]] = {
    PROVIDER_LANGFUSE: _load_langfuse_provider,
    PROVIDER_OTEL: _load_otel_provider,
    PROVIDER_NULL: _load_null_provider,
}


class ObservabilityConfig:
    """
    Thread-safe singleton for observability provider management.
    Uses double-checked locking pattern for thread safety.
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        if not hasattr(self, "_initialised"):
            self._provider: Optional[ProviderFunctions] = None
            self._loaded_providers: Dict[ProviderName, ProviderFunctions] = {}
            self._inner_lock = threading.RLock()
            self._is_configured = False
            self._initialised = True

    def _get_or_load(self, provider_name: ProviderName) -> ProviderFunctions:
        if provider_name in self._loaded_providers:
            return self._loaded_providers[provider_name]

        if provider_name not in PROVIDER_LOADERS:
            logging.error("Unknown observability provider: %s", provider_name)
            raise ValueError(f"Unknown observability provider: {provider_name}")

        try:
            provider = PROVIDER_LOADERS[provider_name]()
            self._loaded_providers[provider_name] = provider
            return provider
        except ModuleNotFoundError as e:
            logging.error(
                "Provider '%s' requires missing dependency: %s",
                provider_name,
                e,
            )
            raise
        except Exception as e:
            logging.error("Failed to load provider '%s': %s", provider_name, e)
            raise

    def set_provider(self, provider_name: ProviderName) -> None:
        with self._inner_lock:
            if self._is_configured:
                raise RuntimeError(
                    "Observability provider already configured. Cannot reconfigure.",
                )

            self._provider = self._get_or_load(provider_name)
            self._is_configured = True

            logging.info(
                f"Observability configured with provider: {provider_name}",
            )

    def get_provider(
        self,
        provider_name: Optional[ProviderName] = None,
    ) -> ProviderFunctions:
        if provider_name:
            return self._get_or_load(provider_name)

        if not self._provider:
            logging.info(
                f"No provider configured {self._provider}, using default: {PROVIDER_OTEL}",
            )
            return self._get_or_load(PROVIDER_OTEL)

        return self._provider


observability_config = ObservabilityConfig()
