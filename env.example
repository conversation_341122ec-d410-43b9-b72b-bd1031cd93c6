# LLM Provider Configuration
# Get your API keys from the respective providers
BRAINPOWA_API_KEY=your_brainpowa_api_key_here
BRAINPOWA_API_URL=https://brainpowa-model-garden.dev.az.rezolve.com/v1
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
# Update these with your actual database connection details
MONGODB_URL=****************************************************************
MONGODB_DATABASE=shoeby_conversations
MONGODB_COLLECTION=chat_history

# Visenze Search Configuration
# Get your API keys and placement IDs from Visenze
VISENZE_APP_KEY=your_visenze_app_key_here
VISENZE_PLACEMENT_ID=your_visenze_placement_id_here
VISENZE_PLACEMENT_ID_SEARCH=your_visenze_search_placement_id_here
VISENZE_PLACEMENT_ID_RECS=your_visenze_recommendations_placement_id_here

# Weaviate Configuration (legacy - can be removed if not using Weaviate)
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8080
WEAVIATE_GRPC_PORT=50051
WEAVIATE_API_KEY=your_weaviate_api_key_here
WEAVIATE_COLLECTION=ShoebyAgenticStoreProducts

# Optional: Custom Settings
TIMEZONE=UTC
USER_ID=your_user_id

# Framework is now fixed to LangGraph - no configuration needed
DEFAULT_MODEL_PROVIDER=openai

# Note: Copy this file to .env and fill in your actual values
# Never commit the .env file to version control

# Observability (OpenTelemetry)
# Set provider to 'otel' to enable OpenTelemetry tracing
OBSERVABILITY_PROVIDER_NAME=otel

# OTLP HTTP endpoint of your collector (default shown)
OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318

# Logical service name to appear in traces
OTEL_SERVICE_NAME=shoeby-agent

# Optional: Enable specific instrumentations
# Set non-empty value (e.g., '1' or 'true') to enable
OTEL_WEAVIATE_TRACING=1
OTEL_mongo_TRACING=1

# Optional: Limit size of string attributes on spans (in characters)
OTEL_TRACE_STRING_LIMIT=1000

# Optional: Custom tag list for trace attributes
OTEL_TRACE_TAGS=merchant
