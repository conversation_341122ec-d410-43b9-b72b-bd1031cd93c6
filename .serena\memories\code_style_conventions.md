# Code Style and Conventions

## Python Code Style

### General Conventions
- **Python Version**: 3.11+ required
- **Type Hints**: Used extensively throughout the codebase
- **Async/Await**: Heavy use of async patterns for I/O operations
- **PEP 8 Compliance**: Standard Python formatting

### Naming Conventions
- **Classes**: PascalCase (e.g., `BaseAgent`, `ShoebyAgent`)
- **Functions/Methods**: snake_case (e.g., `process_query_with_reasoning`)
- **Private Methods**: Leading underscore (e.g., `_show_orchestrator_info`)
- **Constants**: UPPER_SNAKE_CASE
- **Module Names**: snake_case

### Import Style
- Standard library imports first
- Third-party imports second
- Local application imports last
- Grouped and separated by blank lines

### Class Structure Pattern
```python
class BaseAgent(ABC):
    def __init__(self, name: str, description: str, required_params: List[str]):
        self.name = name
        self.description = description
        self.required_params = required_params
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        pass
```

### Documentation
- Docstrings not consistently present but type hints are used
- Method signatures include type hints for parameters and return values
- Async methods properly marked with `async def`

### Error Handling
- Try-except blocks used for error handling
- Custom exceptions where appropriate
- Logging for debugging

### Dependency Injection
- Services injected through constructors
- Configuration passed as Config objects
- Registry pattern for agents and tools

## Frontend Code Style
- **React 18** with functional components
- **ES6+** JavaScript/JSX
- **Vite** for bundling and development