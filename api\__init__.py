"""
Pydantic models for the Shoeby Agent API.

This package contains all request/response models organized by domain.
"""

from .routers.chat import (
    ChatRequest,
    ChatResponse,
    SessionCreateRequest,
    SessionCreateResponse,
    HistoryMessage,
    HistoryResponse,
)
from .routers.basket import (
    BasketItem,
    BasketResponse,
    AddToBasketRequest,
    RemoveFromBasketRequest,
    UpdateQuantityRequest,
)

__all__ = [
    # Chat models
    "ChatRequest",
    "ChatResponse",
    "SessionCreateRequest",
    "SessionCreateResponse",
    "HistoryMessage",
    "HistoryResponse",
    # Basket models
    "BasketItem",
    "BasketResponse",
    "AddToBasketRequest",
    "RemoveFromBasketRequest",
    "UpdateQuantityRequest",
    # Common models
]
