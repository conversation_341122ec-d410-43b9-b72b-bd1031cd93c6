"""
Product details tool for the <PERSON><PERSON><PERSON> agent.
This tool chains cache retrieval and presentation to show detailed product information.
"""
import logging
from typing import Dict, Any, List

from .base_tool import BaseTool
from services.mongodb_utils.session_cache import SessionCacheService

logger = logging.getLogger(__name__)


class ProductDetailsTool(BaseTool):
    """Tool for retrieving detailed product information from MongoDB cache"""
    
    def __init__(self, session_cache_service: SessionCacheService):
        super().__init__(
            name="get_product_details",
            description="Retrieve detailed product information from cache"
        )
        self.session_cache_service = session_cache_service
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the product details tool - retrieves product data from MongoDB cache.
        
        Args:
            params: Dictionary containing:
                - product_name (str): Name of the product to retrieve
                - session_id (str): Current session identifier  
                - user_id (str): User identifier
                - context (dict): Additional context (optional)
        
        Returns:
            Dict containing success status, product details, and any error messages
        """
        try:
            product_name = params.get("product_name", "")
            session_id = params.get("session_id", "")
            user_id = params.get("user_id", "")
            context = params.get("context", {})
            
            if not product_name:
                return {
                    "success": False,
                    "error": "Product name is required",
                    "message": "No product name specified for details retrieval"
                }
            
            if not session_id or not user_id:
                return {
                    "success": False,
                    "error": "Session ID and user ID are required",
                    "message": "Session context is required for cache lookup"
                }
            
            # Retrieve product details from cache
            try:
                product_details = await self.session_cache_service.get_cached_product(
                    session_id=session_id,
                    user_id=user_id,
                    product_name=product_name
                )
                
                if not product_details:
                    return {
                        "success": False,
                        "error": "Product not found in cache",
                        "message": f"Could not find product details for '{product_name}' in current session"
                    }
                
                # Convert CachedProduct to dictionary for consistent return format
                product_data = product_details.to_dict()
                
                return {
                    "success": True,
                    "product_details": product_data,
                    "message": f"Retrieved product details for '{product_details.title}'"
                }
                
            except Exception as e:
                logger.error(f"Error retrieving product from cache: {e}")
                return {
                    "success": False,
                    "error": f"Cache retrieval failed: {str(e)}",
                    "message": "Could not retrieve product from cache"
                }
            
        except Exception as e:
            logger.error(f"Error in product details tool: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Error retrieving product details"
            }
    
    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema for the LLM"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "product_name": {
                        "type": "string",
                        "description": "Name of the product to get details for"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "Current session identifier"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "User identifier"
                    },
                    "context": {
                        "type": "object",
                        "description": "Additional context (optional)"
                    }
                },
                "required": ["product_name", "session_id", "user_id"]
            }
        }
