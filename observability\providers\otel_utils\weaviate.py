from opentelemetry.instrumentation import weaviate as weaviate_instrumentation

CUSTOM_WEAVIATE_WRAPPED_METHODS = [
    {
        "module": "weaviate.collections.collections",
        "object": "_Collections",
        "method": "get",
        "span_name": "db.weaviate.collections.get",
    },
    {
        "module": "weaviate.collections.collections",
        "object": "_Collections",
        "method": "create",
        "span_name": "db.weaviate.collections.create",
    },
    {
        "module": "weaviate.collections.collections",
        "object": "_Collections",
        "method": "create_from_dict",
        "span_name": "db.weaviate.collections.create_from_dict",
    },
    {
        "module": "weaviate.collections.collections",
        "object": "_Collections",
        "method": "delete",
        "span_name": "db.weaviate.collections.delete",
    },
    {
        "module": "weaviate.collections.collections",
        "object": "_Collections",
        "method": "delete_all",
        "span_name": "db.weaviate.collections.delete_all",
    },
    {
        "module": "weaviate.collections.data",
        "object": "_DataCollection",
        "method": "insert",
        "span_name": "db.weaviate.collections.data.insert",
    },
    {
        "module": "weaviate.collections.data",
        "object": "_DataCollection",
        "method": "replace",
        "span_name": "db.weaviate.collections.data.replace",
    },
    {
        "module": "weaviate.collections.data",
        "object": "_DataCollection",
        "method": "update",
        "span_name": "db.weaviate.collections.data.update",
    },
    {
        "module": "weaviate.collections.batch.collection",
        "object": "_BatchCollection",
        "method": "add_object",
        "span_name": "db.weaviate.collections.batch.add_object",
    },
    {
        "module": "weaviate.collections.queries.fetch_object_by_id.query",
        "object": "_FetchObjectByIDQuery",
        "method": "fetch_object_by_id",
        "span_name": "db.weaviate.collections.query.fetch_object_by_id",
    },
    {
        "module": "weaviate.collections.queries.fetch_objects.query",
        "object": "_FetchObjectsQuery",
        "method": "fetch_objects",
        "span_name": "db.weaviate.collections.query.fetch_objects",
    },
    {
        "module": "weaviate.collections.queries.hybrid.query",
        "object": "_HybridQueryAsync",
        "method": "hybrid",
        "span_name": "db.weaviate.collections.query.hybrid",
    },
    {
        "module": "weaviate.collections.queries.bm25.query",
        "object": "_BM25QueryAsync",
        "method": "bm25",
        "span_name": "db.weaviate.collections.query.bm25",
    },
    {
        "module": "weaviate.collections.queries.near_text.query",
        "object": "_NearTextQueryAsync",
        "method": "near_text",
        "span_name": "db.weaviate.collections.query.near_text",
    },
]

weaviate_instrumentation.WRAPPED_METHODS = CUSTOM_WEAVIATE_WRAPPED_METHODS
