import React, { useState, useRef, useEffect, Component } from 'react'
import axios from 'axios'

// Error Boundary Component
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '2rem',
          textAlign: 'center',
          background: '#f8d7da',
          border: '1px solid #dc3545',
          borderRadius: '8px',
          margin: '1rem',
          color: '#721c24'
        }}>
          <h2>Something went wrong</h2>
          <p>The app encountered an error and needs to be refreshed.</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              padding: '0.5rem 1rem',
              background: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh Page
          </button>
          <details style={{ marginTop: '1rem', textAlign: 'left' }}>
            <summary>Error Details</summary>
            <pre style={{ 
              background: 'white', 
              padding: '1rem', 
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '0.8rem'
            }}>
              {this.state.error?.toString()}
            </pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

// Configure API
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000'
const API_KEY = import.meta.env.VITE_API_KEY || 'demo-key'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  }
})

function App() {
  const [messages, setMessages] = useState([
    { role: 'assistant', content: 'Loading...', timestamp: Date.now() }
  ])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [sessionId, setSessionId] = useState(null)
  const [currentProvider, setCurrentProvider] = useState('brainpowa')
  const [basketItems, setBasketItems] = useState(0)
  const [showSearchMessage, setShowSearchMessage] = useState(false)
  const [processingMessage, setProcessingMessage] = useState('')
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)

    // Enhanced link renderer with product name extraction
  const renderMessageWithLinks = (content) => {
    try {
      if (!content || typeof content !== 'string') {
        console.warn('renderMessageWithLinks received invalid content:', content)
        return content || 'No content available'
      }
      
      // Sanitize content to prevent XSS
      const sanitizedContent = content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      
      // Improved regex to handle markdown links and exclude trailing punctuation
      const urlRegex = /https?:\/\/[^\s\)\]\}]+/g;
      
      // Also handle markdown-style links [text](url)
      const markdownLinkRegex = /\[([^\]]+)\]\((https?:\/\/[^\)]+)\)/g;
      
      // First, replace markdown links
      let processedContent = sanitizedContent.replace(markdownLinkRegex, (match, linkText, url) => {
        return `MARKDOWN_LINK_PLACEHOLDER:${linkText}|${url}`;
      });
      
      // Then handle regular URLs
      const parts = processedContent.split(urlRegex);
      const urls = processedContent.match(urlRegex) || [];
      
      let urlIndex = 0;
      const result = [];
      
      for (let i = 0; i < parts.length; i++) {
        // Handle markdown link placeholders
        if (parts[i].includes('MARKDOWN_LINK_PLACEHOLDER:')) {
          const placeholders = parts[i].split(/(MARKDOWN_LINK_PLACEHOLDER:[^|]+\|[^\s]+)/);
          placeholders.forEach(part => {
            if (part.startsWith('MARKDOWN_LINK_PLACEHOLDER:')) {
              const [linkText, url] = part.replace('MARKDOWN_LINK_PLACEHOLDER:', '').split('|');
              result.push(
                <a 
                  key={`markdown-${result.length}`}
                  href={url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  style={{
                    color: '#007bff',
                    textDecoration: 'none',
                    display: 'inline-block',
                    margin: '4px 2px',
                    padding: '6px 12px',
                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                    borderRadius: '8px',
                    fontSize: '0.9em',
                    border: '1px solid #2196f3',
                    transition: 'all 0.2s ease',
                    fontWeight: '500'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = 'linear-gradient(135deg, #bbdefb 0%, #90caf9 100%)'
                    e.target.style.transform = 'translateY(-1px)'
                    e.target.style.boxShadow = '0 4px 8px rgba(33, 150, 243, 0.3)'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)'
                    e.target.style.transform = 'translateY(0)'
                    e.target.style.boxShadow = 'none'
                  }}
                >
                  🔗 {linkText}
                </a>
              );
            } else {
              result.push(part);
            }
          });
        } else {
          result.push(parts[i]);
          
          // Add regular URL if exists
          if (urlIndex < urls.length) {
            const url = urls[urlIndex].replace(/[\)\]\}]*$/, ''); // Remove trailing punctuation
            const productMatch = url.match(/shoeby-([^\/]+)/);
            const linkText = productMatch ? 
              `🔗 ${productMatch[1].replace(/-/g, ' ').substring(0, 25)}...` : 
              '🔗 View Product';
            
            result.push(
              <a 
                key={`url-${urlIndex}`}
                href={url} 
                target="_blank" 
                rel="noopener noreferrer"
                style={{
                  color: '#007bff',
                  textDecoration: 'none',
                  display: 'inline-block',
                  margin: '4px 2px',
                  padding: '6px 12px',
                  background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                  borderRadius: '8px',
                  fontSize: '0.9em',
                  border: '1px solid #2196f3',
                  transition: 'all 0.2s ease',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = 'linear-gradient(135deg, #bbdefb 0%, #90caf9 100%)'
                  e.target.style.transform = 'translateY(-1px)'
                  e.target.style.boxShadow = '0 4px 8px rgba(33, 150, 243, 0.3)'
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)'
                  e.target.style.transform = 'translateY(0)'
                  e.target.style.boxShadow = 'none'
                }}
              >
                {linkText}
              </a>
            );
            urlIndex++;
          }
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error in renderMessageWithLinks:', error)
      return content || 'Error rendering message'
    }
  };

  // Reasoning dropdown component with seconds display
  const ReasoningDropdown = ({ reasoning }) => {
    const [isOpen, setIsOpen] = useState(false)
    
    if (!reasoning) return null
    
    try {
      return (
        <div style={{ marginTop: '0.5rem' }}>
          <button
            onClick={() => setIsOpen(!isOpen)}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '20px',
              padding: '0.5rem 1rem',
              cursor: 'pointer',
              fontSize: '0.8rem',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            {isOpen ? '🔽' : '🔼'} Show LLM Reasoning ({formatDuration(reasoning.total_duration_ms)}s)
          </button>
          
          {isOpen && (
            <div style={{
              background: '#f8f9fa',
              border: '1px solid #dee2e6',
              borderRadius: '8px',
              padding: '1rem',
              marginTop: '0.5rem',
              fontSize: '0.9rem'
            }}>
              <div style={{ marginBottom: '1rem' }}>
                <strong>Provider:</strong> {reasoning.provider ? reasoning.provider.toUpperCase() : 'Unknown'}
              </div>
              
              <div style={{ marginBottom: '1rem' }}>
                <strong>Total Processing Time:</strong> {formatDuration(reasoning.total_duration_ms)}s
              </div>
              
              {reasoning.summary?.strategy_reasoning && (
                <div style={{ marginBottom: '1rem' }}>
                  <strong>Strategy:</strong> {reasoning.summary.strategy_reasoning}
                </div>
              )}
              
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Processing Steps:</strong>
              </div>
              
              {reasoning.steps && reasoning.steps.map((step, index) => (
                <div key={index} style={{ 
                  marginLeft: '1rem', 
                  marginBottom: '0.25rem',
                  padding: '0.25rem',
                  background: 'white',
                  borderRadius: '4px',
                  border: '1px solid #dee2e6'
                }}>
                  <div style={{ fontWeight: 'bold', color: '#007bff' }}>
                    {index + 1}. {step.step?.replace('_', ' ')?.toUpperCase() || 'Unknown Step'} 
                    {step.duration_ms && (
                      <span style={{ fontWeight: 'normal', color: '#6c757d' }}>
                        ({formatDuration(step.duration_ms)}s)
                      </span>
                    )}
                  </div>
                  <div style={{ marginTop: '0.25rem' }}>
                    {step.explanation || 'No explanation available'}
                  </div>
                  {step.actions && (
                    <div style={{ marginTop: '0.25rem', color: '#6c757d' }}>
                      Actions: {Array.isArray(step.actions) ? step.actions.join(', ') : 'No actions'}
                    </div>
                  )}
                  {step.decision && (
                    <div style={{ marginTop: '0.25rem', color: '#6c757d', fontSize: '0.75rem' }}>
                      Decision: {JSON.stringify(step.decision.next_best_actions || step.decision, null, 2)}
                    </div>
                  )}
                </div>
              ))}
              
              {reasoning.error && (
                <div style={{ 
                  color: '#dc3545', 
                  background: '#f8d7da', 
                  padding: '0.5rem', 
                  borderRadius: '4px',
                  marginTop: '0.5rem'
                }}>
                  <strong>Error:</strong> {reasoning.error}
                </div>
              )}
            </div>
          )}
        </div>
      )
    } catch (error) {
      console.error('Error rendering ReasoningDropdown:', error)
      return (
        <div style={{ 
          marginTop: '0.5rem',
          color: '#dc3545', 
          background: '#f8d7da', 
          padding: '0.5rem', 
          borderRadius: '4px',
          fontSize: '0.8rem'
        }}>
          <strong>Error displaying reasoning:</strong> {error.message}
        </div>
      )
    }
  }

  // Format duration from milliseconds to seconds
  const formatDuration = (ms) => {
    if (!ms || typeof ms !== 'number') return '0.0';
    return (ms / 1000).toFixed(1);
  };

  // Auto-resize textarea function
  const adjustTextareaHeight = (textarea) => {
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';
      
      // Calculate new height (min 44px, max 120px for about 4 lines)
      const newHeight = Math.max(44, Math.min(textarea.scrollHeight, 120));
      textarea.style.height = newHeight + 'px';
      
      // Auto-scroll to bottom if content exceeds max height
      if (textarea.scrollHeight > 120) {
        textarea.scrollTop = textarea.scrollHeight;
      }
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    // Create session on startup
    createSession()
    // Load providers
    loadProviders()
    // Load basket
    loadBasket()
    // Focus input on startup
    setTimeout(() => {
      inputRef.current?.focus()
    }, 500)
  }, [])

  // Auto-resize textarea when input changes
  useEffect(() => {
    if (inputRef.current) {
      adjustTextareaHeight(inputRef.current)
    }
  }, [input])

  const getInitialGreeting = () => {
    const now = new Date()
    const hour = now.getHours()
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    const dayName = dayNames[now.getDay()]
    
    // Generate greeting based on time of day
    let timeGreeting
    if (hour >= 5 && hour < 12) {
      timeGreeting = 'Good morning'
    } else if (hour >= 12 && hour < 17) {
      timeGreeting = 'Good afternoon'
    } else if (hour >= 17 && hour < 21) {
      timeGreeting = 'Good evening'
    } else {
      timeGreeting = 'Hello'
    }
    
    // Create expressive greeting with Shoeby branding
    const greeting = `${timeGreeting}, happy ${dayName} and welcome to Shoeby! What can I assist you with today?`
    
    setMessages([{
      role: 'assistant',
      content: greeting,
      timestamp: Date.now()
    }])
  }

  const createSession = async () => {
    try {
      const response = await api.post('/api/sessions/new', { user_id: 'web_user' })
      setSessionId(response.data.session_id)
      // Get initial greeting after session is created
      setTimeout(() => getInitialGreeting(), 200)
    } catch (error) {
      console.error('Failed to create session:', error)
      setSessionId('fallback-session-id-' + Date.now())
      // Still try to get greeting even with fallback session
      setTimeout(() => getInitialGreeting(), 200)
    }
  }

  const loadProviders = async () => {
    try {
      const response = await api.get('/api/providers')
      // Make sure we store lowercase provider name
      setCurrentProvider(response.data.current_provider.toLowerCase())
    } catch (error) {
      console.error('Failed to load providers:', error)
    }
  }

  const loadBasket = async () => {
    try {
      const response = await api.get('/api/basket')
      setBasketItems(response.data.total_items)
    } catch (error) {
      console.error('Failed to load basket:', error)
    }
  }

  const sendMessage = async (e) => {
    e.preventDefault()
    if (!input.trim() || isLoading) return

    const userMessage = { role: 'user', content: input.trim(), timestamp: Date.now() }
    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsLoading(true)

    // Show generic processing message immediately
    setProcessingMessage('🤔 Thinking...')
    setShowSearchMessage(false)

    try {
      console.log('Sending message to API...', { message: userMessage.content, sessionId, userId: 'web_user' })
      
      const response = await api.post('/api/chat', {
        message: userMessage.content,
        session_id: sessionId,
        user_id: 'web_user'
      })

      console.log('API response received:', response.data)

      // Check if the agent decided to search for products
      const willSearchProducts = response.data.reasoning?.steps?.some(step => 
        step.decision?.next_best_actions?.includes('search_products')
      );

      if (willSearchProducts) {
        // Replace generic message with search-specific message
        setProcessingMessage('')
        setShowSearchMessage(true)
        
        // Keep search message visible for a moment to show the transition
        setTimeout(() => {
          setShowSearchMessage(false)
        }, 2000)
      } else {
        // Hide processing message if no search was triggered
        setProcessingMessage('')
      }

      const assistantMessage = {
        role: 'assistant',
        content: response.data.response || 'No response content received',
        timestamp: Date.now(),
        provider: response.data.provider || 'unknown',
        reasoning: response.data.reasoning || null
      }

      console.log('Created assistant message:', assistantMessage)
      setMessages(prev => [...prev, assistantMessage])
      setCurrentProvider(response.data.provider || 'unknown')
      
      // Refresh basket after each interaction
      loadBasket()

    } catch (error) {
      console.error('Chat error:', error)
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      })
      
      // Hide all messages on error
      setProcessingMessage('')
      setShowSearchMessage(false)
      
      const errorMessage = {
        role: 'assistant',
        content: `Sorry, I encountered an issue: ${error.response?.data?.detail || error.message || 'Unknown error'}. Please try again.`,
        timestamp: Date.now(),
        error: true
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
      // Auto-focus the input after message is sent
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }

  const switchProvider = async (provider) => {
    try {
      // Ensure we're sending lowercase to the API
      const providerLower = provider.toLowerCase()
      
      await api.post('/api/providers/switch', { provider: providerLower })
      setCurrentProvider(providerLower)  // Store lowercase in state
      
      // Use proper display names for the message
      const displayName = providerLower === 'brainpowa' ? 'BrainPowa' : 
                         provider.toUpperCase()
      
      setMessages(prev => [...prev, {
        role: 'system',
        content: `Switched to ${displayName} provider`,
        timestamp: Date.now()
      }])
    } catch (error) {
      console.error('Provider switch error:', error)
    }
  }

  const clearHistory = async () => {
    try {
      await api.delete(`/api/sessions/${sessionId}/history?user_id=web_user`)
      // Get a fresh greeting using the same logic
      getInitialGreeting()
    } catch (error) {
      console.error('Clear history error:', error)
    }
  }

  return (
    <ErrorBoundary>
      <div style={{ display: 'flex', flexDirection: 'column', height: '100vh', maxWidth: '800px', margin: '0 auto', border: '1px solid #e1e5e9', borderRadius: '8px', overflow: 'hidden' }}>
        {/* Header */}
        <div style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white', padding: '1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ margin: 0, fontSize: '1.5rem' }}>🛍️ Shoeby Fashion Assistant</h1>
            <p style={{ margin: '0.25rem 0 0 0', opacity: 0.9, fontSize: '0.9rem' }}>
              AI Provider: {currentProvider === 'brainpowa' ? 'BrainPowa' : 
                           currentProvider.toUpperCase()} | Basket: {basketItems} items
            </p>
          </div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <select 
              value={currentProvider} 
              onChange={(e) => switchProvider(e.target.value)}
              style={{ padding: '0.25rem', borderRadius: '4px', border: 'none' }}
            >
              <option value="brainpowa">BrainPowa</option>
            </select>
            <button 
              onClick={clearHistory}
              style={{ padding: '0.25rem 0.5rem', borderRadius: '4px', border: 'none', background: 'rgba(255,255,255,0.2)', color: 'white', cursor: 'pointer' }}
            >
              Clear
            </button>
          </div>
        </div>

        {/* Messages */}
        <div style={{ flex: 1, padding: '1rem', overflowY: 'auto', background: '#f8f9fa' }}>
          {messages.map((msg, index) => (
            <div key={index} style={{ marginBottom: '1rem', display: 'flex', justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start' }}>
              <div style={{
                maxWidth: '70%',
                padding: '0.75rem 1rem',
                borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
                background: msg.role === 'user' ? '#007bff' : msg.role === 'system' ? '#6c757d' : msg.error ? '#dc3545' : 'white',
                color: msg.role === 'user' || msg.role === 'system' || msg.error ? 'white' : '#333',
                boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                wordWrap: 'break-word'
              }}>
                {/* Enhanced message content with clickable links */}
                <div style={{ whiteSpace: 'pre-wrap' }}>
                  {renderMessageWithLinks(msg.content)}
                </div>
                {msg.provider && (
                  <div style={{ fontSize: '0.7rem', opacity: 0.7, marginTop: '0.25rem' }}>
                    via {msg.provider}
                  </div>
                )}
                
                {/* Add reasoning dropdown for assistant messages */}
                {msg.role === 'assistant' && msg.reasoning && (
                  <ReasoningDropdown reasoning={msg.reasoning} />
                )}
              </div>
            </div>
          ))}
          {isLoading && (
            <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '1rem' }}>
              <div style={{ background: 'white', padding: '0.75rem 1rem', borderRadius: '18px 18px 18px 4px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
                <div style={{ display: 'flex', gap: '4px' }}>
                  <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#ccc', animation: 'bounce 1.4s infinite ease-in-out' }}></div>
                  <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#ccc', animation: 'bounce 1.4s infinite ease-in-out 0.16s' }}></div>
                  <div style={{ width: '8px', height: '8px', borderRadius: '50%', background: '#ccc', animation: 'bounce 1.4s infinite ease-in-out 0.32s' }}></div>
                </div>
              </div>
            </div>
          )}
          
          {/* Processing Message */}
          {processingMessage && (
            <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '1rem' }}>
              <div style={{
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                border: '2px solid #6c757d',
                borderRadius: '18px',
                padding: '1rem 1.5rem',
                boxShadow: '0 2px 8px rgba(108, 117, 125, 0.2)',
                maxWidth: '70%',
                animation: 'slideIn 0.3s ease-out'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: '#495057',
                  fontWeight: 'bold',
                  fontSize: '1.1rem'
                }}>
                  {processingMessage}
                </div>
              </div>
            </div>
          )}
          
          {/* Search Products Pop-up Message */}
          {showSearchMessage && (
            <div style={{ display: 'flex', justifyContent: 'flex-start', marginBottom: '1rem' }}>
              <div style={{
                background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                border: '2px solid #2196f3',
                borderRadius: '18px',
                padding: '1rem 1.5rem',
                boxShadow: '0 4px 12px rgba(33, 150, 243, 0.2)',
                maxWidth: '70%',
                animation: 'slideIn 0.3s ease-out'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: '#1565c0',
                  fontWeight: 'bold',
                  fontSize: '1.1rem'
                }}>
                  🔍 Let me just check what we have in stock...
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Auto-expanding Input */}
        <form onSubmit={sendMessage} style={{ padding: '1rem', background: 'white', borderTop: '1px solid #e1e5e9' }}>
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'flex-end' }}>
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => {
                setInput(e.target.value)
                adjustTextareaHeight(e.target) // Auto-resize on change
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  sendMessage(e)
                }
              }}
              placeholder="Ask me about fashion, shoes, clothing..."
              disabled={isLoading}
              rows={1}
              style={{
                flex: 1,
                padding: '0.75rem',
                border: '2px solid #e1e5e9',
                borderRadius: '20px',
                outline: 'none',
                fontSize: '1rem',
                fontFamily: 'inherit',
                resize: 'none',
                minHeight: '44px',
                maxHeight: '120px',
                overflowY: 'auto',
                lineHeight: '1.4',
                transition: 'border-color 0.2s ease'
              }}
              onFocus={(e) => e.target.style.borderColor = '#007bff'}
              onBlur={(e) => e.target.style.borderColor = '#e1e5e9'}
            />
            <button
              type="submit"
              disabled={!input.trim() || isLoading}
              style={{
                padding: '0.75rem 1.5rem',
                background: input.trim() && !isLoading ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#ccc',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                cursor: input.trim() && !isLoading ? 'pointer' : 'not-allowed',
                fontSize: '1rem',
                fontWeight: '500',
                minWidth: '60px',
                height: '44px', // Fixed height to align with textarea
                flexShrink: 0
              }}
            >
              {isLoading ? '⏳' : '📤'}
            </button>
          </div>
          
          {/* Keyboard shortcut hint */}
          <div style={{ fontSize: '0.75rem', color: '#666', marginTop: '0.25rem', textAlign: 'center', opacity: 0.7 }}>
            Press Enter to send, Shift+Enter for new line
          </div>
        </form>

        <style jsx>{`
          @keyframes bounce {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
          }
          @keyframes slideIn {
            from {
              opacity: 0;
              transform: translateY(-10px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}</style>
      </div>
    </ErrorBoundary>
  )
}

export default App
