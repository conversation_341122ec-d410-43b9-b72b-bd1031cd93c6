"""
Chat, session management, and provider switching router for the Shoeby Agent API.
"""

import uuid
import time
import logging
from fastapi import APIRouter, HTTPException, Depends, status, Query

from observability.core import observe_trace, update_trace

from ..dependencies import get_agent
from ..models import (
    ChatRequest,
    ChatResponse,
    SessionCreateRequest,
    SessionCreateResponse,
    HistoryMessage,
    HistoryResponse,
    ProviderSwitchRequest,
    ProviderSwitchResponse,
)
from main import ShoebyAgent
from services.model_providers import ModelProvider

router = APIRouter()
logger = logging.getLogger(__name__)


# Session management
@router.post("/api/sessions/new", response_model=SessionCreateResponse)
async def create_session(
    request: SessionCreateRequest,
):
    """Create a new conversation session"""
    session_id = str(uuid.uuid4())
    return SessionCreateResponse(
        session_id=session_id, user_id=request.user_id, timestamp=time.time()
    )


@router.post("/api/chat", response_model=ChatResponse)
@observe_trace(name="Chat_Entry", capture_input=True, capture_output=True)
async def chat(
    request: ChatRequest, 
    shoeby_agent: ShoebyAgent = Depends(get_agent)
):
    """Standard chat endpoint with detailed logging and reasoning"""
    if not request.session_id:
        request.session_id = str(uuid.uuid4())

    try:
        # Log the incoming request
        logger.info("🔹 API Chat Request:")
        logger.info(f"   User: {request.user_id}")
        logger.info(f"   Session: {request.session_id}")
        logger.info(f"   Message: '{request.message}'")

        update_trace(
            user_id=request.user_id,
            session_id=request.session_id,
            input=request.message,
        )
        # Get current provider info
        current_provider = shoeby_agent.llm_service.get_current_provider()
        logger.info(f"🤖 Using Provider: {current_provider.upper()}")

        # Enable JSON debug mode
        shoeby_agent.config.show_json = True

        # Process the query with timing and collect reasoning
        start_time = time.time()
        logger.info(f"🤖 Processing query with agent (provider: {current_provider})...")
        response, reasoning_data = await shoeby_agent.process_query_with_reasoning(
            request.message
        )
        processing_time = time.time() - start_time

        # Handle None response
        if response is None:
            logger.error("⚠️ process_query returned None - using fallback message")
            raise ValueError("Error {}")

        # Ensure response is a string
        if not isinstance(response, str):
            logger.error(
                f"⚠️ process_query returned non-string: {type(response)} - {response}"
            )
            raise ValueError(f"⚠️ process_query returned non-string: {type(response)} - {response}")

        # Log reasoning data if available
        if reasoning_data:
            logger.info("🧠 Agent reasoning data:")
            if "search_query" in reasoning_data:
                logger.info(f"   Search Query: {reasoning_data['search_query']}")
            if "search_results" in reasoning_data:
                logger.info(
                    f"   Search Results: {len(reasoning_data['search_results']) if isinstance(reasoning_data['search_results'], list) else 'N/A'} products"
                )
            if "tools_used" in reasoning_data:
                logger.info(f"   Tools Used: {reasoning_data['tools_used']}")
            logger.info(f"   Full Reasoning: {reasoning_data}")

        # Log final response details
        logger.info(f"✅ API Response completed in {processing_time:.2f}s")
        logger.info("📤 FINAL RESPONSE TO USER:")
        logger.info(f"   Provider: {current_provider}")
        logger.info(f"   Length: {len(response)} characters")
        logger.info(f"   Content: {response}")
        logger.info("📤 END API RESPONSE")

        update_trace(output=response)

        return ChatResponse(
            response=response,
            session_id=request.session_id,
            user_id=request.user_id,
            timestamp=time.time(),
            provider=current_provider,
            reasoning=reasoning_data,  # Include reasoning data
        )

    except Exception as e:
        logger.error(f"❌ API Chat Error: {e}")
        logger.exception("Full API error traceback:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process message",
        )


# Conversation history
@router.get("/api/sessions/{session_id}/history", response_model=HistoryResponse)
async def get_conversation_history(
    session_id: str,
    user_id: str = "demo_user",
    limit: int = 50,
    shoeby_agent: ShoebyAgent = Depends(get_agent),
):
    """Get conversation history for a session"""
    try:
        history_entries = (
            await shoeby_agent.conversation_service.get_conversation_history(
                user_id, session_id, limit=limit
            )
        )

        messages = [
            HistoryMessage(
                role="user" if entry.is_user_message() else "assistant",
                content=entry.content,
                timestamp=entry.created_at.timestamp(),
            )
            for entry in history_entries
        ]

        return HistoryResponse(
            session_id=session_id, messages=messages, total_messages=len(messages)
        )

    except Exception as e:
        logger.error(f"History retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversation history",
        )


@router.delete("/api/sessions/{session_id}/history")
async def clear_conversation_history(
    session_id: str,
    user_id: str = Query(..., description="User ID"),
    shoeby_agent: ShoebyAgent = Depends(get_agent),
):
    """Clear conversation history for a session"""
    try:
        await shoeby_agent.conversation_service.clear_conversation_history(
            user_id, session_id
        )
        return {"message": "Conversation history cleared successfully"}
    except Exception as e:
        logger.error(f"Error clearing history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear conversation history",
        )


# Provider management
@router.post("/api/providers/switch")
async def switch_provider(
    request: ProviderSwitchRequest, shoeby_agent: ShoebyAgent = Depends(get_agent)
):
    """Switch to a different LLM provider"""
    try:
        # Convert string to ModelProvider enum
        provider_enum = ModelProvider(request.provider)
        success = shoeby_agent.llm_service.switch_provider(provider_enum)

        if success:
            return ProviderSwitchResponse(
                success=True,
                message=f"Switched to {request.provider} provider",
                current_provider=request.provider,
                timestamp=time.time(),
            )
        else:
            return ProviderSwitchResponse(
                success=False,
                message=f"Failed to switch to {request.provider} provider",
                current_provider=shoeby_agent.llm_service.get_current_provider(),
                timestamp=time.time(),
            )

    except Exception as e:
        logger.error(f"Provider switch error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to switch provider",
        )
