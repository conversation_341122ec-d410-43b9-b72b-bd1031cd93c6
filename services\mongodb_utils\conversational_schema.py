from dataclasses import dataclass
from datetime import datetime, timezone
from typing import List, Any, Dict, Optional

from pydantic import BaseModel


@dataclass
class ConversationData:
    """Represents a single conversation exchange."""
    role: str
    content: str
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None
    obsolete: bool = False
    
    def model_dump(self) -> Dict[str, Any]:
        return {
            "role": self.role,
            "content": self.content, 
            "timestamp": self.timestamp,
            "metadata": self.metadata or {},
            "obsolete": self.obsolete
        }


class ConversationHistoryDocument(BaseModel):
    """MongoDB document for storing conversation history."""
    user_id: str
    session_id: str  
    data: List[ConversationData]
    created_at: datetime = datetime.now(timezone.utc)
