# services/llm_service.py
"""
Large Language Model service for strategy and decision making.
Handles all LLM interactions with BrainPowa provider.
Includes async wrappers to avoid blocking the event loop and to enforce timeouts.
"""
import logging
import asyncio
import time

from typing import Dict, Any, List
from config.settings import AgentConfig
from services.model_providers import ModelProviderFactory, ModelProvider, BaseModelProvider

from prompts.strategy import get_strategy_prompt
from prompts.query_construction import (
    get_weaviate_query_construction_prompt,
    get_weaviate_query_reconstruction_prompt,
    get_visenze_query_construction_prompt,
    get_visenze_query_reconstruction_prompt,
)

from prompts.presentation import (
    get_results_presentation_prompt,
    get_product_details_presentation_prompt,
    get_complementary_products_presentation_prompt
)

from prompts.evaluation import (
    get_evaluation_prompt
)
from prompts.conversation import get_followup_query_prompt, get_irrelevant_query_prompt
from prompts.purchase import get_complementary_products_prompt
from prompts.context import get_context_extraction_prompt
from config.schemas import (
    STRATEGY_SCHEMA, WEAVIATE_QUERY_SCHEMA, VISENZE_QUERY_SCHEMA, RESULTS_EVALUATION_SCHEMA, 
    RESULTS_PRESENTATION_SCHEMA, FOLLOW_UP_SCHEMA, IRRELEVANT_REDIRECT_SCHEMA, COMPLEMENTARY_PRODUCTS_SCHEMA
)

logger = logging.getLogger(__name__)


class LLMService:
    """
    Manages all Large Language Model interactions for the agent.
    
    Uses BrainPowa as the primary provider for all AI-powered functionality
    including strategy decisions, query construction, result presentation,
    and other AI-powered features.
    """
    
    def __init__(self, config: AgentConfig):
        """
        Initialise the LLM service with BrainPowa provider.
        
        Args:
            config: Agent configuration containing API keys and provider settings
        """
        self.config = config
        self.current_provider: BaseModelProvider = None
        self.available_providers: Dict[str, BaseModelProvider] = {}
        
        # Initialise BrainPowa provider
        self._initialise_providers()
        
        # Set default provider - use first available provider if default is not available
        if config.default_model_provider in self.available_providers:
            self.switch_provider(ModelProvider(config.default_model_provider))
        else:
            # Use the first available provider as fallback
            first_available = list(self.available_providers.keys())[0]
            logger.warning(f"Default provider '{config.default_model_provider}' not available, using '{first_available}' instead")
            self.switch_provider(ModelProvider(first_available))
        
        # Define task types for different BrainPowa models
        self.task_types = [
            "strategy",
            "search", 
            "evaluation",
            "presentation",
            "followup",
            "continuity",
            "complementary",
            "irrelevant",
            "purchase"
        ]
        
        logger.info(f"LLMService initialised with {config.default_model_provider} as default provider")
    
    def _initialise_providers(self):
        """Initialise available providers."""
        
        # Try to initialise BrainPowaBrainPowa specialised provider
        try:
            self.available_providers["brainpowa-specialised"] = ModelProviderFactory.create_provider(
                ModelProvider.BRAINPOWA, self.config
            )
            logger.info("✅ BrainPowa specialised provider ready")
        except Exception as e:
            logger.error(f"❌ Failed to initialise BrainPowa specialised provider: {e}")
            logger.warning("BrainPowa specialised provider unavailable - will use OpenAI as fallback")
        
        # Try to initialise BrainPowa reasoning provider
        try:
            self.available_providers["brainpowa-reasoning"] = ModelProviderFactory.create_provider(
                ModelProvider.BRAINPOWA_REASONING, self.config
            )
            logger.info("✅ BrainPowa reasoning provider ready")
        except Exception as e:
            logger.error(f"❌ Failed to initialise BrainPowa reasoning provider: {e}")
            logger.warning("BrainPowa reasoning provider unavailable")
        
        # Try to initialise OpenAI provider
        try:
            self.available_providers["openai"] = ModelProviderFactory.create_provider(
                ModelProvider.OPENAI, self.config
            )
            logger.info("✅ OpenAI provider ready")
        except Exception as e:
            logger.error(f"❌ Failed to initialise OpenAI provider: {e}")
            logger.warning("OpenAI provider unavailable")
        
        # Ensure at least one provider is available
        if not self.available_providers:
            raise RuntimeError("No model providers could be initialised")
        
        # Log what providers are available
        logger.info(f"Available providers: {list(self.available_providers.keys())}")
    
    def switch_provider(self, provider: ModelProvider) -> bool:
        """
        Switch to a different model provider.
        
        Args:
            provider: The provider to switch to
            
        Returns:
            True if switch was successful, False otherwise
        """
        provider_key = provider.value
        
        if provider_key not in self.available_providers:
            logger.error(f"Provider {provider_key} is not available")
            
            # Provide helpful error messages for each provider
            if provider_key == "brainpowa":
                logger.error("BrainPowa requires valid API configuration")
            elif provider_key == "openai":
                logger.error("OpenAI requires valid OPENAI_API_KEY environment variable")
            
            return False
        
        self.current_provider = self.available_providers[provider_key]
        
        # Log the switch with provider-specific info
        if provider_key == "brainpowa":
            logger.info("Switched to BrainPowa provider")
        elif provider_key == "openai":
            logger.info("Switched to OpenAI provider")
        else:
            logger.info(f"Switched to {provider_key} provider")
            
        return True
    
    def list_available_providers(self) -> List[str]:
        """Get list of available providers."""
        return list(self.available_providers.keys())
    
    def force_provider(self, provider_name: str) -> bool:
        """
        Force the provider to a specific provider if available.
        This is useful when other providers fail.
        
        Args:
            provider_name: Name of the provider to force (e.g., "brainpowa", "openai")
            
        Returns:
            True if the specified provider is now active, False otherwise
        """
        if provider_name in self.available_providers:
            self.current_provider = self.available_providers[provider_name]
            logger.info(f"🔄 Forced provider to {provider_name}")
            return True
        else:
            logger.error(f"❌ Cannot force {provider_name} - provider not available")
            return False
    
    def force_brainpowa(self) -> bool:
        """
        Force the provider back to BrainPowa specialised if available.
        This is useful when other providers fail.
        
        Returns:
            True if BrainPowa specialised is now active, False otherwise
        """
        return self.force_provider("brainpowa-specialised")
    
    def force_brainpowa_reasoning(self) -> bool:
        """
        Force the provider to BrainPowa reasoning if available.
        This is useful for complex reasoning tasks.
        
        Returns:
            True if BrainPowa reasoning is now active, False otherwise
        """
        return self.force_provider("brainpowa-reasoning")
    
    def force_openai(self) -> bool:
        """
        Force the provider to OpenAI if available.
        This is useful when BrainPowa fails.
        
        Returns:
            True if OpenAI is now active, False otherwise
        """
        return self.force_provider("openai")
    
    def get_current_provider(self) -> str:
        """Get the name of the current provider."""
        for name, provider in self.available_providers.items():
            if provider == self.current_provider:
                return name
        return "unknown"
    
    def get_provider_info(self) -> Dict[str, str]:
        """Get information about available providers and current selection."""
        info = {}
        
        for name, provider in self.available_providers.items():
            if name == "brainpowa-specialised":
                info[name] = "BrainPowa specialised models (task-specific)"
            elif name == "brainpowa-reasoning":
                info[name] = "BrainPowa reasoning models (120B/20B)"
            else:
                info[name] = str(provider)
        
        if self.current_provider:
            current_name = self.get_current_provider()
            info["current"] = current_name
        else:
            info["current"] = "None"
        
        return info
        
    async def decide_next_actions(self, user_input: str, conversation_history: str,
                                        available_tools: List[Dict[str, Any]], time_context: str = "") -> Dict[str, Any]:
        """
        Analyse user input and decide what actions the agent should take.
        
        This is the core strategy function that determines the agent's behaviour
        based on user intent, conversation context, available tools, and time context.
        
        Args:
            user_input: The user's current message
            conversation_history: Formatted history of previous messages
            available_tools: List of available tools with their schemas
            time_context: Current time context for enhanced decision making
        
        Returns:
            Dictionary containing strategic decision with next actions and reasoning
        """
        # Generate focused context using context agent
        context_prompt = get_context_extraction_prompt(conversation_history, user_input)

        focused_context = await self._get_context_summary(context_prompt)
        
        strategy_prompt = get_strategy_prompt(user_input, focused_context, available_tools, time_context)
        print
        
        # Get decision with validation and retry logic
        strategy_decision = await self._get_strategy_decision_with_validation(strategy_prompt, max_retries=3)
        
        # Add focused context to tool parameters for actions that need it
        if strategy_decision.get("tool_parameters"):
            for tool_params in strategy_decision["tool_parameters"].items():
                if isinstance(tool_params, dict):
                    tool_params["focused_context"] = focused_context
        
        return strategy_decision
    
    async def present_search_results(self, results: List[Any], user_query: str,
                                           conversation_history: str) -> str:
        """
        Generate natural language presentation of search results.
        
        Args:
            results: List of product search results (Product objects or dictionaries)
            user_query: Original user query for context
            conversation_history: Previous conversation context
        
        Returns:
            Natural language response presenting the results
        """
        try:
            results_dicts = []
            for result in results:
                if hasattr(result, 'title'):
                    results_dicts.append({
                        "title": result.title,
                        "brand": result.brand,
                        "price": result.price,
                        "sale_price": result.sale_price,
                        "colour": result.colour,
                        "size": result.size,
                        "material": result.material,
                        "category": result.category,
                        "style": result.style,
                        "occasion": result.occasion,
                        "description": result.description,
                        "url": result.product_url,  # Map product_url to url for presentation layer
                        "available_sizes": getattr(result, 'available_sizes', [result.size] if result.size else [])
                    })
                else:
                    results_dicts.append(result)
            limit = getattr(self.config, 'presentation_results_limit', 6)
            limited_results = results_dicts[:max(1, limit)]
            
            # Generate focused context for results presentation
            context_prompt = get_context_extraction_prompt(conversation_history, user_query)
            
            focused_context = await self._get_context_summary(context_prompt)
            
            presentation_prompt = get_results_presentation_prompt(limited_results, user_query, focused_context)

            decision = await self._get_structured_response(presentation_prompt, RESULTS_PRESENTATION_SCHEMA, "presentation")
            if isinstance(decision, dict) and decision.get("error"):
                logger.error(f"Presentation error: {decision['error']}")
                raise Exception(f"Presentation error: {decision['error']}")
            response_text = decision.get("response_text") if isinstance(decision, dict) else None
            if not response_text:
                raise Exception("Presentation step did not return any text")
            return response_text
        except Exception as e:
            logger.error(f"Exception in present_search_results: {e}")
            raise e
    
    async def present_product_details(self, products: List[Any], user_query: str, focused_context: str = "") -> str:
        """
        Present product details using the product details presentation prompt.
        
        Args:
            products: List of product objects or dictionaries
            user_query: Original user query for context
            focused_context: Focused context about the conversation and what the user is asking about
        
        Returns:
            Natural language response presenting the product details
        """
        try:
            # Convert Product objects to dictionaries if needed
            products_dicts = []
            for product in products:
                if hasattr(product, 'title'):
                    products_dicts.append({
                        "title": product.title,
                        "brand": product.brand,
                        "price": product.price,
                        "sale_price": product.sale_price,
                        "colour": product.colour,
                        "size": product.size,
                        "material": product.material,
                        "category": product.category,
                        "style": product.style,
                        "occasion": product.occasion,
                        "description": product.description,
                        "url": product.product_url,
                        "available_sizes": getattr(product, 'available_sizes', [product.size] if product.size else []),
                        "special_offer": getattr(product, 'special_offer', '')
                    })
                else:
                    products_dicts.append(product)
            
            # Use the product details presentation prompt
            presentation_prompt = get_product_details_presentation_prompt(products_dicts, user_query, focused_context)

            decision = await self._get_structured_response(presentation_prompt, RESULTS_PRESENTATION_SCHEMA, "presentation")
            if isinstance(decision, dict) and decision.get("error"):
                logger.error(f"Product details presentation error: {decision['error']}")
                raise Exception(f"Product details presentation error: {decision['error']}")
            response_text = decision.get("response_text") if isinstance(decision, dict) else None
            if not response_text:
                raise Exception("Product details presentation step did not return any text")
            return response_text
        except Exception as e:
            logger.error(f"Exception in present_product_details: {e}")
            raise e
    
    async def present_complementary_products(self, primary_product: Any, complementary_products: List[Any], user_query: str, focused_context: str = "") -> str:
        """
        Present complementary products alongside a primary product.
        
        Args:
            primary_product: The main product the user is interested in (Product object or dict)
            complementary_products: List of complementary products (Product objects or dictionaries)
            user_query: Original user query for context
            focused_context: Focused context about the conversation and what the user is asking about
        
        Returns:
            Natural language response presenting the complementary products
        """
        try:
            # Convert primary product to dictionary if needed
            if hasattr(primary_product, 'title'):
                primary_dict = {
                    "title": primary_product.title,
                    "brand": primary_product.brand,
                    "price": primary_product.price,
                    "sale_price": primary_product.sale_price,
                    "colour": primary_product.colour,
                    "size": primary_product.size,
                    "material": primary_product.material,
                    "category": primary_product.category,
                    "style": primary_product.style,
                    "occasion": primary_product.occasion,
                    "description": primary_product.description,
                    "url": primary_product.product_url,
                    "available_sizes": getattr(primary_product, 'available_sizes', [primary_product.size] if primary_product.size else [])
                }
            else:
                primary_dict = primary_product
            
            # Convert complementary products to dictionaries if needed
            complementary_dicts = []
            for product in complementary_products:
                if hasattr(product, 'title'):
                    complementary_dicts.append({
                        "title": product.title,
                        "brand": product.brand,
                        "price": product.price,
                        "sale_price": product.sale_price,
                        "colour": product.colour,
                        "size": product.size,
                        "material": product.material,
                        "category": product.category,
                        "style": product.style,
                        "occasion": product.occasion,
                        "description": product.description,
                        "url": product.product_url,
                        "available_sizes": getattr(product, 'available_sizes', [product.size] if product.size else [])
                    })
                else:
                    complementary_dicts.append(product)
            
            # Generate presentation prompt
            presentation_prompt = get_complementary_products_presentation_prompt(
                primary_dict, complementary_dicts, user_query, focused_context
            )

            decision = await self._get_structured_response(presentation_prompt, RESULTS_PRESENTATION_SCHEMA, "presentation")
            if isinstance(decision, dict) and decision.get("error"):
                logger.error(f"Complementary products presentation error: {decision['error']}")
                raise Exception(f"Complementary products presentation error: {decision['error']}")
            response_text = decision.get("response_text") if isinstance(decision, dict) else None
            if not response_text:
                raise Exception("Complementary products presentation step did not return any text")
            return response_text
        except Exception as e:
            logger.error(f"Exception in present_complementary_products: {e}")
            raise e
    
    async def present_multi_tool_results(self, executed_tools: List[str], tool_results: Dict[str, str], 
                                             user_query: str, focused_context: str = "") -> str:
        """
        Present results from multiple tool executions.
        
        Args:
            executed_tools: List of tool names that were executed
            tool_results: Dictionary mapping tool names to their results
            user_query: Original user query for context
            focused_context: Focused context about the conversation and what the user is asking about
        
        Returns:
            Natural language response presenting the combined results
        """
        try:
            # Generate multi-tool presentation prompt
            from prompts.presentation import get_multi_tool_presentation_prompt
            presentation_prompt = get_multi_tool_presentation_prompt(
                executed_tools, tool_results, user_query, focused_context
            )

            decision = await self._get_structured_response(presentation_prompt, RESULTS_PRESENTATION_SCHEMA, "presentation")
            if isinstance(decision, dict) and decision.get("error"):
                logger.error(f"Multi-tool presentation error: {decision['error']}")
                raise Exception(f"Multi-tool presentation error: {decision['error']}")
            response_text = decision.get("response_text") if isinstance(decision, dict) else None
            if not response_text:
                raise Exception("Multi-tool presentation step did not return any text")
            return response_text
        except Exception as e:
            logger.error(f"Exception in present_multi_tool_results: {e}")
            raise e
    
    async def evaluate_search_results(self, results: List[Any], tool_intent: str, focused_context: str) -> Dict[str, Any]:
        """
        Evaluate whether search results satisfy the customer's needs.
        
        Args:
            results: List of product search results (Product objects or dictionaries)
            tool_intent: The original customer request/intent
            focused_context: Focused, relevant context for the current turn
        
        Returns:
            Dictionary with evaluation results including 'results_satisfactory' boolean
        """
        try:
            results_dicts = []
            for result in results:
                if hasattr(result, 'title'):
                    results_dicts.append({
                        "title": result.title,
                        "brand": result.brand,
                        "price": result.price,
                        "sale_price": result.sale_price,
                        "colour": result.colour,
                        "size": result.size,
                        "material": result.material,
                        "category": result.category,
                        "style": result.style,
                        "occasion": result.occasion,
                        "description": result.description,
                        "url": result.product_url,  # Map product_url to url for presentation layer
                        "available_sizes": getattr(result, 'available_sizes', [result.size] if result.size else [])
                    })
                else:
                    results_dicts.append(result)
            evaluation_prompt = get_evaluation_prompt(results_dicts, tool_intent, focused_context)
            return await self._get_structured_response(evaluation_prompt, RESULTS_EVALUATION_SCHEMA, "evaluation")
        except Exception as e:
            logger.error(f"Error in evaluate_search_results: {e}")
            return {"results_satisfactory": False, "reasoning": f"Evaluation error: {str(e)}"}

    def reconstruct_search_query(self, feedback_history: List[Dict[str, Any]], last_query: Dict[str, Any], focused_context: str) -> Dict[str, Any]:
        """
        Reconstruct a Visenze search query using feedback from previous evaluations
        and the last attempted query. The original tool intent is excluded to avoid
        reintroducing prior mistakes; rely solely on feedback and history.

        Args:
            feedback_history: Ordered list of feedback objects from evaluations
            last_query: The last executed Visenze query parameters
            focused_context: Focused, relevant context for the current turn

        Returns:
            Dictionary containing a revised structured search query
        """
        reconstruction_prompt = get_visenze_query_reconstruction_prompt(
            feedback_history=feedback_history,
            last_query=last_query,
            focused_context=focused_context,
        )
        return self._get_structured_response(reconstruction_prompt, VISENZE_QUERY_SCHEMA, "visenze_query")

    async def construct_search_query(self, user_input: str, conversation_history: str) -> Dict[str, Any]:
        """
        Construct a structured Visenze search query from user input.
        
        Args:
            user_input: User's search request
            conversation_history: Previous conversation for context
        
        Returns:
            Dictionary containing structured search parameters
        """
        # Generate focused context for search
        context_prompt = get_context_extraction_prompt(conversation_history, user_input)
        
        focused_context = await self._get_context_summary(context_prompt)
        
        # Construct Visenze query using LLM with focused context
        visenze_prompt = get_visenze_query_construction_prompt(user_input, focused_context)
        
        return await self._get_structured_response(visenze_prompt, VISENZE_QUERY_SCHEMA, "visenze_query")
    
    def generate_follow_up_question(self, user_query: str, focused_context: str, 
                                   time_context: str = "", execution_context: Dict[str, Any] = None) -> str:
        """
        Generate a follow-up question to clarify user requirements.
        
        Args:
            user_query: User's current query
            focused_context: Focused, relevant context for the current turn
            time_context: Current time context for enhanced responses
            execution_context: Execution context containing search results and other data
            
        Returns:
            Follow-up question to clarify user requirements
        """
        # Get search results from execution context
        search_results = None
        if execution_context:
            search_results = execution_context.get("current_search_results") or execution_context.get("search_results") or execution_context.get("results")
        
        # Use the enhanced prompt with the provided focused context
        followup_prompt = get_followup_query_prompt(
            user_query, focused_context
        )
        
        decision = self._get_structured_response(followup_prompt, FOLLOW_UP_SCHEMA, "followup")
        return decision.get("response_text")
    
    async def generate_follow_up_question_async(self, user_query: str, focused_context: str, 
                                              time_context: str = "", execution_context: Dict[str, Any] = None) -> str:
        """
        Generate a follow-up question to clarify user requirements (async version).
        
        Args:
            user_query: User's current query
            focused_context: Focused, relevant context for the current turn
            time_context: Current time context for enhanced responses
            execution_context: Execution context containing search results and other data
            
        Returns:
            Follow-up question to clarify user requirements
        """
        # Get search results from execution context
        search_results = None
        if execution_context:
            search_results = execution_context.get("current_search_results") or execution_context.get("search_results") or execution_context.get("results")
        
        # Use the enhanced prompt with the provided focused context
        followup_prompt = get_followup_query_prompt(
            user_query, focused_context
        )
        
        decision = await self._get_structured_response(followup_prompt, FOLLOW_UP_SCHEMA, "followup")
        return decision.get("response_text")
    
    def handle_irrelevant_query(self, user_query: str) -> str:
        """
        Handle queries that are outside the scope of fashion and shopping assistance.
        
        Args:
            user_query: The irrelevant user query
            
        Returns:
            Polite redirect response
        """
        irrelevant_prompt = get_irrelevant_query_prompt(user_query)
        decision = self._get_structured_response(irrelevant_prompt, IRRELEVANT_REDIRECT_SCHEMA, "irrelevant")
        return decision.get("response_text")
    
    def suggest_complementary_products(self, product_terms: str, focused_context: str, recent_actions: str = "") -> Dict[str, Any]:
        """
        Determine what complementary products to search for based on context.
        
        Args:
            product_terms: The specific product terms to find complementary items for
            focused_context: Focused, relevant context for the current turn
            recent_actions: Recent actions taken in the conversation (optional)
            
        Returns:
            Dictionary containing complementary product search strategy
        """
        # Use the provided focused context directly
        complementary_prompt = get_complementary_products_prompt(product_terms, focused_context)
        
        # Log the prompt being sent (clean)
        logger.debug(f"\n🔄 COMPLEMENTARY PRODUCTS PROMPT:")
        logger.debug(f"Product Terms: {product_terms}")
        logger.debug(f"Focused Context: {focused_context}")
        logger.debug(f"Recent Actions: {recent_actions}")
        logger.debug(f"=" * 80)
        
        return self._get_structured_response(complementary_prompt, COMPLEMENTARY_PRODUCTS_SCHEMA, "complementary")
    
    async def generate_response(self, prompt: str) -> str:
        """
        Generate a natural language response from a prompt.
        
        Args:
            prompt: The prompt to send to the LLM
            
        Returns:
            Natural language response as a string
        """
        try:
            # Use a simple schema for free-form text generation
            simple_schema = {
                "type": "object",
                "properties": {
                    "response_text": {
                        "type": "string",
                        "description": "The natural language response to the prompt"
                    }
                },
                "required": ["response_text"]
            }
            
            result = await self._get_structured_response(prompt, simple_schema, "response_generation")
            
            # Handle case where result might not be a dict
            if not isinstance(result, dict):
                logger.warning(f"Unexpected result type from _get_structured_response: {type(result)}")
                return str(result) if result else "I'm sorry, I couldn't generate a proper response."
            
            if result.get("error"):
                logger.error(f"Error generating response: {result['error']}")
                raise Exception(f"Error generating response: {result['error']}")
            
            # Extract response_text, with fallback to the entire result if needed
            response_text = result.get("response_text")
            if response_text:
                return str(response_text)
            
            # If no response_text, try to convert the entire result to string
            logger.warning("No response_text found in result, converting entire result to string")
            return str(result)
            
        except Exception as e:
            logger.error(f"Exception in generate_response: {e}")
            raise e
    
    async def _get_structured_response(self, prompt: str, schema: Dict[str, Any], task_type: str) -> Dict[str, Any]:
        """
        Get a structured response from the current LLM provider with timeout and thread offloading.
        
        Args:
            prompt: The prompt to send to the LLM
            schema: Expected JSON schema for response validation
            task_type: Type of task for model selection
        
        Returns:
            Dictionary containing structured response or error information
        """
        start_time = time.time()
        if not self.current_provider:
            logger.error("No current provider available")
            return {"error": "No LLM provider available"}
        from config.merchant_utils import get_temperature_for_task
        temperature = get_temperature_for_task(task_type)
        timeout_s = getattr(self.config, 'llm_timeout_seconds', 20)
        
        # Debug: Print prompt and task details
        print(f"\n{'='*80}")
        print(f"🤖 LLM CALL - {task_type.upper()}")
        print(f"Provider: {self.get_current_provider()}")
        print(f"Temperature: {temperature}")
        print(f"Timeout: {timeout_s}s")
        print(f"{'='*80}")
        print(f"📝 PROMPT:")
        print(prompt)
        print(f"{'='*80}")
        
        try:
            result = await asyncio.wait_for(
                asyncio.to_thread(
                    self.current_provider.generate_structured_response,
                    prompt, schema, task_type, None, temperature, None
                ),
                timeout=timeout_s
            )
            response_time = time.time() - start_time
            
            # Debug: Print response
            print(f"📤 RESPONSE ({response_time:.3f}s):")
            print(result)
            print(f"{'='*80}\n")
            
            logger.info(f"⏱️ LLM {task_type} call completed in {response_time:.3f}s")
            return result
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            print(f"❌ TIMEOUT ERROR ({response_time:.3f}s):")
            print(f"LLM call timed out for task '{task_type}' after {response_time:.3f}s (timeout: {timeout_s}s)")
            print(f"{'='*80}\n")
            logger.error(f"LLM call timed out for task '{task_type}' after {response_time:.3f}s (timeout: {timeout_s}s)")
            return {"error": f"LLM timeout after {timeout_s}s"}
        except Exception as e:
            response_time = time.time() - start_time
            print(f"❌ ERROR ({response_time:.3f}s):")
            print(f"Async LLM provider error: {e}")
            print(f"{'='*80}\n")
            logger.error(f"Async LLM provider error after {response_time:.3f}s: {e}")
            return {"error": f"LLM provider error: {str(e)}"}
    
    async def _get_context_summary(self, context_prompt: str) -> str:
        """
        Generate focused context summary using the context agent.
        
        Args:
            context_prompt: The context agent prompt
            
        Returns:
            str: Focused, natural language context summary
        """
        start_time = time.time()
        try:
            # Check if current provider is available
            if not self.current_provider:
                logger.error("No current provider available for context summary")
                return "No specific context available for this turn."
            
            # Debug: Print context prompt and details
            print(f"\n{'='*80}")
            print(f"🧠 CONTEXT SUMMARY CALL")
            print(f"Provider: {self.get_current_provider()}")
            print(f"{'='*80}")
            print(f"📝 CONTEXT PROMPT:")
            print(context_prompt)
            print(f"{'='*80}")
            
            # Use the current provider to generate context summary
            response = self.current_provider.generate_text(context_prompt)
            
            # Debug: Print context response
            print(f"📤 CONTEXT RESPONSE:")
            print(response.strip())
            print(f"{'='*80}\n")
                        
            return response.strip()
        except Exception as e:
            response_time = time.time() - start_time
            print(f"❌ CONTEXT ERROR ({response_time:.3f}s):")
            print(f"Context agent error: {e}")
            print(f"{'='*80}\n")
            logger.error(f"Context agent error after {response_time:.3f}s: {e}")
            # Fallback to empty context if context agent fails
            return "No specific context available for this turn."
    
    async def _get_strategy_decision_with_validation(self, strategy_prompt: str, max_retries: int = 3) -> Dict[str, Any]:
        """
        Get a strategy decision from the LLM with validation and retry, enforcing timeouts.
        
        Args:
            strategy_prompt: The strategy prompt
            max_retries: Maximum number of retry attempts
        
        Returns:
            Dictionary containing strategy decision with next actions
        """
        for attempt in range(max_retries + 1):
            strategy_decision = await self._get_structured_response(strategy_prompt, STRATEGY_SCHEMA, "strategy")
            if isinstance(strategy_decision, list):
                logger.error(f"Invalid response format on attempt {attempt + 1}: Expected dict, got list")
                logger.error(f"Received: {strategy_decision}")
                continue
            if isinstance(strategy_decision, dict) and strategy_decision.get("error"):
                logger.error(f"LLM Error on attempt {attempt + 1}: {strategy_decision['error']}")
                continue
            if not isinstance(strategy_decision, dict):
                logger.error(f"Invalid response type on attempt {attempt + 1}: Expected dict, got {type(strategy_decision)}")
                logger.error(f"Received: {strategy_decision}")
                continue
            if self.config.show_json and attempt > 0:
                logger.info(f"Strategy decision received on attempt {attempt + 1}")
            return strategy_decision
        logger.error(f"Max retries ({max_retries}) exhausted. Using fallback strategy.")
        return {
            "next_best_actions": ["ask_follow_up_question"],
            "tool_parameters": {
                "ask_follow_up_question": {
                    "user_query": "Please provide more details about what you're looking for",
                    "focused_context": "No specific context available for this turn."
                }
            },
            "reasoning": "LLM failed to provide valid strategy after multiple attempts. Using fallback.",
            "irrelevant_query": False
        }
    
    async def generate_structured_response(self, prompt: str, schema: Dict[str, Any], task_type: str) -> Dict[str, Any]:
        """
        Generate a structured JSON response from the current LLM provider.
        
        Args:
            prompt: The prompt to send to the LLM
            schema: Expected JSON schema for response validation
            task_type: Type of task for model selection
        
        Returns:
            Dictionary containing structured response or error information
        """
        return await self._get_structured_response(prompt, schema, task_type)
    