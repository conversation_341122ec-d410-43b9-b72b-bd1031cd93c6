"""
Base agent interface for all agent components.
Defines the contract that all agents must implement.
"""
from typing import Dict, Any, List
from abc import ABC, abstractmethod

class BaseAgent(ABC):
    """Base interface for all agent components"""
    
    def __init__(self, name: str, description: str, required_params: List[str]):
        self.name = name
        self.description = description
        self.required_params = required_params
    
    @abstractmethod
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent with given parameters and context"""
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Return the agent's parameter schema for the LLM"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": self._get_parameter_schema(),
                "required": self.required_params
            }
        }
    
    @abstractmethod
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """Return the parameter schema for this agent"""
        pass
