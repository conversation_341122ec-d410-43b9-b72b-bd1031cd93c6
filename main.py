"""
Main entry point for the Conversational Agent

Note: For web API access with streaming support, use api.py instead.
This file provides the standalone CLI interface for the agent.
Uses LangGraph framework for agent orchestration.
"""
import time
import asyncio
import logging
import signal
import argparse



from typing import Dict, Any
from config.settings import AgentConfig
from core.agent_orchestrator import AgentOrchestrator
from observability.core import init_observability, observe_trace


# Setup logging 
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%d/%m/%Y %H:%M:%S'  
)
logger = logging.getLogger(__name__)


class ShoebyAgent:
    """
    Main Shoeby conversational agent using LangGraph framework.
    
    Uses LangGraph for agent orchestration and state management.
    """
    
    def __init__(self, config: AgentConfig):
        """
        initialise the Shoeby agent with configuration and orchestrator.
        
        Args:
            config: Agent configuration containing connection details and settings
        """
        self.config = config
        
        # Create the agent orchestrator
        try:
            self.orchestrator = AgentOrchestrator(config)
            logger.info(f"✅ Agent initialised with LangGraph orchestrator")
        except Exception as e:
            logger.error(f"Failed to create agent orchestrator: {e}")
            raise RuntimeError(f"Failed to initialise agent orchestrator: {e}")
    
    async def setup(self) -> bool:
        """
        Set up the agent orchestrator and all required services.
        
        Returns:
            True if setup successful, False otherwise
        """
        logger.info(f"🔧 Setting up Shoeby agent orchestrator...")
        
        try:
            # Delegate setup to the orchestrator
            setup_success = await self.orchestrator.setup()
            
            if setup_success:
                logger.info(f"✅ Agent orchestrator setup complete")
                return True
            else:
                logger.error(f"❌ Agent orchestrator setup failed")
                return False
                
        except Exception as e:
            logger.error(f"Agent setup failed: {e}")
            return False
    
    @property
    def tools_registry(self):
        """
        Get the tools registry from the orchestrator.
        
        Returns:
            ToolsRegistry instance from the orchestrator
        """
        return self.orchestrator.tools_registry
    
    @property
    def agents_registry(self):
        """
        Get the agents registry from the orchestrator.
        
        Returns:
            AgentsRegistry instance from the orchestrator
        """
        return self.orchestrator.agents_registry
    
    @property
    def session_cache(self):
        """
        Get the session cache from the orchestrator.
        
        Returns:
            Session cache instance from the orchestrator
        """
        return self.orchestrator.session_cache
    
    @property
    def llm_service(self):
        """
        Get the LLM service from the orchestrator.
        
        Returns:
            LLMService instance from the orchestrator
        """
        return self.orchestrator.llm_service
    
    @property
    def conversation_service(self):
        """
        Get the conversation service from the orchestrator.
        
        Returns:
            ConversationService instance from the orchestrator
        """
        return self.orchestrator.conversation_service
    
    @property
    def search_engine(self):
        """
        Get the search engine from the orchestrator.
        
        Returns:
            SearchEngine instance from the orchestrator
        """
        return self.orchestrator.search_engine
    
    @observe_trace()
    async def process_query_with_reasoning(self, user_input: str) -> tuple[str, Dict[str, Any]]:
        """
        Process a user query using the agent orchestrator and return both response and reasoning data.
        
        Args:
            user_input: User's input message
            
        Returns:
            Tuple of (response, reasoning_data)
        """
        try:
            # Delegate to the orchestrator
            return await self.orchestrator.process_query_with_reasoning(user_input)
        except Exception as e:
            logger.error(f"Orchestrator processing error: {e}")
            error_reasoning = {
                "framework": "langgraph",
                "user_query": user_input,
                "timestamp": time.time(),
                "error": str(e)
            }
            return "I'm sorry, I encountered an issue processing your request. Could you please try again.", error_reasoning

    async def run_interactive_session(self):
        """
        Run the interactive chat session with framework information.
        
        Provides debugging commands and maintains conversation state.
        """
        # Show orchestrator and provider status
        orchestrator_info = self.orchestrator.get_framework_info()
        
        print("🛍️ Shoeby Conversational Agent")
        print("Intelligent fashion assistant with LangGraph orchestration")
        print("Commands: 'quit', 'orchestrator', 'agents', 'info'")
        print("=" * 70)
        print(f"🔧 Current Orchestrator: {orchestrator_info.get('name', 'Unknown')}")
        
        # Start with time-aware greeting
        greeting = self.orchestrator.datetime_service.get_simple_greeting()
        print(f"\n🤖 Shoeby Agent: {greeting}")
                        
        while True:
            try:
                # Check if shutdown was requested
                if shutdown_requested:
                    print("\n🛑 Shutdown requested. Cleaning up...")
                    break
                
                # Get user input with timeout to prevent hanging
                try:
                    user_input = input("\n👤 You: ").strip()
                except (EOFError, KeyboardInterrupt):
                    print("\n🛑 Input interrupted. Cleaning up...")
                    break
                
                # Handle special commands
                if user_input.lower() == 'quit':
                    print("👋 Goodbye! Thanks for shopping with Shoeby!")
                    break
                elif user_input.lower() == 'orchestrator':
                    await self._show_orchestrator_info()
                    continue
                elif user_input.lower() == 'agents':
                    self._show_agents()
                    continue
                elif user_input.lower() == 'info':
                    await self._show_full_info()
                    continue
                
                # Process the user's query
                print(f"\n🤖 Processing your request with LangGraph orchestrator...")
                result = await self.process_query_with_reasoning(user_input)
                
                # Extract response from tuple (response, reasoning_data)
                if isinstance(result, tuple) and len(result) >= 1:
                    response, reasoning = result
                    
                    # Show reasoning if available
                    if reasoning and reasoning.get("agents_used"):
                        agents_used = reasoning.get("agents_used", [])
                        print(f"🔧 Agents used: {', '.join(agents_used)}")
                else:
                    response = result
                
                if response:
                    print(f"\n🤖 Shoeby Agent: {response}")
                else:
                    print("\n🤖 Shoeby Agent: I'm sorry, I didn't get a response. Could you try again?")
                    
            except KeyboardInterrupt:
                print("\n\n🛑 Interrupted by user. Cleaning up...")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")
                logger.error(f"Interactive session error: {e}")
                continue
    
    async def _show_orchestrator_info(self):
        """Display current orchestrator information."""
        try:
            orchestrator_info = self.orchestrator.get_framework_info()
            print("\n🔧 Orchestrator Information:")
            for key, value in orchestrator_info.items():
                print(f"  • {key.title()}: {value}")
        except Exception as e:
            logger.error(f"Error showing orchestrator info: {e}")
            print("🔧 Unable to load orchestrator information")
    
    def _show_agents(self):
        """Display available agents."""
        try:
            agents = self.orchestrator.list_available_agents()
            print(f"\n🤖 Available Agents ({len(agents)}):")
            for i, agent in enumerate(agents, 1):
                print(f"  {i}. {agent}")
        except Exception as e:
            logger.error(f"Error showing agents: {e}")
            print("🤖 Unable to list agents")
    
    async def _show_full_info(self):
        """Display complete system information."""
        try:
            await self._show_orchestrator_info()
            self._show_agents()
            
            # Show configuration info
            print(f"\n⚙️ Configuration:")
            print(f"  • Orchestrator Type: LangGraph")
            print(f"  • Model Provider: {self.config.default_model_provider}")
            print(f"  • Environment: {self.config.environment}")
            
        except Exception as e:
            logger.error(f"Error showing full info: {e}")
            print("ℹ️ Unable to display complete information")
    
    async def cleanup(self):
        """Clean up resources and connections."""
        logger.info("Cleaning up agent resources...")
        
        try:
            # Delegate cleanup to the orchestrator
            await self.orchestrator.cleanup()
            logger.info("✅ Agent cleanup completed successfully")
            
        except Exception as e:
            logger.error(f"Error during agent cleanup: {e}")


# Global variable to track shutdown signal
shutdown_requested = False
current_agent = None

def signal_handler(signum, _frame):
    """Handle shutdown signals gracefully."""
    global shutdown_requested, current_agent
    print(f"\n🛑 Received shutdown signal {signum}. Cleaning up...")
    shutdown_requested = True
    
    # If we have an agent, trigger cleanup
    if current_agent:
        # Schedule cleanup in the event loop
        asyncio.create_task(current_agent.cleanup())

async def shutdown_gracefully(agent):
    """Ensure graceful shutdown of the agent."""
    if agent:
        try:
            await agent.cleanup()
            logger.info("✅ Graceful shutdown completed")
        except Exception as e:
            logger.error(f"Error during graceful shutdown: {e}")

async def main():
    """
    Main entry point for the Shoeby Conversational Agent.
    
    Sets up configuration, initialises the agent, and runs the interactive session.
    """
    global current_agent
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Shoeby Conversational Agent')
    # Framework is now fixed to LangGraph, no need for framework argument
    parser.add_argument('--provider', '-p', 
                       choices=['brainpowa-specialised', 'brainpowa-reasoning', 'openai'],
                       help='Choose the LLM provider to use')
    parser.add_argument('--list-info', action='store_true',
                       help='List system information and exit')
    
    args = parser.parse_args()
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    agent = None
    try:
        # Load configuration
        config = AgentConfig()
        
        # Override default provider if specified
        if args.provider:
            config.default_model_provider = args.provider
            print(f"🎯 Using specified provider: {args.provider}")
        
        # List system info if requested
        if args.list_info:
            print("\n📋 System Information:")
            print("  • Orchestrator: LangGraph")
            print(f"  • Model Provider: {config.default_model_provider}")
            print(f"  • Environment: {config.environment}")
            return
        
        # Create and setup the agent
        agent = ShoebyAgent(config)
        current_agent = agent # Assign to global variable

        # Initialize observability provider (OTEL, langfuse, or null)
        try:
            init_observability(config.observability_provider_name)
            logger.info(f"Observability provider initialized: {config.observability_provider_name}")
        except Exception as e:
            logger.warning(f"Observability initialization failed: {e}")

        setup_success = await agent.setup()
        if not setup_success:
            print("💥 Failed to initialise agent services. Please check configuration and connections.")
            return
        
        # Run the interactive session
        await agent.run_interactive_session()
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print("💥 The agent encountered a fatal error and needs to shut down.")
    finally:
        # Always ensure cleanup happens
        if agent:
            try:
                await shutdown_gracefully(agent)
                logger.info("Agent cleanup completed successfully")
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {cleanup_error}")
        else:
            logger.info("No agent to cleanup")
        
        # Clear global reference
        current_agent = None

if __name__ == "__main__":
    try:
        # Run the main function with proper signal handling
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"💥 Unexpected error: {e}")
        logger.error(f"Unexpected error in main: {e}")
    finally:
        # Ensure any remaining cleanup happens
        if current_agent:
            try:
                # Create a new event loop if needed for cleanup
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(shutdown_gracefully(current_agent))
                loop.close()
            except Exception as cleanup_error:
                logger.error(f"Final cleanup attempt failed: {cleanup_error}")
            finally:
                current_agent = None
