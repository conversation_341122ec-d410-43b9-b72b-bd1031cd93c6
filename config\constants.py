"""
Constants and error messages for the <PERSON><PERSON>by agent.
Contains all hardcoded strings and error messages used throughout the system.
"""

# Error messages
class ErrorMessages:
    """Error messages used throughout the system."""
    
    # Agent availability errors
    SEARCH_AGENT_NOT_AVAILABLE = "Search agent not available"
    EVALUATION_AGENT_NOT_AVAILABLE = "Evaluation agent not available"
    PRESENTATION_AGENT_NOT_AVAILABLE = "Presentation agent not available"
    CONVERSATION_AGENT_NOT_AVAILABLE = "Conversation agent not available"
    
    # Operation failure errors
    QUERY_CONSTRUCTION_FAILED = "Query construction failed"
    PRODUCT_EVALUATION_FAILED = "Product evaluation failed"
    PRODUCT_PRESENTATION_FAILED = "Product presentation failed"
    CONVERSATION_HANDLING_FAILED = "Conversation handling failed"
    
    # Generic errors
    UNKNOWN_ERROR = "Unknown error"
    WORKFLOW_ERROR = "An unknown error occurred"
    
    # Product extraction errors
    NO_PRODUCT_FOUND = "NO_PRODUCT_FOUND"
    PRODUCT_EXTRACTION_FAILED = "Product extraction failed"
    
    # Basket operation errors
    BASKET_UPDATE_FAILED = "Basket update failed"
    BASKET_AGENT_NOT_AVAILABLE = "Basket agent not available"


# Success messages
class SuccessMessages:
    """Success messages used throughout the system."""
    
    # Basket operations
    ITEM_ADDED_TO_BASKET = "Great! I've added the {product_name} in size {size} to your basket."
    ITEM_ADDED_WITH_MESSAGE = "Great! I've added the {product_name} in size {size} to your basket. {message}"
    
    # Search operations
    SEARCH_SUCCESS = "Search completed successfully"
    PRODUCTS_FOUND = "Found {count} products"


# Context messages
class ContextMessages:
    """Context messages used for user input and conversation."""
    
    # Basket operations
    BASKET_ADD_SUCCESS = "Successfully added {product_name} in size {size} to basket"
    BASKET_ADD_CONTEXT = "User just added {product_name} in size {size} to their basket. {message}"
    
    # Error contexts
    BASKET_ADD_ERROR = "Error adding {product_name} to basket: {error_msg}"
    BASKET_ADD_ERROR_CONTEXT = "Failed to add {product_name} in size {size} to basket due to: {error_msg}"


# Log messages
class LogMessages:
    """Log messages used throughout the system."""
    
    # Workflow messages
    WORKFLOW_ERROR = "Workflow error: {error_msg}"
    SEARCH_EXECUTION_ERROR = "Error in execute_search: {error}"
    PRODUCT_EVALUATION_ERROR = "Error in evaluate_products: {error}"
    PRODUCT_PRESENTATION_ERROR = "Error in present_products: {error}"
    CONVERSATION_HANDLING_ERROR = "Error in handle_conversation: {error}"
    
    # Product extraction messages
    PRODUCT_EXTRACTION_PROMPT = "PRODUCT EXTRACTION PROMPT:"
    PRODUCT_EXTRACTION_SEPARATOR = "=" * 80


# Response messages
class ResponseMessages:
    """Response messages used throughout the system."""
    
    # Default responses
    DEFAULT_PRODUCT_RESPONSE = "I found some products for you."
    DEFAULT_ERROR_RESPONSE = "I'm sorry, I encountered an issue processing your request. Could you please try again?"
    DEFAULT_CONVERSATION_ERROR = "I'm sorry, I couldn't process your request properly."
    DEFAULT_SERVICE_UNAVAILABLE = "I'm sorry, the conversation service is not available right now."
    DEFAULT_BASKET_ERROR = "I'm sorry, there was an error processing your request: {error}"
    
    # Basket error responses
    BASKET_ADD_ERROR_RESPONSE = "I encountered an issue adding the {product_name} to your basket: {error_msg}"
    BASKET_ADD_ERROR_FALLBACK = "I encountered an issue adding the {product_name} to your basket: {error_msg}"
    
    # Technical difficulties
    TECHNICAL_DIFFICULTIES = "I'm experiencing technical difficulties with the basket service right now. Please try again in a moment."


# Action messages
class ActionMessages:
    """Action messages used for workflow tracking."""
    
    # Search actions
    SEARCH_EXECUTED = "search_executed"
    PRODUCTS_PRESENTED = "products_presented"
    PRODUCTS_EVALUATED = "products_evaluated"
    
    # Product details actions
    PRODUCT_DETAILS_RETRIEVED = "product_details_retrieved"
    PRODUCT_DETAILS_PRESENTED = "product_details_presented"
    
    # Complementary products actions
    COMPLEMENTARY_PRODUCTS_RETRIEVED = "complementary_products_retrieved"
    COMPLEMENTARY_PRODUCTS_PRESENTED = "complementary_products_presented"
    
    # Conversation actions
    CONVERSATION_HANDLED = "conversation_handled"
    CONVERSATION_ERROR = "conversation_error"
    CONVERSATION_SERVICE_UNAVAILABLE = "conversation_service_unavailable"
    
    # Basket actions
    ITEM_ADDED_TO_BASKET = "item_added_to_basket"
    BASKET_ERROR = "basket_error"
    
    # Error actions
    WORKFLOW_ERROR = "workflow_error"
    BASKET_SERVICE_UNAVAILABLE = "basket_service_unavailable"
