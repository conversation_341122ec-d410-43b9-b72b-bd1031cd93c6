"""
Shopping basket management router for the Shoeby Agent API.
"""

import logging
from fastapi import APIRouter, HTTPException, Depends, status

from ..dependencies import get_agent
from ..models import (
    BasketResponse,
    AddToBasketRequest,
    RemoveFromBasketRequest,
    UpdateQuantityRequest,
    BasketItem,
)
from main import ShoebyAgent

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/api/basket", response_model=BasketResponse)
async def get_basket(shoeby_agent: ShoebyAgent = Depends(get_agent)):
    """Get current basket contents"""
    try:
        basket_tool = shoeby_agent.tools_registry.get_tool("update_basket")
        if not basket_tool:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Basket tool not available",
            )

        basket_items = basket_tool.get_basket_contents()

        items = [
            BasketItem(
                name=item.name, price=item.price, quantity=item.quantity, size=item.size
            )
            for item in basket_items
        ]

        return BasketResponse(
            items=items,
            total_items=basket_tool.get_basket_item_count(),
            total_price=basket_tool.get_basket_total(),
        )
    except Exception as e:
        logger.error(f"Basket retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve basket contents",
        )


@router.post("/api/basket/clear")
async def clear_basket(shoeby_agent: ShoebyAgent = Depends(get_agent)):
    """Clear the shopping basket"""
    try:
        basket_tool = shoeby_agent.tools_registry.get_tool("update_basket")
        if not basket_tool:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Basket tool not available",
            )

        # Clear basket contents
        basket_tool.basket_contents.clear()

        return {"message": "Basket cleared successfully"}
    except Exception as e:
        logger.error(f"Basket clearing error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear basket",
        )


@router.post("/api/basket/add")
async def add_to_basket(
    request: AddToBasketRequest, shoeby_agent: ShoebyAgent = Depends(get_agent)
):
    """Add an item to the shopping basket"""
    try:
        basket_tool = shoeby_agent.tools_registry.get_tool("update_basket")
        if not basket_tool:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Basket tool not available",
            )

        # Execute add to basket action
        context = {"basket_contents": basket_tool.basket_contents}
        result = await basket_tool.execute(
            {
                "action": "add",
                "product_name": request.product_name,
                "size": request.size,
                "quantity": request.quantity,
                "conversation_context": request.conversation_context or "",
            },
            context,
        )

        if result["success"]:
            return {
                "success": True,
                "message": result["message"],
                "basket_total": result["basket_total"],
                "total_items": result["total_items"],
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=result["message"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Add to basket error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add item to basket",
        )


@router.post("/api/basket/remove")
async def remove_from_basket(
    request: RemoveFromBasketRequest, shoeby_agent: ShoebyAgent = Depends(get_agent)
):
    """Remove an item from the shopping basket"""
    try:
        basket_tool = shoeby_agent.tools_registry.get_tool("update_basket")
        if not basket_tool:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Basket tool not available",
            )

        # Execute remove from basket action
        context = {"basket_contents": basket_tool.basket_contents}
        result = await basket_tool.execute(
            {
                "action": "remove",
                "product_name": request.product_name,
                "size": request.size,
                "quantity": request.quantity,
            },
            context,
        )

        if result["success"]:
            return {
                "success": True,
                "message": result["message"],
                "basket_total": result["basket_total"],
                "total_items": result["total_items"],
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=result["message"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Remove from basket error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove item from basket",
        )


@router.post("/api/basket/update-quantity")
async def update_basket_quantity(
    request: UpdateQuantityRequest, shoeby_agent: ShoebyAgent = Depends(get_agent)
):
    """Update the quantity of an item in the shopping basket"""
    try:
        basket_tool = shoeby_agent.tools_registry.get_tool("update_basket")
        if not basket_tool:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Basket tool not available",
            )

        # Execute update quantity action
        context = {"basket_contents": basket_tool.basket_contents}
        result = await basket_tool.execute(
            {
                "action": "update_quantity",
                "product_name": request.product_name,
                "size": request.size,
                "quantity": request.quantity,
            },
            context,
        )

        if result["success"]:
            return {
                "success": True,
                "message": result["message"],
                "basket_total": result["basket_total"],
                "total_items": result["total_items"],
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=result["message"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update quantity error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update item quantity",
        )
