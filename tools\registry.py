"""
Tools registry for managing available tools in the system.

This registry provides a central place to register and manage all tools
that can be used by the agent orchestrator.
"""
import logging
from typing import Dict, List, Optional
from tools.base_tool import BaseTool

logger = logging.getLogger(__name__)


class ToolsRegistry:
    """
    Registry for managing available tools.
    
    Provides methods to register, retrieve, and list tools that can be used
    by the agent orchestrator.
    """
    
    def __init__(self):
        """Initialise the tools registry."""
        self.tools: Dict[str, BaseTool] = {}
        logger.info("Tools registry initialised")
    
    def register_tool(self, tool: BaseTool) -> bool:
        """
        Register a tool in the registry.
        
        Args:
            tool: Tool instance to register
            
        Returns:
            True if registration successful, False otherwise
        """
        try:
            tool_name = tool.get_name()
            if tool_name in self.tools:
                logger.warning(f"Tool '{tool_name}' is already registered, overwriting")
            
            self.tools[tool_name] = tool
            logger.info(f"✅ Tool '{tool_name}' registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error registering tool: {e}")
            return False
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """
        Get a tool by name.
        
        Args:
            name: Name of the tool to retrieve
            
        Returns:
            Tool instance if found, None otherwise
        """
        return self.tools.get(name)
    
    def list_tools(self) -> List[str]:
        """
        List all registered tool names.
        
        Returns:
            List of tool names
        """
        return list(self.tools.keys())
    
    def get_tool_count(self) -> int:
        """
        Get the number of registered tools.
        
        Returns:
            Number of tools in registry
        """
        return len(self.tools)
    
    def unregister_tool(self, name: str) -> bool:
        """
        Unregister a tool from the registry.
        
        Args:
            name: Name of the tool to unregister
            
        Returns:
            True if unregistration successful, False otherwise
        """
        try:
            if name in self.tools:
                del self.tools[name]
                logger.info(f"✅ Tool '{name}' unregistered successfully")
                return True
            else:
                logger.warning(f"Tool '{name}' not found in registry")
                return False
                
        except Exception as e:
            logger.error(f"Error unregistering tool '{name}': {e}")
            return False
    
    def clear(self):
        """Clear all tools from the registry."""
        self.tools.clear()
        logger.info("Tools registry cleared")
