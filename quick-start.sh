#!/bin/bash

# 🚀 Shoeby Agent Quick Start Script
# This script will set up your local development environment

set -e  # Exit on any error

echo "🛍️ Welcome to Shoeby Agent Quick Start!"
echo "========================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ and try again."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    echo "❌ Python $PYTHON_VERSION is not supported. Please install Python 3.8+ and try again."
    exit 1
fi

echo "✅ Python $PYTHON_VERSION detected"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "🔧 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip install -r requirements.txt

# Start databases with Docker Compose
echo "🐳 Starting databases with Docker Compose..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Test MongoDB connection
echo "🔍 Testing MongoDB connection..."
if docker exec shoeby-mongodb mongosh --quiet --eval "db.runCommand('ping')" > /dev/null 2>&1; then
    echo "✅ MongoDB is ready"
else
    echo "❌ MongoDB is not ready yet. Waiting a bit more..."
    sleep 10
    if docker exec shoeby-mongodb mongosh --quiet --eval "db.runCommand('ping')" > /dev/null 2>&1; then
        echo "✅ MongoDB is ready"
    else
        echo "❌ MongoDB failed to start. Check docker-compose logs."
        exit 1
    fi
fi

# Test Weaviate connection
echo "🔍 Testing Weaviate connection..."
if curl -s http://localhost:8080/v1/.well-known/ready > /dev/null; then
    echo "✅ Weaviate is ready"
else
    echo "❌ Weaviate is not ready yet. Waiting a bit more..."
    sleep 10
    if curl -s http://localhost:8080/v1/.well-known/ready > /dev/null; then
        echo "✅ Weaviate is ready"
    else
        echo "❌ Weaviate failed to start. Check docker-compose logs."
        exit 1
    fi
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "🔧 Creating .env file from template..."
    cp env.example .env
    echo "📝 Please edit .env file with your API keys and configuration"
    echo "   You can do this now or later, but the agent won't work without proper API keys"
fi

# Test basic Python imports
echo "🔍 Testing Python imports..."
if python3 -c "from config.settings import AgentConfig; print('✅ Configuration loaded successfully')" 2>/dev/null; then
    echo "✅ Python setup is working"
else
    echo "❌ Python setup failed. Check the error above."
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo "================================"
echo ""
echo "🚀 To start the agent:"
echo "   source venv/bin/activate"
echo "   python main.py"
echo ""
echo "🌐 To start the API server:"
echo "   source venv/bin/activate"
echo "   python api.py"
echo ""
echo "🎨 To start the frontend:"
echo "   cd frontend"
echo "   npm install"
echo "   npm run dev"
echo ""
echo "📊 To view running services:"
echo "   docker-compose ps"
echo ""
echo "📝 Don't forget to:"
echo "   1. Edit .env file with your API keys"
echo "   2. Restart the agent after updating .env"
echo ""
echo "🆘 For help, see SETUP.md or run:"
echo "   cat SETUP.md"
echo ""
echo "Happy coding! 🎉"
