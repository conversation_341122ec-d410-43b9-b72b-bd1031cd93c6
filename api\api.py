# api.py
"""
FastAPI implementation of the Shoeby Conversational Agent

ENHANCED LOGGING FEATURES:
- 🔍 Detailed Weaviate search parameter logging
- 📊 Search execution timing and result counts
- 🔧 Filter construction and combination logging
- 📈 Search broadening and retry attempt tracking
- 🧪 Debug endpoint for testing searches directly
- 🚀 Comprehensive search flow monitoring

To see detailed Weaviate search logs, run the API and:
1. Send chat messages that trigger searches
2. Use /api/debug/test-search to test searches directly
3. Check console output for detailed search information
"""

import logging
import sys
import os

# Add parent directory to Python path when running from api folder
if __name__ == "__main__":
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

from observability.core import init_observability
# Import and register routers
from api.routers.health import router as health_router
from api.routers.debug import router as debug_router
from api.routers.basket import router as basket_router
from api.routers.chat import router as chat_router

from main import ShoebyAgent
from config.settings import AgentConfig
from contextlib import asynccontextmanager
# Import dependencies
from api.dependencies import agent, set_agent

# Typing imports now handled in models
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI
try:
    from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
except Exception:  # package may not be installed yet; instrumentation is optional
    FastAPIInstrumentor = None  # type: ignore


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%d/%m/%Y %H:%M:%S",
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events"""
    # Startup
    logger.info("Starting Shoeby Agent API...")
    config = AgentConfig()
    agent_instance = ShoebyAgent(config)

    setup_success = await agent_instance.setup()
    if not setup_success:
        logger.error("Failed to initialise agent services")
        raise RuntimeError("Agent initialisation failed")

    # Set the global agent instance
    set_agent(agent_instance)
    init_observability(config.observability_provider_name)

    logger.info("🚀 Shoeby Agent API ready!")
    yield

    # Shutdown
    logger.info("Shutting down Shoeby Agent API...")
    if agent:
        await agent.cleanup()


app = FastAPI(
    title="Shoeby Conversational Agent API",
    description=(
        "Intelligent fashion assistant with persistent conversation history"
    ),
    version="1.0.0",
    lifespan=lifespan,
)

# Instrument FastAPI routes for OpenTelemetry traces if OTEL is enabled
if FastAPIInstrumentor is not None:
    try:
        FastAPIInstrumentor.instrument_app(app)
        logger.info("OpenTelemetry FastAPI instrumentation enabled")
    except Exception as e:
        logger.debug(f"FastAPI instrumentation skipped or failed: {e}")


# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React dev server
        "http://localhost:5173",  # Vite dev server
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        # Add your production domains here
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Models now imported in individual routers
app.include_router(health_router)
app.include_router(debug_router)
app.include_router(basket_router)
app.include_router(chat_router)


if __name__ == "__main__":
    import uvicorn
    # Read host, port, and log level from configuration
    cfg = AgentConfig()
    uvicorn.run(
        app,
        host=cfg.api_host,
        port=cfg.api_port,
        log_level=cfg.api_log_level,
    )
