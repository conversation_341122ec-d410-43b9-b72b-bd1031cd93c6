# services/model_providers.py
"""
Model provider abstraction layer for BrainPowa LLM service.
Supports BrainPowa models with consistent interfaces.
"""
import json
import logging

from enum import Enum
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from config.settings import AgentConfig

logger = logging.getLogger(__name__)


class ModelProvider(str, Enum):
    """Available model providers."""
    BRAINPOWA = "brainpowa-specialised"
    BRAINPOWA_REASONING = "brainpowa-reasoning"
    OPENAI = "openai"


class BaseModelProvider(ABC):
    """
    Abstract base class for all model providers.
    Ensures consistent interface across different LLM services.
    """
    
    def __init__(self, config: AgentConfig):
        """Initialise the provider with configuration."""
        self.config = config
        
    @abstractmethod
    def generate_structured_response(self, prompt: str, schema: Dict[str, Any], 
                                   task_type: str, model_override: Optional[str] = None, 
                                   temperature: Optional[float] = None,
                                   system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a structured JSON response from the model.
        
        Args:
            prompt: The prompt to send
            schema: Expected JSON schema for response validation
            task_type: Type of task for model selection
            model_override: Specific model to use instead of default selection
            temperature: Temperature setting for response generation (optional)
            system_prompt: Optional custom system prompt (if None, will use task-appropriate default)
            
        Returns:
            Dictionary containing structured response or error information
        """
        pass
    
    def generate_text(self, prompt: str, model_override: Optional[str] = None, 
                     temperature: Optional[float] = None) -> str:
        """
        Generate plain text response from the model.
        
        Args:
            prompt: The prompt to send
            model_override: Specific model to use instead of default selection
            temperature: Temperature setting for response generation (optional)
            
        Returns:
            str: Generated text response
        """
        # Default implementation - override in subclasses
        raise NotImplementedError("generate_text not implemented for this provider")
    
    
    def get_system_prompt_for_task(self, task_type: str, schema: Dict[str, Any]) -> str:
        """
        Get an appropriate system prompt for the given task type.
        
        Args:
            task_type: Type of task being performed
            schema: JSON schema for response validation
            
        Returns:
            str: Appropriate system prompt for the task
        """
        # Default system prompt for general tasks
        base_prompt = f"""You are an AI assistant. You must respond with valid JSON that follows this exact schema:

{json.dumps(schema, indent=2)}

CRITICAL: Always respond with a complete JSON OBJECT (starting with {{), NOT a JSON array (starting with [).
The response must be a single object containing the required fields."""

        # Task-specific system prompts
        task_prompts = {
            "strategy": f"""You are a strategy agent for Shoeby fashion retail. Analyse the customer's query and determine the next best actions using available tools.

You must respond with valid JSON that follows this exact schema:
{json.dumps(schema, indent=2)}

CRITICAL: Always respond with a complete JSON OBJECT (starting with {{), NOT a JSON array (starting with [).
The response must be a single object containing the required fields.""",
            
            "presentation": f"""You are a fashion retail assistant for Shoeby. Present information clearly and helpfully to customers.

You must respond with valid JSON that follows this exact schema:
{json.dumps(schema, indent=2)}

CRITICAL: Always respond with a complete JSON OBJECT (starting with {{), NOT a JSON array (starting with [).
The response must be a single object containing the required fields.""",
            
            "search": f"""You are a search and query specialist. Help construct effective search queries and evaluate results.

You must respond with valid JSON that follows this exact schema:
{json.dumps(schema, indent=2)}

CRITICAL: Always respond with a complete JSON OBJECT (starting with {{), NOT a JSON array (starting with [).
The response must be a single object containing the required fields.""",
            
            "evaluation": f"""You are an evaluation specialist. Analyse and assess information objectively.

You must respond with valid JSON that follows this exact schema:
{json.dumps(schema, indent=2)}

CRITICAL: Always respond with a complete JSON OBJECT (starting with {{), NOT a JSON array (starting with [).
The response must be a single object containing the required fields."""
        }
        
        return task_prompts.get(task_type, base_prompt)
    
    @abstractmethod
    def get_model_for_task(self, task_type: str) -> str:
        """Get the appropriate model for a given task type."""
        pass


class BrainPowaSpecialisedProvider(BaseModelProvider):
    """
    BrainPowa specialised model provider using OpenAI-compatible API.
    Supports different BrainPowa models for different task types.
    """
    
    def __init__(self, config: AgentConfig):
        """Initialise BrainPowa specialised provider with API configuration."""
        super().__init__(config)
        
        # Use custom base URL for BrainPowa if available, otherwise OpenAI URL
        base_url = getattr(config, 'brainpowa_base_url', None) or "https://brainpowa-model-garden.dev.az.rezolve.com/v1"
        api_key = getattr(config, 'brainpowa_api_key', None) or config.openai_api_key
        
        from openai import OpenAI
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            default_headers={"X-API-Key": api_key}
        )
        
        logger.info("BrainPowa specialised provider initialised with custom models")
    
    
    def get_model_for_task(self, task_type: str) -> str:
        """
        Get the appropriate BrainPowa model for the task.
        
        Args:
            task_type (str): Type of task to perform
            
        Returns:
            str: Model name to use for the task
        """
        # Return a default model since we don't use this method
        return "brainpowa-single-gpu-27B"
    
    def generate_structured_response(self, prompt: str, schema: Dict[str, Any], 
                                   task_type: str, model_override: Optional[str] = None, 
                                   temperature: Optional[float] = None,
                                   system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate structured response using BrainPowa models.
        
        Args:
            prompt (str): The prompt to send to the model
            schema (Dict[str, Any]): Expected JSON schema for response validation
            task_type (str): Type of task for model selection
            model_override (Optional[str]): Specific model to use instead of default selection (ignored)
            temperature (Optional[float]): Temperature setting for response generation
            system_prompt (Optional[str]): Optional custom system prompt (if None, will use task-appropriate default)
            
        Returns:
            Dict[str, Any]: Dictionary containing structured response or error information
        """
        # Define models for each task type with brainpowa-single-gpu-27B as final fallback
        model_mapping = {
            "strategy": ["brainpowa-single-gpu-27B"],
            "ask_follow_up_question": ["brainpowa-retail-chat-v1-7B", "brainpowa-single-gpu-27B"],
            "construct_weaviate_query": ["brainpowa-single-gpu-27B"],
            "weaviate_query": ["brainpowa-single-gpu-27B"],  
            "evaluate_results": ["brainpowa-retail-chat-v1-7B", "brainpowa-single-gpu-27B"],
            "present_results": ["brainpowa-education-128K", "brainpowa-retail-chat-v1-7B", "brainpowa-single-gpu-27B"],
            "presentation": ["brainpowa-education-128K", "brainpowa-retail-chat-v1-7B", "brainpowa-single-gpu-27B"],
            "suggest_complementary_products": ["brainpowa-retail-chat-v1-7B", "brainpowa-single-gpu-27B"],
            "redirect_irrelevant_query": ["brainpowa-retail-chat-v1-7B", "brainpowa-single-gpu-27B"]
        }
        
        # Get the list of models to try for the task
        # Note: model_override is ignored as this provider uses its own model selection logic
        models_to_try = model_mapping.get(task_type, [])
        
        # Ensure brainpowa-single-gpu-27B is always the final fallback
        if "brainpowa-single-gpu-27B" not in models_to_try:
            models_to_try.append("brainpowa-single-gpu-27B")
        
        # Try each model until one works
        for attempt, model in enumerate(models_to_try):
            try:
                logger.info(f"🧠 BrainPowa Specialised - Attempt {attempt + 1} with model: {model}")
                
                result = self._try_single_model(prompt, schema, task_type, model, temperature, system_prompt)
                
                # If we get a valid result (not an error), return it
                if "error" not in result:
                    logger.info(f"✅ BrainPowa Specialised SUCCESS - Model: {model} completed task: {task_type}")
                    return result
                
                # If this was the last attempt, return the error
                if attempt == len(models_to_try) - 1:
                    logger.error(f"❌ All BrainPowa Specialised models failed for task: {task_type}")
                    logger.error(f"Final error: {result.get('error', 'Unknown error')}")
                    return result
                
                logger.warning(f"⚠️ Model {model} failed, trying next model...")
                
            except Exception as e:
                logger.error(f"Exception with model {model}: {e}")
                if attempt == len(models_to_try) - 1:
                    return {"error": f"All BrainPowa Specialised models failed: {str(e)}"}
        
        return {"error": "Unexpected error in BrainPowa Specialised provider"}
    
    def generate_text(self, prompt: str, model_override: Optional[str] = None, 
                     temperature: Optional[float] = None) -> str:
        """Generate plain text response using BrainPowa models."""
        try:
            # Use the default model for text generation
            model = "brainpowa-single-gpu-27B"
            
            # Use provided temperature or default to 0.7 for text generation
            temp = temperature if temperature is not None else 0.7
            
            # Debug: Print text generation call details
            print(f"\n{'='*60}")
            print(f"🔧 BRAINPOWA TEXT GENERATION")
            print(f"Model: {model}")
            print(f"Temperature: {temp}")
            print(f"{'='*60}")
            print(f"📝 PROMPT:")
            print(prompt)
            print(f"{'='*60}")
            
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=temp,
                max_tokens=2000
            )
            
            if not response.choices:
                print(f"❌ BRAINPOWA TEXT ERROR: No response choices returned")
                print(f"{'='*60}\n")
                return "No response generated"
            
            content = response.choices[0].message.content
            
            # Debug: Print text response
            print(f"📤 BRAINPOWA TEXT RESPONSE:")
            print(content)
            print(f"{'='*60}\n")
            return content.strip() if content else "No response generated"
                        
        except Exception as e:
            logger.error(f"BrainPowa text generation error: {e}")
            return f"Error generating text: {str(e)}"
    
    
    def _try_single_model(self, prompt: str, schema: Dict[str, Any], task_type: str, model: str, temperature: Optional[float] = None, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Try to generate a response using a single BrainPowa model.
        
        Args:
            prompt (str): The prompt to send
            schema (Dict[str, Any]): Expected JSON schema
            task_type (str): Type of task
            model (str): Model to use
            temperature (float): Temperature setting for response generation
            system_prompt (Optional[str]): Optional custom system prompt
            
        Returns:
            Dict[str, Any]: Response or error information
        """
        try:
            # Use provided system prompt or get task-appropriate default
            system_message = system_prompt or self.get_system_prompt_for_task(task_type, schema)

            # Debug: Print API call details
            print(f"\n{'='*60}")
            print(f"🔧 BRAINPOWA API CALL - {task_type.upper()}")
            print(f"Model: {model}")
            print(f"Temperature: {temperature or 1}")
            print(f"{'='*60}")
            print(f"📝 SYSTEM MESSAGE:")
            print(system_message)
            print(f"{'='*60}")
            print(f"📝 USER PROMPT:")
            print(prompt)
            print(f"{'='*60}")

            # Try with response_format first (for models that support it)
            try:
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": prompt}
                    ],
                    response_format={"type": "json_object"},
                    temperature=temperature or 1,
                    max_tokens=4000  # Ensure we get complete responses
                )
            except Exception as format_error:
                # If response_format fails, try without it
                logger.warning(f"Response format failed for {model}, trying without it: {format_error}")
                try:
                    response = self.client.chat.completions.create(
                        model=model,
                        messages=[
                            {"role": "system", "content": system_message},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=temperature or 1,
                        max_tokens=4000  # Ensure we get complete responses
                    )
                except Exception as fallback_error:
                    logger.error(f"Both response format attempts failed for {model}: {fallback_error}")
                    return {"error": f"Model {model} failed to respond: {str(fallback_error)}"}
            
            if not response.choices:
                print(f"❌ BRAINPOWA API ERROR: No response choices returned")
                print(f"{'='*60}\n")
                return {"error": "No response choices returned from BrainPowa API"}
            
            content = response.choices[0].message.content
            
            # Debug: Print raw response
            print(f"📤 BRAINPOWA API RESPONSE:")
            print(content)
            print(f"{'='*60}")
            
            if not content or not content.strip():
                print(f"❌ BRAINPOWA API ERROR: Empty content from model {model}")
                print(f"{'='*60}\n")
                logger.error(f"Empty content from BrainPowa model: {model}")
                return {"error": "Empty response content from BrainPowa API"}
                        
            try:
                result = json.loads(content.strip())
                
                # Debug: Print parsed result
                print(f"📋 PARSED RESULT:")
                print(result)
                print(f"{'='*60}\n")
                
                # Ensure result is a dictionary, not a list
                if isinstance(result, list):
                    print(f"❌ BRAINPOWA API ERROR: Model returned list instead of dict: {result}")
                    print(f"{'='*60}\n")
                    logger.error(f"BrainPowa {model} returned list instead of dict: {result}")
                    return {"error": "Model returned list instead of dict. Expected JSON object, got array.", "model_used": model}
                
                # Detect schema-like echo or missing required fields
                if (isinstance(result, dict) and "type" in result and "properties" in result and "response_text" not in result):
                    print(f"❌ BRAINPOWA API ERROR: Model returned schema-like object instead of response: keys={list(result.keys())}")
                    print(f"{'='*60}\n")
                    logger.error(f"BrainPowa {model} returned schema-like object instead of response: keys={list(result.keys())}")
                    return {"error": "Model returned schema-like object instead of content", "model_used": model}
                if isinstance(result, dict) and task_type == "presentation" and "response_text" not in result:
                    print(f"❌ BRAINPOWA API ERROR: Missing 'response_text' in output for presentation task: keys={list(result.keys())}")
                    print(f"{'='*60}\n")
                    logger.error(f"BrainPowa {model} missing 'response_text' in output for presentation task: keys={list(result.keys())}")
                    return {"error": "Missing 'response_text' in model output", "model_used": model}
                
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON Parse Error from BrainPowa {model}: {e}")
                logger.error(f"Content: '{content[:200]}'")
                
                # Try to repair common JSON issues
                repaired_content = self._attempt_json_repair(content)
                if repaired_content:
                    try:
                        result = json.loads(repaired_content)
                        
                        # Ensure repaired result is also a dictionary
                        if isinstance(result, list):
                            logger.error(f"BrainPowa {model} repaired JSON still returns list: {result}")
                            return {"error": "Repaired JSON still returns list instead of dict", "model_used": model}
                        
                        logger.info(f"JSON repaired successfully for BrainPowa {model}")
                        return result
                    except json.JSONDecodeError:
                        logger.error(f"JSON repair failed for BrainPowa {model}")
                
                return {"error": f"JSON parsing failed: {str(e)}", "model_used": model}
                
        except Exception as e:
            logger.error(f"BrainPowa API error: {e}")
            return {"error": f"BrainPowa processing failed: {str(e)}"}
    
    def _attempt_json_repair(self, content: str):
        """
        Attempt to repair common JSON issues in LLM responses.
        
        Args:
            content: Raw content from the model
            
        Returns:
            Repaired JSON string or empty string if repair failed
        """
        try:
            # Remove markdown code blocks
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            
            # Find the first { and try to build a complete JSON object
            start = content.find("{")
            if start == -1:
                return ""
            
            # Start building the JSON from the first {
            json_content = content[start:]
            
            # Try to fix common issues
            json_content = json_content.replace("'", '"')  # Replace single quotes with double quotes
            json_content = json_content.replace("None", "null")  # Replace Python None with JSON null
            json_content = json_content.replace("True", "true")  # Replace Python True with JSON true
            json_content = json_content.replace("False", "false")  # Replace Python False with JSON false
            
            # Handle incomplete conversation_history field (most common issue)
            if '"conversation_history": "' in json_content:
                conv_start = json_content.find('"conversation_history": "')
                if conv_start != -1:
                    # Find the next quote after conversation_history starts
                    conv_value_start = conv_start + 25  # length of '"conversation_history": "'
                    next_quote = json_content.find('"', conv_value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, complete it
                        json_content += 'No previous conversation"'
                        logger.info("Completed incomplete conversation_history field")
                    else:
                        # Check if the value is incomplete (ends abruptly)
                        conv_value = json_content[conv_value_start:next_quote]
                        # Look for common truncation patterns
                        if any(conv_value.endswith(trunc) for trunc in ['previou', 'conversat', 'convers', 'prev', 'con']):
                            # Value was cut off, complete it
                            json_content = json_content[:next_quote] + 'No previous conversation"' + json_content[next_quote:]
                            logger.info("Completed truncated conversation_history field")
            
            # Ensure conversation_history field exists if it's missing
            if '"conversation_history"' not in json_content:
                # Find the last field and add conversation_history before closing
                last_comma = json_content.rfind(',')
                if last_comma != -1:
                    # Add conversation_history field before the last comma
                    json_content = json_content[:last_comma] + ', "conversation_history": "No previous conversation"' + json_content[last_comma:]
                    logger.info("Added missing conversation_history field")
                else:
                    # No comma found, add before closing brace
                    if json_content.endswith('}'):
                        json_content = json_content[:-1] + ', "conversation_history": "No previous conversation"}'
                        logger.info("Added missing conversation_history field before closing brace")
            
            # Handle incomplete recent_actions field
            if '"recent_actions": "' in json_content:
                actions_start = json_content.find('"recent_actions": "')
                if actions_start != -1:
                    # Find the next quote after recent_actions starts
                    actions_value_start = actions_start + 20  # length of '"recent_actions": "'
                    next_quote = json_content.find('"', actions_value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, complete it
                        json_content += 'none"'
                        logger.info("Completed incomplete recent_actions field")
                    else:
                        # Check if the value is incomplete
                        actions_value = json_content[actions_value_start:next_quote]
                        if any(actions_value.endswith(trunc) for trunc in ['act', 'rec', 'recent', 'acti']):
                            # Value was cut off, complete it
                            json_content = json_content[:next_quote] + 'none"' + json_content[next_quote:]
                            logger.info("Completed truncated recent_actions field")
            
            # Ensure recent_actions field exists if it's missing
            if '"recent_actions"' not in json_content:
                # Find the last field and add recent_actions before closing
                last_comma = json_content.rfind(',')
                if last_comma != -1:
                    # Add recent_actions field before the last comma
                    json_content = json_content[:last_comma] + ', "recent_actions": "none"' + json_content[last_comma:]
                    logger.info("Added missing recent_actions field")
                else:
                    # No comma found, add before closing brace
                    if json_content.endswith('}'):
                        json_content = json_content[:-1] + ', "recent_actions": "none"}'
                        logger.info("Added missing recent_actions field before closing brace")
            
            # Handle incomplete reasoning field
            if '"reasoning": "' in json_content:
                reasoning_start = json_content.find('"reasoning": "')
                if reasoning_start != -1:
                    # Find the next quote after reasoning starts
                    value_start = reasoning_start + 14  # length of '"reasoning": "'
                    next_quote = json_content.find('"', value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, add a reasonable default
                        json_content += 'Customer request analysed for appropriate action selection"'
                        logger.info("Completed incomplete reasoning field")
            
            # Try to close any unclosed objects/arrays
            open_braces = json_content.count('{') - json_content.count('}')
            open_brackets = json_content.count('[') - json_content.count(']')
            
            if open_braces > 0:
                json_content += '}' * open_braces
                logger.info(f"Added {open_braces} closing braces")
            
            if open_brackets > 0:
                json_content += ']' * open_brackets
                logger.info(f"Added {open_brackets} closing brackets")
            
            # Log the repaired JSON for debugging
            logger.info(f"JSON after repair attempts: {json_content[:300]}...")
            
            # Try to validate the JSON by parsing it
            try:
                import json
                json.loads(json_content)
                logger.info("JSON repaired successfully for BrainPowa brainpowa-single-gpu-27B")
                return json_content.strip()
            except json.JSONDecodeError as e:
                logger.warning(f"JSON still invalid after repair: {e}")
                
                # If still invalid, try a more aggressive repair approach
                # Look for the last complete field and close everything after it
                last_complete_field = json_content.rfind('"')
                if last_complete_field != -1:
                    # Find the last complete field structure
                    # Look for patterns like "field": "value"
                    pattern = '": "'
                    last_pattern = json_content.rfind(pattern)
                    if last_pattern != -1:
                        # Close the last string and add missing fields
                        json_content = json_content[:last_pattern + len(pattern)] + 'default value"'
                        
                        # Add missing fields if they don't exist
                        if '"reasoning"' not in json_content:
                            json_content += ', "reasoning": "Request processed based on available context"'
                        
                        if '"recent_actions"' not in json_content:
                            json_content += ', "recent_actions": "none"'
                        
                        if '"conversation_history"' not in json_content:
                            json_content += ', "conversation_history": "No previous conversation"'
                        
                        # Ensure tool_parameters structure is complete
                        if '"tool_parameters"' in json_content and '"ask_follow_up_question"' in json_content:
                            # Check if ask_follow_up_question has all required fields
                            if '"user_query"' not in json_content:
                                json_content += ', "user_query": "customer request"'
                        
                        # Close the object
                        if not json_content.endswith('}'):
                            json_content += '}'
                        
                        logger.info("Applied aggressive JSON repair")
                        
                        # Try parsing again
                        try:
                            json.loads(json_content)
                            logger.info("JSON repaired with aggressive approach")
                            return json_content.strip()
                        except json.JSONDecodeError:
                            pass
                
                # Final fallback: return a minimal valid JSON
                fallback_json = '{"next_best_actions": ["ask_follow_up_question"], "tool_parameters": {"ask_follow_up_question": {"user_query": "test", "conversation_history": "No previous conversation", "recent_actions": "none"}}, "reasoning": "Customer request requires clarification before proceeding with product search. Following up to gather more details about preferences, style, occasion, and requirements.", "irrelevant_query": false}'
                logger.info("Using fallback JSON due to repair failure")
                return fallback_json
                
        except Exception as e:
            logger.error(f"JSON repair failed: {e}")
            # Return fallback JSON
            fallback_json = '{"next_best_actions": ["ask_follow_up_question"], "tool_parameters": {"ask_follow_up_question": {"user_query": "test", "conversation_history": "No previous conversation", "recent_actions": "none"}}, "reasoning": "Customer request requires clarification before proceeding with product search. Following up to gather more details about preferences, style, occasion, and requirements.", "irrelevant_query": false}'
            return fallback_json


class BrainPowaReasoningProvider(BaseModelProvider):
    """
    BrainPowa reasoning model provider using OpenAI-compatible API.
    Uses brainpowa-os-reasoning-120B for harder tasks and brainpowa-os-reasoning-20B for easier tasks.
    """
    
    def __init__(self, config: AgentConfig):
        """Initialise BrainPowa reasoning provider with API configuration."""
        super().__init__(config)
        
        # Use custom base URL for BrainPowa if available, otherwise OpenAI URL
        base_url = getattr(config, 'brainpowa_base_url', None) or "https://brainpowa-model-garden.dev.az.rezolve.com/v1"
        api_key = getattr(config, 'brainpowa_api_key', None) or config.openai_api_key
        
        from openai import OpenAI
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            default_headers={"X-API-Key": api_key}
        )
        
        # Map task types to reasoning models - 120B for harder tasks, 20B for easier tasks
        self.model_mapping = {
            "strategy": "brainpowa-os-reasoning-120B",      # Harder: Strategy decisions
            "search": "brainpowa-os-reasoning-20B",         # Easier: Search query construction
            "weaviate_query": "brainpowa-os-reasoning-20B", # Easier: Weaviate query construction
            "evaluation": "brainpowa-os-reasoning-120B",    # Harder: Result evaluation and analysis
            "presentation": "brainpowa-os-reasoning-20B",   # Easier: Result presentation
            "followup": "brainpowa-os-reasoning-20B",       # Easier: Follow-up questions
            "continuity": "brainpowa-os-reasoning-20B",     # Easier: Conversation continuity
            "complementary": "brainpowa-os-reasoning-20B",  # Easier: Complementary products
            "irrelevant": "brainpowa-os-reasoning-20B",     # Easier: Irrelevant query handling
            "purchase": "brainpowa-os-reasoning-20B",       # Easier: Purchase prompts
        }
        
        logger.info("BrainPowa reasoning provider initialised with reasoning models")
    
    def get_model_for_task(self, task_type: str) -> str:
        """
        Select the appropriate BrainPowa reasoning model for the task.
        
        Args:
            task_type (str): Type of task to perform
            
        Returns:
            str: Model name to use for the task
        """
        # Get the reasoning model for the task
        reasoning_model = self.model_mapping.get(task_type, "brainpowa-os-reasoning-20B")
        
        return reasoning_model
    
    def generate_structured_response(self, prompt: str, schema: Dict[str, Any], 
                                   task_type: str, model_override: Optional[str] = None, 
                                   temperature: Optional[float] = None,
                                   system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate structured response using BrainPowa reasoning models with fallback support.
        
        Args:
            prompt (str): The prompt to send to the model
            schema (Dict[str, Any]): Expected JSON schema for response validation
            task_type (str): Type of task for model selection
            model_override (Optional[str]): Specific model to use instead of default selection
            temperature (Optional[float]): Temperature setting for response generation
            system_prompt (Optional[str]): Optional custom system prompt (if None, will use task-appropriate default)
            
        Returns:
            Dict[str, Any]: Dictionary containing structured response or error information
        """
        # Define fallback models for each task type - reasoning models first, then fallback to specialised models
        model_mapping = {
            "strategy": ["brainpowa-os-reasoning-120B", "brainpowa-os-reasoning-20B", "brainpowa-single-gpu-27B"],
            "search": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"],
            "weaviate_query": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"],  # Handle weaviate query task type
            "evaluation": ["brainpowa-os-reasoning-120B", "brainpowa-os-reasoning-20B", "brainpowa-multi-gpu-14B"],
            "presentation": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"],
            "followup": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"],
            "continuity": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"],
            "complementary": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"],
            "irrelevant": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"],
            "purchase": ["brainpowa-os-reasoning-20B", "brainpowa-os-reasoning-120B", "brainpowa-multi-gpu-14B"]
        }
        
        # Get the list of models to try
        if model_override:
            models_to_try = [model_override]
        else:
            primary_model = self.get_model_for_task(task_type)
            models_to_try = [primary_model] + model_mapping.get(task_type, [])
        
        # Try each model until one works
        for attempt, model in enumerate(models_to_try):
            try:
                logger.info(f"🧠 BrainPowa reasoning attempt {attempt + 1} with model: {model}")
                
                result = self._try_single_model(prompt, schema, task_type, model, temperature, system_prompt)
                
                # If we get a valid result (not an error), return it
                if "error" not in result:
                    logger.info(f"✅ BrainPowa reasoning SUCCESS - Model: {model} completed task: {task_type}")
                    return result
                
                # If this was the last attempt, return the error
                if attempt == len(models_to_try) - 1:
                    logger.error(f"❌ All BrainPowa reasoning models failed for task: {task_type}")
                    return result
                
                logger.warning(f"⚠️ Model {model} failed, trying next model...")
                
            except Exception as e:
                logger.error(f"Exception with model {model}: {e}")
                if attempt == len(models_to_try) - 1:
                    return {"error": f"All BrainPowa reasoning models failed: {str(e)}"}
        
        return {"error": "Unexpected error in BrainPowa reasoning provider"}
    
    def generate_text(self, prompt: str, model_override: Optional[str] = None, 
                     temperature: Optional[float] = None) -> str:
        """Generate plain text response using BrainPowa reasoning models."""
        try:
            # Use the default model for text generation
            model = "brainpowa-os-reasoning-20B"
            
            # Use provided temperature or default to 0.7 for text generation
            temp = temperature if temperature is not None else 0.7
            
            # Debug: Print reasoning text generation call details
            print(f"\n{'='*60}")
            print(f"🧠 BRAINPOWA REASONING TEXT GENERATION")
            print(f"Model: {model}")
            print(f"Temperature: {temp}")
            print(f"{'='*60}")
            print(f"📝 PROMPT:")
            print(prompt)
            print(f"{'='*60}")
            
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=temp,
                max_tokens=2000
            )
            
            if not response.choices:
                print(f"❌ BRAINPOWA REASONING TEXT ERROR: No response choices returned")
                print(f"{'='*60}\n")
                return "No response generated"
            
            content = response.choices[0].message.content
            
            # Debug: Print reasoning text response
            print(f"📤 BRAINPOWA REASONING TEXT RESPONSE:")
            print(content)
            print(f"{'='*60}\n")
            return content.strip() if content else "No response generated"
                        
        except Exception as e:
            logger.error(f"BrainPowa reasoning text generation error: {e}")
            return f"Error generating text: {str(e)}"
    
    
    def _try_single_model(self, prompt: str, schema: Dict[str, Any], task_type: str, model: str, temperature: Optional[float] = None, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Try to generate a response using a single BrainPowa reasoning model.
        
        Args:
            prompt (str): The prompt to send
            schema (Dict[str, Any]): Expected JSON schema
            task_type (str): Type of task
            model (str): Model to use
            temperature (float): Temperature setting for response generation
            system_prompt (Optional[str]): Optional custom system prompt
            
        Returns:
            Dict[str, Any]: Response or error information
        """
        try:
            # Use provided system prompt or get task-appropriate default
            system_message = system_prompt or self.get_system_prompt_for_task(task_type, schema)

            # Debug: Print reasoning API call details
            print(f"\n{'='*60}")
            print(f"🧠 BRAINPOWA REASONING API CALL - {task_type.upper()}")
            print(f"Model: {model}")
            print(f"Temperature: {temperature if temperature is not None else 1.0}")
            print(f"{'='*60}")
            print(f"📝 SYSTEM MESSAGE:")
            print(system_message)
            print(f"{'='*60}")
            print(f"📝 USER PROMPT:")
            print(prompt)
            print(f"{'='*60}")

            if self.config.show_json:
                logger.info(f"🧠 BrainPowa reasoning request - Model: {model}, Task: {task_type}")
            else:
                logger.info(f"🧠 BrainPowa reasoning processing - Model: {model}, Task: {task_type}")
            
            # Use provided temperature or default to 1.0 for BrainPowa reasoning
            temp = temperature if temperature is not None else 1.0
            
            # Try with response_format first (for models that support it)
            try:
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": prompt}
                    ],
                    response_format={"type": "json_object"},
                    temperature=temp,
                    max_tokens=4000  # Ensure we get complete responses
                )
            except Exception as format_error:
                # If response_format fails, try without it
                logger.warning(f"Response format failed for {model}, trying without it: {format_error}")
                try:
                    response = self.client.chat.completions.create(
                        model=model,
                        messages=[
                            {"role": "system", "content": system_message},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=temp,
                        max_tokens=4000  # Ensure we get complete responses
                    )
                except Exception as fallback_error:
                    logger.error(f"Both response format attempts failed for {model}: {fallback_error}")
                    return {"error": f"Model {model} failed to respond: {str(fallback_error)}"}
            
            if not response.choices:
                print(f"❌ BRAINPOWA REASONING API ERROR: No response choices returned")
                print(f"{'='*60}\n")
                return {"error": "No response choices returned from BrainPowa reasoning API"}
            
            content = response.choices[0].message.content
            
            # Debug: Print raw response
            print(f"📤 BRAINPOWA REASONING API RESPONSE:")
            print(content)
            print(f"{'='*60}")
            
            if not content or not content.strip():
                print(f"❌ BRAINPOWA REASONING API ERROR: Empty content from model {model}")
                print(f"{'='*60}\n")
                logger.error(f"Empty content from BrainPowa reasoning model: {model}")
                return {"error": "Empty response content from BrainPowa reasoning API"}
                        
            try:
                result = json.loads(content.strip())
                
                # Debug: Print parsed result
                print(f"📋 PARSED REASONING RESULT:")
                print(result)
                print(f"{'='*60}\n")
                
                # Ensure result is a dictionary, not a list
                if isinstance(result, list):
                    print(f"❌ BRAINPOWA REASONING API ERROR: Model returned list instead of dict: {result}")
                    print(f"{'='*60}\n")
                    logger.error(f"BrainPowa reasoning {model} returned list instead of dict: {result}")
                    return {"error": "Model returned list instead of dict. Expected JSON object, got array.", "model_used": model}
                
                if self.config.show_json:
                    logger.info(f"BrainPowa reasoning success - Model: {model}")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON Parse Error from BrainPowa reasoning {model}: {e}")
                logger.error(f"Content: '{content[:200]}'")
                
                # Try to repair common JSON issues
                repaired_content = self._attempt_json_repair(content)
                if repaired_content:
                    try:
                        result = json.loads(repaired_content)
                        
                        # Ensure repaired result is also a dictionary
                        if isinstance(result, list):
                            logger.error(f"BrainPowa reasoning {model} repaired JSON still returns list: {result}")
                            return {"error": "Repaired JSON still returns list instead of dict", "model_used": model}
                        
                        logger.info(f"JSON repaired successfully for BrainPowa reasoning {model}")
                        return result
                    except json.JSONDecodeError:
                        logger.error(f"JSON repair failed for BrainPowa reasoning {model}")
                
                return {"error": f"JSON parsing failed: {str(e)}", "model_used": model}
                
        except Exception as e:
            logger.error(f"BrainPowa reasoning API error: {e}")
            return {"error": f"BrainPowa reasoning processing failed: {str(e)}"}
    
    def _attempt_json_repair(self, content: str):
        """
        Attempt to repair common JSON issues in LLM responses.
        
        Args:
            content: Raw content from the model
            
        Returns:
            Repaired JSON string or empty string if repair failed
        """
        try:
            # Remove markdown code blocks
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            
            # Find the first { and try to build a complete JSON object
            start = content.find("{")
            if start == -1:
                return ""
            
            # Start building the JSON from the first {
            json_content = content[start:]
            
            # Try to fix common issues
            json_content = json_content.replace("'", '"')  # Replace single quotes with double quotes
            json_content = json_content.replace("None", "null")  # Replace Python None with JSON null
            json_content = json_content.replace("True", "true")  # Replace Python True with JSON true
            json_content = json_content.replace("False", "false")  # Replace Python False with JSON false
            
            # Handle incomplete conversation_history field (most common issue)
            if '"conversation_history": "' in json_content:
                conv_start = json_content.find('"conversation_history": "')
                if conv_start != -1:
                    # Find the next quote after conversation_history starts
                    conv_value_start = conv_start + 25  # length of '"conversation_history": "'
                    next_quote = json_content.find('"', conv_value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, complete it
                        json_content += 'No previous conversation"'
                        logger.info("Completed incomplete conversation_history field")
                    else:
                        # Check if the value is incomplete (ends abruptly)
                        conv_value = json_content[conv_value_start:next_quote]
                        # Look for common truncation patterns
                        if any(conv_value.endswith(trunc) for trunc in ['previou', 'conversat', 'convers', 'prev', 'con']):
                            # Value was cut off, complete it
                            json_content = json_content[:next_quote] + 'No previous conversation"' + json_content[next_quote:]
                            logger.info("Completed truncated conversation_history field")
            
            # Ensure conversation_history field exists if it's missing
            if '"conversation_history"' not in json_content:
                # Find the last field and add conversation_history before closing
                last_comma = json_content.rfind(',')
                if last_comma != -1:
                    # Add conversation_history field before the last comma
                    json_content = json_content[:last_comma] + ', "conversation_history": "No previous conversation"' + json_content[last_comma:]
                    logger.info("Added missing conversation_history field")
                else:
                    # No comma found, add before closing brace
                    if json_content.endswith('}'):
                        json_content = json_content[:-1] + ', "conversation_history": "No previous conversation"}'
                        logger.info("Added missing conversation_history field before closing brace")
            
            # Handle incomplete recent_actions field
            if '"recent_actions": "' in json_content:
                actions_start = json_content.find('"recent_actions": "')
                if actions_start != -1:
                    # Find the next quote after recent_actions starts
                    actions_value_start = actions_start + 20  # length of '"recent_actions": "'
                    next_quote = json_content.find('"', actions_value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, complete it
                        json_content += 'none"'
                        logger.info("Completed incomplete recent_actions field")
                    else:
                        # Check if the value is incomplete
                        actions_value = json_content[actions_value_start:next_quote]
                        if any(actions_value.endswith(trunc) for trunc in ['act', 'rec', 'recent', 'acti']):
                            # Value was cut off, complete it
                            json_content = json_content[:next_quote] + 'none"' + json_content[next_quote:]
                            logger.info("Completed truncated recent_actions field")
            
            # Ensure recent_actions field exists if it's missing
            if '"recent_actions"' not in json_content:
                # Find the last field and add recent_actions before closing
                last_comma = json_content.rfind(',')
                if last_comma != -1:
                    # Add recent_actions field before the last comma
                    json_content = json_content[:last_comma] + ', "recent_actions": "none"' + json_content[last_comma:]
                    logger.info("Added missing recent_actions field")
                else:
                    # No comma found, add before closing brace
                    if json_content.endswith('}'):
                        json_content = json_content[:-1] + ', "recent_actions": "none"}'
                        logger.info("Added missing recent_actions field before closing brace")
            
            # Handle incomplete reasoning field
            if '"reasoning": "' in json_content:
                reasoning_start = json_content.find('"reasoning": "')
                if reasoning_start != -1:
                    # Find the next quote after reasoning starts
                    value_start = reasoning_start + 14  # length of '"reasoning": "'
                    next_quote = json_content.find('"', value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, add a reasonable default
                        json_content += 'Customer request analysed for appropriate action selection"'
                        logger.info("Completed incomplete reasoning field")
            
            # Try to close any unclosed objects/arrays
            open_braces = json_content.count('{') - json_content.count('}')
            open_brackets = json_content.count('[') - json_content.count(']')
            
            if open_braces > 0:
                json_content += '}' * open_braces
                logger.info(f"Added {open_braces} closing braces")
            
            if open_brackets > 0:
                json_content += ']' * open_brackets
                logger.info(f"Added {open_brackets} closing brackets")
            
            # Log the repaired JSON for debugging
            logger.info(f"JSON after repair attempts: {json_content[:300]}...")
            
            # Try to validate the JSON by parsing it
            try:
                import json
                json.loads(json_content)
                logger.info("JSON repaired successfully for BrainPowa reasoning brainpowa-reasoning-128K")
                return json_content.strip()
            except json.JSONDecodeError as e:
                logger.warning(f"JSON still invalid after repair: {e}")
                
                # If still invalid, try a more aggressive repair approach
                # Look for the last complete field and close everything after it
                last_complete_field = json_content.rfind('"')
                if last_complete_field != -1:
                    # Find the last complete field structure
                    # Look for patterns like "field": "value"
                    pattern = '": "'
                    last_pattern = json_content.rfind(pattern)
                    if last_pattern != -1:
                        # Close the last string and add missing fields
                        json_content = json_content[:last_pattern + len(pattern)] + 'default value"'
                        
                        # Add missing fields if they don't exist
                        if '"reasoning"' not in json_content:
                            json_content += ', "reasoning": "Request processed based on available context"'
                        
                        if '"recent_actions"' not in json_content:
                            json_content += ', "recent_actions": "none"'
                        
                        if '"conversation_history"' not in json_content:
                            json_content += ', "conversation_history": "No previous conversation"'
                        
                        # Ensure tool_parameters structure is complete
                        if '"tool_parameters"' in json_content and '"ask_follow_up_question"' in json_content:
                            # Check if ask_follow_up_question has all required fields
                            if '"user_query"' not in json_content:
                                json_content += ', "user_query": "customer request"'
                        
                        # Close the object
                        if not json_content.endswith('}'):
                            json_content += '}'
                        
                        logger.info("Applied aggressive JSON repair")
                        
                        # Try parsing again
                        try:
                            json.loads(json_content)
                            logger.info("JSON repaired with aggressive approach")
                            return json_content.strip()
                        except json.JSONDecodeError:
                            pass
                
                # Final fallback: return a minimal valid JSON
                fallback_json = '{"next_best_actions": ["ask_follow_up_question"], "tool_parameters": {"ask_follow_up_question": {"user_query": "test", "conversation_history": "No previous conversation", "recent_actions": "none"}}, "reasoning": "Customer request requires clarification before proceeding with product search. Following up to gather more details about preferences, style, occasion, and requirements.", "irrelevant_query": false}'
                logger.info("Using fallback JSON due to repair failure")
                return fallback_json
                
        except Exception as e:
            logger.error(f"JSON repair failed: {e}")
            # Return fallback JSON
            fallback_json = '{"next_best_actions": ["ask_follow_up_question"], "tool_parameters": {"ask_follow_up_question": {"user_query": "test", "conversation_history": "No previous conversation", "recent_actions": "none"}}, "reasoning": "Customer request requires clarification before proceeding with product search. Following up to gather more details about preferences, style, occasion, and requirements.", "irrelevant_query": false}'
            return fallback_json


class OpenAIProvider(BaseModelProvider):
    """
    OpenAI model provider using OpenAI's official API.
    """
    
    def __init__(self, config: AgentConfig):
        """Initialise OpenAI provider with API configuration."""
        super().__init__(config)
        
        # Use OpenAI API key
        api_key = config.openai_api_key
        
        if not api_key:
            raise ValueError("OpenAI API key is required for OpenAI provider")
        
        from openai import OpenAI
        self.client = OpenAI(
            api_key=api_key,
            # Use default OpenAI base URL
        )
        
        self.model_name = "gpt-4o"
        
        logger.info(f"OpenAI provider initialised with {self.model_name} model")
    
    def get_model_for_task(self, task_type: str) -> str:
        return self.model_name
    
    def generate_structured_response(self, prompt: str, schema: Dict[str, Any], 
                                   task_type: str, model_override: Optional[str] = None, 
                                   temperature: Optional[float] = None,
                                   system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """Generate structured response using OpenAI models."""
        try:
            model = model_override or self.get_model_for_task(task_type)
            
            # Use provided system prompt or get task-appropriate default
            system_message = system_prompt or self.get_system_prompt_for_task(task_type, schema)

            # Debug: Print OpenAI API call details
            print(f"\n{'='*60}")
            print(f"🤖 OPENAI API CALL - {task_type.upper()}")
            print(f"Model: {model}")
            print(f"Temperature: {temperature if temperature is not None else 0.5}")
            print(f"{'='*60}")
            print(f"📝 SYSTEM MESSAGE:")
            print(system_message)
            print(f"{'='*60}")
            print(f"📝 USER PROMPT:")
            print(prompt)
            print(f"{'='*60}")

            if self.config.show_json:
                logger.info(f"OpenAI request - Model: {model}, Task: {task_type}")
            
            # Use provided temperature or default to 0.5 for OpenAI
            temp = temperature if temperature is not None else 0.5
            
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                response_format={"type": "json_object"},
                temperature=temp,
                # max_tokens=4000  
            )
            
            if not response.choices:
                print(f"❌ OPENAI API ERROR: No response choices returned")
                print(f"{'='*60}\n")
                return {"error": "No response choices returned from OpenAI API"}
            
            content = response.choices[0].message.content
            
            if not content or not content.strip():
                print(f"❌ OPENAI API ERROR: Empty content from model {model}")
                print(f"{'='*60}\n")
                logger.error(f"Empty content from OpenAI model: {model}")
                return {"error": "Empty response content from OpenAI API"}
                        
            try:
                result = json.loads(content.strip())
       
                # Ensure result is a dictionary, not a list
                if isinstance(result, list):
                    print(f"❌ OPENAI API ERROR: Model returned list instead of dict: {result}")
                    print(f"{'='*60}\n")
                    logger.error(f"OpenAI {model} returned list instead of dict: {result}")
                    return {"error": "Model returned list instead of dict. Expected JSON object, got array.", "model_used": model}
                
                if self.config.show_json:
                    logger.info(f"OpenAI success - Model: {model}")
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON Parse Error from OpenAI {model}: {e}")
                logger.error(f"Content: '{content[:200]}'")
                
                # Try to repair common JSON issues
                repaired_content = self._attempt_json_repair(content)
                if repaired_content:
                    try:
                        result = json.loads(repaired_content)
                        
                        # Ensure repaired result is also a dictionary
                        if isinstance(result, list):
                            logger.error(f"OpenAI {model} repaired JSON still returns list: {result}")
                            return {"error": "Repaired JSON still returns list instead of dict", "model_used": model}
                        
                        logger.info(f"JSON repaired successfully for OpenAI {model}")
                        return result
                    except json.JSONDecodeError:
                        logger.error(f"JSON repair failed for OpenAI {model}")
                
                return {"error": f"JSON parsing failed: {str(e)}", "model_used": model}
                
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return {"error": f"OpenAI processing failed: {str(e)}"}
    
    def generate_text(self, prompt: str, model_override: Optional[str] = None, 
                     temperature: Optional[float] = None) -> str:
        """Generate plain text response using OpenAI models."""
        try:
            model = model_override or self.get_model_for_task("general")
            
            # Use provided temperature or default to 0.7 for text generation
            temp = temperature if temperature is not None else 0.7
            
            # Debug: Print OpenAI text generation call details
            print(f"\n{'='*60}")
            print(f"🤖 OPENAI TEXT GENERATION")
            print(f"Model: {model}")
            print(f"Temperature: {temp}")
            print(f"{'='*60}")
            print(f"📝 PROMPT:")
            print(prompt)
            print(f"{'='*60}")
            
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=temp,
                # max_tokens=2000
            )
            
            if not response.choices:
                print(f"❌ OPENAI TEXT ERROR: No response choices returned")
                print(f"{'='*60}\n")
                return "No response generated"
            
            content = response.choices[0].message.content
            
            # Debug: Print text response
            print(f"📤 OPENAI TEXT RESPONSE:")
            print(content)
            print(f"{'='*60}\n")
            return content.strip() if content else "No response generated"
                        
        except Exception as e:
            logger.error(f"OpenAI text generation error: {e}")
            return f"Error generating text: {str(e)}"
    
    
    def _attempt_json_repair(self, content: str):
        """
        Attempt to repair common JSON issues in LLM responses.
        
        Args:
            content: Raw content from the model
            
        Returns:
            Repaired JSON string or empty string if repair failed
        """
        try:
            # Remove markdown code blocks
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            
            # Find the first { and try to build a complete JSON object
            start = content.find("{")
            if start == -1:
                return ""
            
            # Start building the JSON from the first {
            json_content = content[start:]
            
            # Try to fix common issues
            json_content = json_content.replace("'", '"')  # Replace single quotes with double quotes
            json_content = json_content.replace("None", "null")  # Replace Python None with JSON null
            json_content = json_content.replace("True", "true")  # Replace Python True with JSON true
            json_content = json_content.replace("False", "false")  # Replace Python False with JSON false
            
            # Handle incomplete conversation_history field (most common issue)
            if '"conversation_history": "' in json_content:
                conv_start = json_content.find('"conversation_history": "')
                if conv_start != -1:
                    # Find the next quote after conversation_history starts
                    conv_value_start = conv_start + 25  # length of '"conversation_history": "'
                    next_quote = json_content.find('"', conv_value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, complete it
                        json_content += 'No previous conversation"'
                        logger.info("Completed incomplete conversation_history field")
                    else:
                        # Check if the value is incomplete (ends abruptly)
                        conv_value = json_content[conv_value_start:next_quote]
                        # Look for common truncation patterns
                        if any(conv_value.endswith(trunc) for trunc in ['previou', 'conversat', 'convers', 'prev', 'con']):
                            # Value was cut off, complete it
                            json_content = json_content[:next_quote] + 'No previous conversation"' + json_content[next_quote:]
                            logger.info("Completed truncated conversation_history field")
            
            # Ensure conversation_history field exists if it's missing
            if '"conversation_history"' not in json_content:
                # Find the last field and add conversation_history before closing
                last_comma = json_content.rfind(',')
                if last_comma != -1:
                    # Add conversation_history field before the last comma
                    json_content = json_content[:last_comma] + ', "conversation_history": "No previous conversation"' + json_content[last_comma:]
                    logger.info("Added missing conversation_history field")
                else:
                    # No comma found, add before closing brace
                    if json_content.endswith('}'):
                        json_content = json_content[:-1] + ', "conversation_history": "No previous conversation"}'
                        logger.info("Added missing conversation_history field before closing brace")
            
            # Handle incomplete recent_actions field
            if '"recent_actions": "' in json_content:
                actions_start = json_content.find('"recent_actions": "')
                if actions_start != -1:
                    # Find the next quote after recent_actions starts
                    actions_value_start = actions_start + 20  # length of '"recent_actions": "'
                    next_quote = json_content.find('"', actions_value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, complete it
                        json_content += 'none"'
                        logger.info("Completed incomplete recent_actions field")
                    else:
                        # Check if the value is incomplete
                        actions_value = json_content[actions_value_start:next_quote]
                        if any(actions_value.endswith(trunc) for trunc in ['act', 'rec', 'recent', 'acti']):
                            # Value was cut off, complete it
                            json_content = json_content[:next_quote] + 'none"' + json_content[next_quote:]
                            logger.info("Completed truncated recent_actions field")
            
            # Ensure recent_actions field exists if it's missing
            if '"recent_actions"' not in json_content:
                # Find the last field and add recent_actions before closing
                last_comma = json_content.rfind(',')
                if last_comma != -1:
                    # Add recent_actions field before the last comma
                    json_content = json_content[:last_comma] + ', "recent_actions": "none"' + json_content[last_comma:]
                    logger.info("Added missing recent_actions field")
                else:
                    # No comma found, add before closing brace
                    if json_content.endswith('}'):
                        json_content = json_content[:-1] + ', "recent_actions": "none"}'
                        logger.info("Added missing recent_actions field before closing brace")
            
            # Handle incomplete reasoning field
            if '"reasoning": "' in json_content:
                reasoning_start = json_content.find('"reasoning": "')
                if reasoning_start != -1:
                    # Find the next quote after reasoning starts
                    value_start = reasoning_start + 14  # length of '"reasoning": "'
                    next_quote = json_content.find('"', value_start)
                    
                    if next_quote == -1:
                        # No closing quote found, add a reasonable default
                        json_content += 'Customer request analysed for appropriate action selection"'
                        logger.info("Completed incomplete reasoning field")
            
            # Try to close any unclosed objects/arrays
            open_braces = json_content.count('{') - json_content.count('}')
            open_brackets = json_content.count('[') - json_content.count(']')
            
            if open_braces > 0:
                json_content += '}' * open_braces
                logger.info(f"Added {open_braces} closing braces")
            
            if open_brackets > 0:
                json_content += ']' * open_brackets
                logger.info(f"Added {open_brackets} closing brackets")
            
            # Log the repaired JSON for debugging
            logger.info(f"JSON after repair attempts: {json_content[:300]}...")
            
            # Try to validate the JSON by parsing it
            try:
                import json
                return json_content.strip()
            except json.JSONDecodeError as e:
                logger.warning(f"JSON still invalid after repair: {e}")
                
                # If still invalid, try a more aggressive repair approach
                # Look for the last complete field and close everything after it
                last_complete_field = json_content.rfind('"')
                if last_complete_field != -1:
                    # Find the last complete field structure
                    # Look for patterns like "field": "value"
                    pattern = '": "'
                    last_pattern = json_content.rfind(pattern)
                    if last_pattern != -1:
                        # Close the last string and add missing fields
                        json_content = json_content[:last_pattern + len(pattern)] + 'default value"'
                        
                        # Add missing fields if they don't exist
                        if '"reasoning"' not in json_content:
                            json_content += ', "reasoning": "Request processed based on available context"'
                        
                        if '"recent_actions"' not in json_content:
                            json_content += ', "recent_actions": "none"'
                        
                        if '"conversation_history"' not in json_content:
                            json_content += ', "conversation_history": "No previous conversation"'
                        
                        # Ensure tool_parameters structure is complete
                        if '"tool_parameters"' in json_content and '"ask_follow_up_question"' in json_content:
                            # Check if ask_follow_up_question has all required fields
                            if '"user_query"' not in json_content:
                                json_content += ', "user_query": "customer request"'
                        
                        # Close the object
                        if not json_content.endswith('}'):
                            json_content += '}'
                        
                        logger.info("Applied aggressive JSON repair")
                        
                        # Try parsing again
                        try:
                            logger.info("JSON repaired with aggressive approach")
                            return json_content.strip()
                        except json.JSONDecodeError:
                            pass
                
                # Final fallback: return a minimal valid JSON
                fallback_json = '{"next_best_actions": ["ask_follow_up_question"], "tool_parameters": {"ask_follow_up_question": {"user_query": "test", "conversation_history": "No previous conversation", "recent_actions": "none"}}, "reasoning": "Customer request requires clarification before proceeding with product search. Following up to gather more details about preferences, style, occasion, and requirements.", "irrelevant_query": false}'
                logger.info("Using fallback JSON due to repair failure")
                return fallback_json
                
        except Exception as e:
            logger.error(f"JSON repair failed: {e}")
            # Return fallback JSON
            fallback_json = '{"next_best_actions": ["ask_follow_up_question"], "tool_parameters": {"ask_follow_up_question": {"user_query": "test", "conversation_history": "No previous conversation", "recent_actions": "none"}}, "reasoning": "Customer request requires clarification before proceeding with product search. Following up to gather more details about preferences, style, occasion, and requirements.", "irrelevant_query": false}'
            return fallback_json


class ModelProviderFactory:
    """Factory for creating model provider instances."""
    
    @staticmethod
    def create_provider(provider_type: ModelProvider, config: AgentConfig) -> BaseModelProvider:
        """
        Create a model provider instance.
        
        Args:
            provider_type: The type of provider to create
            config: Agent configuration
            
        Returns:
            Model provider instance
            
        Raises:
            ValueError: If provider type is not supported
        """
        if provider_type == ModelProvider.BRAINPOWA:
            return BrainPowaSpecialisedProvider(config)
        elif provider_type == ModelProvider.BRAINPOWA_REASONING:
            return BrainPowaReasoningProvider(config)
        elif provider_type == ModelProvider.OPENAI:
            return OpenAIProvider(config)
        else:
            raise ValueError(f"Unsupported model provider: {provider_type}")
        