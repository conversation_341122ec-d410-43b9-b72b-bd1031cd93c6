[project]
name = "multi-agentic-architecture"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "langfuse>=3.3.0",
    "motor>=3.3.0",
    "openai>=1.12.0",
    "opentelemetry-api>=1.36.0",
    "opentelemetry-exporter-otlp>=1.36.0",
    "opentelemetry-instrumentation-aio-pika>=0.57b0",
    "opentelemetry-instrumentation-fastapi>=0.57b0",
    "opentelemetry-instrumentation-logging>=0.57b0",
    "opentelemetry-instrumentation-openai>=0.45.6",
    "opentelemetry-instrumentation-pymongo>=0.57b0",
    "opentelemetry-instrumentation-weaviate>=0.45.6",
    "opentelemetry-sdk>=1.36.0",
    "pydantic>=2.5.0",
    "pymongo>=4.6.0",
    "python-dotenv>=1.0.0",
    "python-multipart>=0.0.6",
    "pytz>=2023.3",
    "requests>=2.31.0",
    "uvicorn[standard]>=0.24.0",
    "weaviate-client>=4.4.0",
    # LangGraph framework
    "langgraph>=0.0.40",
    "langchain>=0.1.0",
    "langchain-core>=0.1.0",
]
