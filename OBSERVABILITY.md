OpenTelemetry Integration Guide

This project ships with a working OpenTelemetry (OTEL) integration for tracing and log correlation across the backend services. This guide explains what was added, how to enable it, and how to view traces locally.

What’s Included
- OTEL SDK setup with OTLP HTTP exporter.
- Automatic instrumentation for FastAPI routes, OpenAI client, optional PyMongo and Weaviate.
- Lightweight helper decorators for custom spans and attributes.
- Environment-based configuration so you can enable/disable features without code changes.

Key Files
- `observability/providers/otel_provider.py`: OTEL provider (init + decorator implementation).
- `observability/core.py`: Provider bootstrap and helper APIs used in the app.
- `api/api.py`: FastAPI app with OTEL route instrumentation.
- `main.py`: CLI entry also initializes OTEL when enabled.
- `requirements.txt`: OTEL dependencies added.
- `env.example`: OTEL environment variables added.

Enabling OpenTelemetry
1) Install dependencies
   - If you use the quick-start scripts, you already install from `requirements.txt`.
   - Otherwise:
     - `pip install -r requirements.txt`

2) Configure environment
   - Copy `env.example` to `.env` and set:
     - `OBSERVABILITY_PROVIDER_NAME=otel`
     - `OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318` (or your collector)
     - `OTEL_SERVICE_NAME=shoeby-agent` (or any name you prefer)
     - Optional instrumentations:
       - `OTEL_WEAVIATE_TRACING=1`
       - `OTEL_mongo_TRACING=1`
     - Optional limits/tags:
       - `OTEL_TRACE_STRING_LIMIT=1000`
       - `OTEL_TRACE_TAGS=merchant`

3) Start the Observability Stack (Collector + Jaeger UI)
   Use the provided compose file which runs an OTEL collector and a Jaeger all‑in‑one UI.

   a) Start Jaeger and OTEL Collector:
      - `docker compose -f docker-compose.observability.yml up -d`

   b) Verify services:
      - Jaeger UI: http://localhost:16686
      - Collector (OTLP HTTP): http://localhost:4318
      - Collector logs: `docker logs -f shoeby-otel-collector`

4) Start the app
   - API: `python api/api.py` (or `uvicorn api.api:app --host 0.0.0.0 --port 8877`)
   - CLI: `python main.py`

   On startup, you should see a message like: “OpenTelemetry & Traceloop initialised. Traces will be sent to http://localhost:4318”.

5) Generate some traces
   - Call the API, for example:
     - `curl -X POST http://localhost:8877/api/chat -H 'Content-Type: application/json' -d '{"user_id":"demo_user","session_id":"","message":"hi"}'`
   - Watch the OTEL Collector logs to see spans.
   - Open Jaeger UI (http://localhost:16686), find the service name you set (e.g., `shoeby-agent`), and view traces.

Running Everything Together
- Databases: `docker compose up -d` (from root; starts MongoDB + Weaviate)
- Observability: `docker compose -f docker-compose.observability.yml up -d`
- Backend API: `python api/api.py`
- Frontend (optional): `cd frontend && npm install && npm run dev`

Environment Matrix
- Local backend, Dockerized collector: set `OTEL_EXPORTER_OTLP_ENDPOINT=http://localhost:4318` (default in env.example).
- Containerized backend (if you containerize it later) on same compose network: set endpoint to `http://otel-collector:4318` and ensure services share the `shoeby-network`.

What the Code Does
- Initialization (`init_observability`):
  - In `api/api.py` startup and `main.py`, we initialize the configured observability provider (OTEL when enabled by env).
  - The OTEL provider (`observability/providers/otel_provider.py`) sets up the `TracerProvider` with `OTLPSpanExporter` (HTTP), batch processor, service name, and environment tags.
  - Automatic instrumentors:
    - FastAPI routes are instrumented in `api/api.py` via `FastAPIInstrumentor.instrument_app(app)`.
    - OpenAI client calls are instrumented via `OpenAIInstrumentor().instrument()`.
    - Logging is correlated via `opentelemetry-instrumentation-logging`.
    - Optional PyMongo/Weaviate instrumentation is toggled via env vars.

- Custom spans and attributes:
  - Use the `@observe_trace(...)` decorator from `observability.core` on functions or route handlers.
  - Supported args (mapped to OTEL):
    - `name` → span name.
    - `capture_input` (bool) → include function args as span attributes.
    - `capture_output` (bool) → include function result as span attributes.
  - You can also call `update_trace(key=value, ...)` to add attributes to the current span.

Minimal Examples
- Decorate a function:
  - `from observability.core import observe_trace`
  - `@observe_trace(name="my_operation", capture_input=True, capture_output=True)`
  - `async def do_work(x: int): return x * 2`

- Add attributes to current span:
  - `from observability.core import update_trace`
  - `update_trace(user_id="abc", session_id="123", step="stage-1")`

Notes and Tips
- If the collector is not running, the app will still run; spans will be dropped by the exporter.
- Ensure `OTEL_EXPORTER_OTLP_ENDPOINT` matches your collector endpoint.
- For broader auto-instrumentation (httpx, requests, etc.), you can add/install more `opentelemetry-instrumentation-*` packages and enable them in the provider.
- To instrument per-route names on FastAPI beyond the default, keep using `@observe_trace(name=...)` in your route handlers as shown in `api/routers/chat.py`.

Uninstall/Disable
- Set `OBSERVABILITY_PROVIDER_NAME=null` to disable and avoid any OTEL overhead.

Troubleshooting
- No traces? Check:
  - Collector is running and listening on `4318` (or your configured port).
  - `OBSERVABILITY_PROVIDER_NAME=otel` is set in your environment.
  - Dependencies installed from `requirements.txt`.
  - Look for “OpenTelemetry” log messages on startup.
