# 🛍️ Shoeby Conversational Agent

A conversational LLM agent designed for intelligent fashion retail assistance. Built with a modular architecture that supports multiple LLM providers, persistent conversation history, and product search capabilities.

## 🚀 Features

- **BrainPowa LLM Support**: Powered by BrainPowa's advanced reasoning models for optimal performance
- **Intelligent Product Search**: Advanced semantic search using Weaviate vector database with automatic query broadening
- **Persistent Conversations**: MongoDB-backed conversation history with session management and caching
- **Strategy Engine**: LLM-driven decision making for optimal user assistance
- **RESTful API**: FastAPI backend with CORS support for web applications
- **Modern Frontend**: React-based chat interface with Vite build system
- **Shopping Cart Management**: Intelligent product recommendations and basket management with size resolution
- **Performance Monitoring**: Detailed reasoning and execution metrics
- **DateTime Context**: Intelligent timezone handling and temporal reasoning
- **Tool-Based Architecture**: Modular tool system for extensible functionality
- **Framework Switching**: Support for multiple agentic frameworks (Custom, LangGraph)

## 🏗️ Architecture

The Shoeby Agent follows a modern multi-agent architecture with pluggable frameworks:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI        │    │   Framework     │
│   (React 18)    │◄──►│   (api.py)       │◄──►│   Factory       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   API Routers    │    │   Frameworks    │
                       │   • Chat         │    │   • Custom      │
                       │   • Basket       │    │   • LangGraph   │
                       │   • Debug        │    │   • Base        │
                       │   • Health       │    └─────────────────┘
                       └──────────────────┘             │
                                │                       ▼
                                ▼              ┌─────────────────┐
                       ┌──────────────────┐    │   Agents        │
                       │   Services       │    │   • Search      │
                       │   • LLM          │    │   • Presentation│
                       │   • Conversation │    │   • Purchase    │
                       │   • DateTime     │    │   • Conversation│
                       └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Tools          │    │   External      │
                       │   • Database     │    │   • Weaviate    │
                       │   • Product      │    │   • MongoDB     │
                       │   • Base         │    │   • BrainPowa   │
                       └──────────────────┘    └─────────────────┘
```

For detailed architecture information including UML diagrams, see [ARCHITECTURE.md](documentation/ARCHITECTURE.md).

Note: The system has been refactored to use a modern multi-agent architecture with pluggable frameworks. Observability modules have been simplified and unused components removed for better maintainability.

### Core Components

- **`ShoebyAgent`**: Main agent class that manages the overall system
- **`AgentOrchestrator`**: LangGraph-based orchestrator for agent coordination
- **`AgentsRegistry`**: Central registry for all available agents and their capabilities
- **`ToolsRegistry`**: Central registry for all available tools
- **`SearchEngine`**: Intelligent product search with Weaviate integration
- **`LLMService`**: Multi-provider LLM abstraction layer with BrainPowa integration
- **`ConversationService`**: MongoDB-backed conversation persistence with session caching
- **`DateTimeContextService`**: Intelligent timezone handling and temporal reasoning

### Agent System

The system uses specialized agents for different tasks:

- **Search Agents**: `ConstructWeaviateQueryAgent`, `EvaluateProductsAgent`
- **Presentation Agents**: `PresentProductsAgent`
- **Conversation Agents**: `AskFollowUpQuestionAgent`, `RedirectIrrelevantQueryAgent`
- **Purchase Agents**: `SuggestComplementaryProductsAgent`

### Tool System

The system includes concrete tools for external operations:

- **Database Tools**: `WeaviateSearchTool`, `MongoDBCacheTool`, `BrainPowaLLMTool`
- **Product Tools**: `ProductDetailsTool` for detailed product information retrieval
- **Basket Tools**: `BasketTool` for shopping basket management operations
- **Base Tools**: Common tool functionality and interfaces

## 🔄 Agent Orchestration

The Shoeby Agent uses LangGraph for agent orchestration and state management:

### LangGraph Orchestrator

- **Graph-based Workflow**: State management with intelligent routing between agents
- **Agent Orchestration**: Seamless coordination of multiple specialized agents
- **State Persistence**: Maintains conversation context and user preferences
- **Error Handling**: Robust error recovery and fallback mechanisms

### Architecture

The system now uses a simplified architecture with:
- **Single Orchestrator**: LangGraph-based agent coordination
- **Centralized Registries**: Agents and tools managed in dedicated registries
- **Streamlined Configuration**: No framework switching complexity

## 🛠️ Technology Stack

### Backend
- **Python 3.8+**: Core application logic
- **FastAPI 0.104+**: High-performance web framework
- **Uvicorn**: ASGI server for production deployment
- **Weaviate 1.22.4**: Vector database for semantic search
- **MongoDB 7.0**: Document database for conversation history
- **Pydantic 2.5+**: Data validation and serialisation
- **Motor**: Async MongoDB driver

### LLM Provider
- **BrainPowa**: Advanced reasoning models with OpenAI-compatible API
- **OpenAI Client**: Used by BrainPowa provider for API communication

### Frontend
- **React 18**: Modern UI framework
- **Vite 5.0**: Fast build tool and dev server
- **Axios 1.6**: HTTP client for API communication

## 📦 Installation

### Prerequisites

Before starting, you'll need:
- **Python 3.8+** installed on your system
- **BrainPowa API Key** for the LLM service
- **OpenAI API Key** for fallback and evaluation
- **MongoDB** instance (local or cloud)
- **Weaviate** instance (local or cloud)

### Environment Setup

**🚨 Important**: Set up your environment variables first to avoid API key exposure!

1. **Quick Setup** (Recommended):
   ```bash
   python setup_env.py
   ```

2. **Manual Setup**:
   ```bash
   cp env.example .env
   # Edit .env with your actual API keys and database URLs
   ```

See [ENVIRONMENT_SETUP.md](ENVIRONMENT_SETUP.md) for detailed instructions.

### Quick Start (Recommended)

For the fastest setup, use our automated scripts:

**macOS/Linux:**
```bash
git clone <your-repository-url>
cd multi-agentic-architecture
./quick-start.sh
```

**Windows:**
```cmd
git clone <your-repository-url>
cd multi-agentic-architecture
quick-start.bat
```

For detailed setup instructions, see [SETUP.md](SETUP.md).

### Manual Setup

#### Prerequisites

- Python 3.8 or higher (Python 3.9+ recommended)
- Docker and Docker Compose
- Git for cloning the repository
- BrainPowa API key (or OpenAI API key for compatibility)

#### Step-by-Step Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd multi-agentic-architecture
   ```

2. **Start databases with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Set up Python environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate.bat
   pip install -r requirements.txt
   ```

   **Optional - For LangGraph Framework:**
   ```bash
   pip install langgraph langchain langchain-core
   ```

4. **Configure environment variables**
   ```bash
   cp env.example .env
   # Edit .env with your API keys and configuration
   ```

5. **Test the setup**
   ```bash
   python test-setup.py
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

**Note:** The frontend requires the backend API to be running. Start the API server first with `python api.py`.

## 🛠️ Setup Files

The repository includes several files to make local development easier:

- **`quick-start.sh`** / **`quick-start.bat`** - Automated setup scripts for macOS/Linux and Windows
- **`docker-compose.yml`** - Complete database stack (MongoDB 7.0 + Weaviate 1.22.4)
- **`test-setup.py`** - Verification script to test your setup
- **`env.example`** - Environment variables template
- **`SETUP.md`** - Comprehensive setup guide with troubleshooting

## 🚀 Usage

### Interactive Mode

Run the agent in interactive mode for testing and development:

```bash
python main.py
```

**Available Commands:**
- `quit`: Exit the session
- `json on/off`: Toggle JSON debug output
- `history`: Show conversation history
- `basket`: Display shopping cart contents
- `clear_history`: Clear session history
- `summary`: Show session statistics
- `providers`: List available LLM providers
- `info`: Show framework and system information
- `agents`: List available agents
- `bp`/`brainpowa`: Switch to BrainPowa models

### API Mode

Start the FastAPI server:

```bash
python api.py
```

The API will be available at `http://localhost:8000` with automatic OpenAPI documentation.

**Key Endpoints:**
- `POST /chat`: Send a message and get response
- `POST /chat/reasoning`: Get response with detailed reasoning
- `POST /sessions`: Create new conversation session
- `GET /sessions/{session_id}`: Get session details
- `GET /basket`: Get current shopping basket
- `POST /basket/add`: Add items to basket
- `GET /health`: Health check endpoint
- `GET /api/debug/test-search`: Debug endpoint for testing searches
- `GET /docs`: Interactive API documentation

### Frontend

Access the React chat interface at `http://localhost:5173` (Vite dev server).

## 🔧 Configuration

### LLM Provider Selection

The agent supports multiple BrainPowa models optimized for different tasks:

- **Default Provider**: BrainPowa with specialized models for different tasks
- **Model Selection**: Automatic model selection based on task type (strategy, search, presentation, etc.)
- **Available Models**: `brainpowa-single-gpu-27B`, `brainpowa-retail-chat-v1-7B`, `brainpowa-education-128K`
- **Fallback Support**: OpenAI client compatibility for BrainPowa integration

### Search Configuration

- **Max Retries**: 3 attempts with automatic query broadening
- **Result Limits**: Configurable per search type (default: 10)
- **Filters**: Dynamic filtering based on user preferences
- **Collection**: `ShoebyAgenticStoreProducts` in Weaviate

### Session Management

- **User ID**: Configurable user identification (default: "demo_user")
- **Session ID**: Auto-generated UUIDs with persistence
- **History Limits**: Configurable conversation memory
- **Session Caching**: MongoDB-backed conversation persistence

### DateTime Context

- **Timezone Support**: Configurable timezone handling
- **Temporal Reasoning**: Intelligent date and time understanding
- **Context Awareness**: Time-aware product recommendations

## 📊 Performance & Monitoring

The agent provides comprehensive performance metrics:

- **Response Times**: Per-step execution timing
- **Provider Usage**: Which LLM provider handled each request
- **Action Execution**: Detailed breakdown of LLM decisions
- **Search Performance**: Query success rates and result quality
- **Tool Execution**: Performance metrics for individual tools

Enable JSON debug output to see detailed reasoning:

```bash
json on
```

### Telemetry (OpenTelemetry)

- Built-in OpenTelemetry integration for traces and log correlation.
- Enable by setting `OBSERVABILITY_PROVIDER_NAME=otel` and pointing `OTEL_EXPORTER_OTLP_ENDPOINT` to your collector.
- FastAPI routes, OpenAI client, and optional MongoDB/Weaviate are instrumented.
- See `OBSERVABILITY.md` for setup and walkthrough.

## 🧪 Testing & Evaluation

### Setup Verification

Test your local setup with our comprehensive verification script:

```bash
python test-setup.py
```

This script checks:
- Python imports and dependencies
- Configuration loading
- Environment variables
- Docker services
- Database connections
- Agent initialisation

### Performance Evaluation

Located in the `archive/` directory:
- `evaluate.py`: Performance evaluation framework

### Test Results

Evaluation results are stored in `eval-results/` with timestamps and detailed metrics.

## 🔍 Search Capabilities

### Intelligent Query Processing

1. **Natural Language Understanding**: Converts user intent to structured queries
2. **Semantic Search**: Uses Weaviate's vector capabilities for meaning-based matching
3. **Query Broadening**: Automatically expands search terms for better results
4. **Filter Optimisation**: Dynamic filtering based on context and preferences
5. **Result Evaluation**: AI-powered result quality assessment

### Product Recommendations

- **Complementary Items**: Finds products that work well together
- **Style Matching**: Semantic similarity for fashion coordination
- **Occasion-Based**: Contextual recommendations for specific events
- **Size Resolution**: Intelligent product sizing and availability

## 🛒 Shopping Experience

### Cart Management

- **Intelligent Additions**: AI-suggested products based on conversation
- **Quantity Management**: Automatic quantity suggestions
- **Price Tracking**: Real-time total calculations
- **Session Persistence**: Cart contents maintained across conversations
- **Basket Size Resolution**: Automatic product size selection

### User Guidance

- **Follow-up Questions**: Contextual questions to understand preferences
- **Style Advice**: Fashion recommendations and coordination tips
- **Purchase Flow**: Guided shopping experience with clear next steps
- **Temporal Context**: Time-aware recommendations and availability

## 🔐 Security & Privacy

- **API Key Management**: Environment variable configuration
- **CORS Protection**: Configurable cross-origin policies
- **Session Isolation**: User and session-based data separation
- **No Data Persistence**: LLM conversations not stored externally
- **MongoDB Authentication**: Secure database access with credentials

## 🚀 Deployment

### Production Considerations

1. **Environment Variables**: Secure API key management
2. **Database Security**: MongoDB authentication and network isolation
3. **Weaviate Security**: API key protection and access controls
4. **CORS Configuration**: Restrict to production domains
5. **Load Balancing**: Consider multiple agent instances for high traffic

### Docker Support

The modular architecture makes containerisation straightforward:

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "api.py"]
```

## 🤝 Contributing

### Local Development Setup

The repository is designed for easy local development:

1. **Quick Setup**: Run `./quick-start.sh` (macOS/Linux) or `quick-start.bat` (Windows)
2. **Database Stack**: MongoDB 7.0 and Weaviate 1.22.4 run in Docker containers
3. **Environment Management**: Virtual environment and dependencies handled automatically
4. **Testing**: Use `python test-setup.py` to verify your setup
5. **Documentation**: See [SETUP.md](SETUP.md) for detailed instructions

### Contributing Guidelines

1. **Fork the repository**
2. **Create a feature branch**
3. **Follow the existing code style**
4. **Add tests for new functionality**
5. **Submit a pull request**

### Code Style

- **Python**: PEP 8 compliance with type hints
- **JavaScript**: ES6+ with consistent formatting
- **Documentation**: Comprehensive docstrings and comments

## 📚 Documentation

Comprehensive documentation is available in the `documentation/` directory:

- **`FRAMEWORK_SWITCHING.md`**: Detailed guide for switching between frameworks
- **`ARCHITECTURE.md`**: System architecture and design patterns
- Additional documentation for specific components and features

## 📝 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For technical support or questions:

### Quick Troubleshooting

1. **Run the test script**: `python test-setup.py` to diagnose issues
2. **Check Docker services**: `docker-compose ps` to verify database status
3. **Review setup guide**: See [SETUP.md](SETUP.md) for detailed troubleshooting

### Common Issues

- **Import errors**: Ensure virtual environment is activated
- **Database connection**: Check if Docker containers are running
- **API keys**: Verify `.env` file configuration
- **Port conflicts**: Ensure ports 27017, 8080, and 8000 are available

### Getting Help

- Check the configuration settings
- Review the logs for error messages
- Ensure all external services are running
- Verify API keys and connection strings

## 🔮 Future Enhancements

- **Multi-language Support**: Internationalisation for global markets
- **Voice Interface**: Speech-to-text and text-to-speech capabilities
- **Image Recognition**: Visual product search and style matching
- **Personalisation**: User preference learning and customisation
- **Analytics Dashboard**: Business intelligence and user behaviour insights
- **Advanced Caching**: Redis integration for improved performance
- **Microservices**: Containerised service deployment

---

This README is LLM-generated with Cursor
