"""
Product search functionality with Weaviate integration.
Handles all search operations, query building, and result processing.
"""
import logging
import weaviate

from typing import List, Optional, Dict
from core.entities import Product, SearchQuery, SearchState

from weaviate import classes as wvc


logger = logging.getLogger(__name__)


class SearchEngine:
    """
    Manages all product search operations using Weaviate database.
    
    Encapsulates search logic, query construction, and result processing.
    """
    
    def __init__(self, weaviate_client: weaviate.WeaviateClient, collection_name: str, max_retries: int = 3):
        """
        Initialise the search engine with Weaviate connection.
        
        Args:
            weaviate_client: Connected Weaviate client instance
            collection_name: Name of the product collection to search
            max_retries: Maximum search retry attempts for poor results
        """
        logger.info("🔧 INITIALISING SEARCH ENGINE:")
        logger.info(f"   Collection Name: {collection_name}")
        logger.info(f"   Max Retries: {max_retries}")
        
        self.client = weaviate_client
        self.collection = weaviate_client.collections.get(collection_name)
        self.max_retries = max_retries

        # Test connection and log product count
        try:
            total = self.collection.aggregate.over_all(total_count=True)
            logger.info("✅ Search engine initialised successfully:")
            logger.info(f"   Collection: {collection_name}")
            logger.info(f"   Total Products: {total.total_count}")
            logger.info(f"   Weaviate Client: {type(weaviate_client).__name__}")
            logger.info(f"   Collection Object: {type(self.collection).__name__}")
        except Exception as e:
            logger.error(f"❌ Failed to get collection info: {e}")
            logger.warning("   Search engine initialised but collection info unavailable")
            logger.info(f"   Collection: {collection_name}")
            logger.info(f"   Weaviate Client: {type(weaviate_client).__name__}")
            logger.info(f"   Collection Object: {type(self.collection).__name__}")
    
    @classmethod
    def from_config(cls, config):
        """
        Create SearchEngine instance from AgentConfig.
        
        Args:
            config: AgentConfig instance containing Weaviate connection details
            
        Returns:
            SearchEngine instance
        """
        # Get Weaviate connection parameters
        connection_params = config.get_weaviate_connection_params()
        
        # Create Weaviate client
        weaviate_client = weaviate.connect_to_local(
            host=connection_params["http_host"],
            port=connection_params["http_port"],
            grpc_port=connection_params["grpc_port"],
            headers=connection_params.get("headers", {}),
            auth_credentials=connection_params.get("auth_credentials")
        )
        
        # Create SearchEngine instance
        return cls(
            weaviate_client=weaviate_client,
            collection_name=config.collection_name,
            max_retries=config.max_search_retries
        )
    
    def close(self):
        """Close the Weaviate client connection."""
        if hasattr(self, 'client') and self.client:
            self.client.close()
            logger.info("✅ Weaviate client connection closed")
    
    def search_products(self, search_query: SearchQuery) -> SearchState:
        """
        Execute a product search with automatic retry and broadening logic.
        
        Implements intelligent search strategy:
        1. Execute initial search
        2. Evaluate result quality 
        3. Broaden search if results are insufficient
        4. Retry up to maximum attempts
        
        Args:
            search_query: SearchQuery containing search parameters
            
        Returns:
            SearchState with results and execution metadata
        """
        search_state = SearchState(current_query=search_query)
        
        logger.info(f"🚀 SEARCH: {search_query.search_type} | {len(search_query.search_terms)} terms | {len(search_query.primary_filters)} filters | Limit: {search_query.result_limit}")
        
        while search_state.retry_count < self.max_retries:
            attempt_num = search_state.retry_count + 1
            logger.info(f"🔄 Attempt {attempt_num}/{self.max_retries}")
            
            # Execute the search
            success = self._execute_search(search_state)
            
            if not success:
                search_state.retry_count += 1
                logger.warning(f"   Attempt {attempt_num} failed, retrying...")
                continue
            
            # Check if results are satisfactory
            if self._are_results_satisfactory(search_state):
                logger.info(f"✅ Search successful: {search_state.get_result_count()} products found")
                return search_state
            
            # If we have some results on the last retry, return them
            if search_state.retry_count == self.max_retries - 1 and search_state.has_results():
                logger.warning(f"⚠️ Max retries reached, returning {search_state.get_result_count()} products")
                return search_state
            
            # Broaden search for next attempt
            logger.info("📈 Broadening search...")
            old_query = search_state.current_query
            search_state.current_query = self._broaden_search_query(search_state.current_query)
            
            logger.info(f"   Filters: {old_query.primary_filters} → {search_state.current_query.primary_filters}")
            
            search_state.retry_count += 1
        
        logger.warning("❌ All search attempts failed")
        return search_state
    
    def find_complementary_products(self, primary_product: Product, limit: int = 5) -> List[Product]:
        """
        Find products that complement a given primary product.
        
        Uses semantic similarity to find items that would work well together
        based on style, occasion, and category.
        
        Args:
            primary_product: The product to find complements for
            limit: Maximum number of complementary products to return
            
        Returns:
            List of complementary Product entities
        """
        logger.info(f"🔍 Finding complementary products for: {primary_product.title} ({primary_product.category})")
        
        try:
            # Create a query based on the primary product's attributes
            complementary_query = f"{primary_product.style} {primary_product.occasion} fashion accessories"
            
            # Search for complementary items, excluding the primary product's category
            results = self.collection.query.near_text(
                query=complementary_query,
                limit=limit * 2,  # Get more results to filter out same category
                return_metadata=wvc.query.MetadataQuery(distance=True)
            )
            
            complements = []
            for i, obj in enumerate(results.objects):
                props = obj.properties
                product = self._map_properties_to_product(props)
                
                # Exclude items from the same category as the primary product
                if product.category != primary_product.category and len(complements) < limit:
                    complements.append(product)
            
            logger.info(f"✅ Found {len(complements)} complementary products")
            return complements
            
        except Exception as e:
            logger.error(f"❌ Error finding complementary products: {e}")
            logger.exception("Full complementary products error traceback:")
            return []
    
    def _execute_search(self, search_state: SearchState) -> bool:
        """
        Execute a single search attempt using current query parameters.
        
        Args:
            search_state: Current search state with query parameters
            
        Returns:
            True if search executed successfully, False otherwise
        """
        try:
            query = search_state.current_query
            search_terms = " ".join(query.search_terms) if query.search_terms else ""
            
            # Build filters
            filters = self._build_weaviate_filters(query.primary_filters, query.price_filters)
            
            # Log essential search parameters
            logger.info(f"🔍 SEARCH: {query.search_type.upper()} | Terms: '{search_terms}' | Filters: {query.primary_filters} | Limit: {query.result_limit}")
            
            # Execute search based on type
            results = None
            search_executed = False
            
            if query.search_type == "hybrid" and search_terms:
                try:
                    if filters:
                        results = self.collection.query.hybrid(
                            query=search_terms,
                            filters=filters,
                            limit=query.result_limit,
                            alpha=0.7,
                            return_metadata=wvc.query.MetadataQuery(score=True)
                        )
                    else:
                        results = self.collection.query.hybrid(
                            query=search_terms,
                            limit=query.result_limit,
                            alpha=0.7,
                            return_metadata=wvc.query.MetadataQuery(score=True)
                        )
                    search_executed = True
                except Exception as e:
                    logger.warning(f"   Hybrid search failed: {e}, will try semantic fallback")
                    results = None
                
                # If hybrid search returns no results, try semantic search as fallback
                if search_executed and (not results or not hasattr(results, 'objects') or not results.objects):
                    logger.info("   🔄 Trying semantic fallback...")
                    try:
                        if filters:
                            results = self.collection.query.near_text(
                                query=search_terms,
                                filters=filters,
                                limit=query.result_limit,
                                return_metadata=wvc.query.MetadataQuery(distance=True)
                            )
                        else:
                            results = self.collection.query.near_text(
                                query=search_terms,
                                limit=query.result_limit,
                                return_metadata=wvc.query.MetadataQuery(distance=True)
                            )
                    except Exception as e:
                        logger.warning(f"   Semantic fallback also failed: {e}")
                        results = None
                        
            elif query.search_type == "semantic" and search_terms:
                try:
                    if filters:
                        results = self.collection.query.near_text(
                            query=search_terms,
                            filters=filters,
                            limit=query.result_limit,
                            return_metadata=wvc.query.MetadataQuery(distance=True)
                        )
                    else:
                        results = self.collection.query.near_text(
                            query=search_terms,
                            limit=query.result_limit,
                            return_metadata=wvc.query.MetadataQuery(distance=True)
                        )
                    search_executed = True
                except Exception as e:
                    logger.warning(f"   Semantic search failed: {e}, will try filter-only fallback")
                    results = None
                
                # If semantic search returns no results, try filter-only search as fallback
                if search_executed and (not results or not hasattr(results, 'objects') or not results.objects):
                    logger.info("   🔄 Trying filter-only fallback...")
                    try:
                        if filters:
                            results = self.collection.query.fetch_objects(
                                filters=filters,
                                limit=query.result_limit
                            )
                        else:
                            results = self.collection.query.fetch_objects(limit=query.result_limit)
                    except Exception as e:
                        logger.warning(f"   Filter-only fallback also failed: {e}")
                        results = None
                        
            elif query.search_type == "keyword" and search_terms:
                try:
                    if filters:
                        results = self.collection.query.fetch_objects(
                            filters=filters,
                            limit=query.result_limit
                        )
                    else:
                        results = self.collection.query.fetch_objects(limit=query.result_limit)
                    search_executed = True
                except Exception as e:
                    logger.warning(f"   Keyword search failed: {e}, will try semantic fallback")
                    results = None
                
                # If keyword search returns no results, try semantic search as fallback
                if search_executed and (not results or not hasattr(results, 'objects') or not results.objects):
                    logger.info("   🔄 Trying semantic fallback...")
                    try:
                        if filters:
                            results = self.collection.query.near_text(
                                query=search_terms,
                                filters=filters,
                                limit=query.result_limit,
                                return_metadata=wvc.query.MetadataQuery(distance=True)
                            )
                        else:
                            results = self.collection.query.near_text(
                                query=search_terms,
                                limit=query.result_limit,
                                return_metadata=wvc.query.MetadataQuery(distance=True)
                            )
                    except Exception as e:
                        logger.warning(f"   Semantic fallback also failed: {e}")
                        results = None
                        
            else:
                # Fallback to simple fetch
                if filters:
                    results = self.collection.query.fetch_objects(
                        filters=filters,
                        limit=query.result_limit
                    )
                else:
                    results = self.collection.query.fetch_objects(limit=query.result_limit)
            
            # Final fallback: if no results found with any method, try unfiltered search
            # BUT respect price constraints if they exist
            if not results or not hasattr(results, 'objects') or not results.objects:
                logger.info("   🔄 Trying unfiltered fallback...")
                try:
                    # Check if we have price constraints that should be respected
                    price_filters = query.price_filters
                    has_price_constraints = (price_filters.get("min_price") is not None or 
                                           price_filters.get("max_price") is not None)
                    
                    if has_price_constraints:
                        logger.info("   ⚠️ Price constraints present - will respect them in fallback")
                        # Try a simple text search with only price filters
                        if search_terms:
                            price_only_filters = self._build_weaviate_filters({}, price_filters)
                            if price_only_filters:
                                results = self.collection.query.near_text(
                                    query=search_terms,
                                    filters=price_only_filters,
                                    limit=query.result_limit,
                                    return_metadata=wvc.query.MetadataQuery(distance=True)
                                )
                            else:
                                results = self.collection.query.near_text(
                                    query=search_terms,
                                    limit=query.result_limit,
                                    return_metadata=wvc.query.MetadataQuery(distance=True)
                                )
                        else:
                            # Last resort with price constraints: fetch products with price filters only
                            price_only_filters = self._build_weaviate_filters({}, price_filters)
                            if price_only_filters:
                                results = self.collection.query.fetch_objects(
                                    filters=price_only_filters,
                                    limit=query.result_limit
                                )
                            else:
                                results = self.collection.query.fetch_objects(limit=query.result_limit)
                    else:
                        # No price constraints - safe to do completely unfiltered search
                        if search_terms:
                            results = self.collection.query.near_text(
                                query=search_terms,
                                limit=query.result_limit,
                                return_metadata=wvc.query.MetadataQuery(distance=True)
                            )
                        else:
                            # Last resort: fetch any products
                            results = self.collection.query.fetch_objects(limit=query.result_limit)
                except Exception as e:
                    logger.warning(f"   Unfiltered fallback also failed: {e}")
                    results = None
            
            # Process results
            search_state.current_results = []
            if results and hasattr(results, 'objects') and results.objects:
                # Deduplicate products and aggregate sizes
                product_map = {}
                
                for obj in results.objects:
                    product = self._map_properties_to_product(obj.properties)
                    
                    # Create a unique key based on title and brand
                    unique_key = f"{product.title}_{product.brand}_{product.category}_{product.colour}"
                    
                    if unique_key in product_map:
                        # Product already exists, add size if different
                        existing_product = product_map[unique_key]
                        if product.size and product.size != existing_product.size:
                            if not hasattr(existing_product, 'available_sizes'):
                                existing_product.available_sizes = [existing_product.size]
                            if product.size not in existing_product.available_sizes:
                                existing_product.available_sizes.append(product.size)
                    else:
                        # New product, initialise available sizes
                        if product.size:
                            product.available_sizes = [product.size]
                        else:
                            product.available_sizes = []
                        product_map[unique_key] = product
                
                # Convert map back to list
                search_state.current_results = list(product_map.values())
                
                # Log products found (concise format)
                logger.info(f"✅ Found {len(search_state.current_results)} products:")
                for i, product in enumerate(search_state.current_results):  # Log first 5 results
                    size_info = f" | Sizes: {', '.join(product.available_sizes)}" if hasattr(product, 'available_sizes') and product.available_sizes else ""
                    logger.info(f"   {i+1}. {product.title} - £{product.sale_price}{size_info}")
                
                if len(search_state.current_results) > 5:
                    logger.info(f"   ... and {len(search_state.current_results) - 5} more")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Search execution failed: {e}")
            search_state.current_results = []
            return False
    
    def _build_weaviate_filters(self, primary_filters: Dict, price_filters: Dict) -> Optional[wvc.query.Filter]:
        """
        Construct Weaviate v4 filters using proper syntax.
        
        Args:
            primary_filters: Dictionary of field-value filters
            price_filters: Dictionary of price range filters
            
        Returns:
            Combined Weaviate filter object or None if no filters
        """
        filters = []
        
        # Build primary filters with British English handling
        for field, value in primary_filters.items():
            if value:
                # Handle field name mappings to match database schema
                if field == "product_category":
                    field = "product_category"  # Keep as is - database uses this
                elif field == "color":
                    field = "color"  # Keep as is - database uses this
                
                # Create filter based on field type
                if field == "product_category":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "color":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "brand":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "material":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "occasion":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "style":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "gender":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "pattern":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "collection":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "availability":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                elif field == "special_offer":
                    logger.info(f"   Filter: {field} = {value}")
                    filters.append(wvc.query.Filter.by_property(field).equal(value))
                else:
                    logger.warning(f"   Unknown filter field: {field} = {value}")
        
        # Build price filters
        if price_filters:
            min_price = price_filters.get("min_price")
            max_price = price_filters.get("max_price")
            
            if min_price is not None and max_price is not None:
                logger.info(f"   Filter: price between {min_price} and {max_price}")
                filters.append(wvc.query.Filter.by_property("sale_price").greater_or_equal(min_price))
                filters.append(wvc.query.Filter.by_property("sale_price").less_or_equal(max_price))
            elif min_price is not None:
                logger.info(f"   Filter: price >= {min_price}")
                filters.append(wvc.query.Filter.by_property("sale_price").greater_or_equal(min_price))
            elif max_price is not None:
                logger.info(f"   Filter: price <= {max_price}")
                filters.append(wvc.query.Filter.by_property("sale_price").less_or_equal(max_price))
        
        # Combine filters
        if filters:
            if len(filters) == 1:
                return filters[0]
            else:
                # Use the correct Weaviate v4 syntax for combining filters
                combined_filter = filters[0]
                for filter_item in filters[1:]:
                    combined_filter = combined_filter & filter_item
                return combined_filter
        
        return None
    
    def _are_results_satisfactory(self, search_state: SearchState) -> bool:
        """
        Determine if search results meet quality standards.
        
        Args:
            search_state: Current search state with results
            
        Returns:
            True if results are satisfactory, False if search should be broadened
        """
        results_count = search_state.get_result_count()
        
        # No results are definitely not satisfactory
        if results_count == 0:
            return False
        
        # Good number of results are usually satisfactory
        if results_count >= 5:
            return True
        
        # For edge cases with few results, consider them satisfactory if we have any
        # This avoids unnecessary broadening when the search is naturally specific
        return results_count > 0
    
    def _broaden_search_query(self, original_query: SearchQuery) -> SearchQuery:
        """
        Create a broader version of the search query to find more results.
        
        Implements business rules for query broadening:
        - Remove most specific filters first
        - Increase result limit
        - Maintain search intent while expanding scope
        
        Args:
            original_query: The original search query that needs broadening
            
        Returns:
            SearchQuery with broadened parameters
        """
        new_filters = original_query.primary_filters.copy()
        new_price_filters = original_query.price_filters.copy()
        
        # Remove filters in order of specificity (most specific first)
        if "size" in new_filters:
            del new_filters["size"]
            logger.info("Removed size filter to broaden search")
        elif "color" in new_filters:
            del new_filters["color"]
            logger.info("Removed color filter to broaden search")
        elif len(new_filters) > 1:
            # Remove first non-essential filter (keep product_category as it's important)
            filter_to_remove = next(iter(new_filters))
            if filter_to_remove != "product_category":
                del new_filters[filter_to_remove]
                logger.info(f"Removed {filter_to_remove} filter to broaden search")
        
        # Increase result limit for broader search
        new_limit = min(original_query.result_limit + 5, 20)
        
        return SearchQuery(
            search_terms=original_query.search_terms,
            primary_filters=new_filters,
            price_filters=new_price_filters,
            search_type=original_query.search_type,
            result_limit=new_limit
        )
    
    def _map_properties_to_product(self, props: Dict) -> Product:
        """
        Map Weaviate object properties to Product entity.
        
        Args:
            props: Dictionary of properties from Weaviate object
            
        Returns:
            Product entity with mapped properties
        """
        return Product(
            title=props.get('title', 'Unknown'),
            brand=props.get('brand', ''),
            price=props.get('price', 0),
            sale_price=props.get('sale_price', props.get('price', 0)),
            colour=props.get('color', ''),  # Database stores as 'color', map to 'colour'
            size=props.get('size', ''),
            material=props.get('material', ''),
            category=props.get('product_category', ''),  # Database stores as 'product_category', map to 'category'
            style=props.get('style', ''),
            occasion=props.get('occasion', ''),
            description=props.get('description', ''),
            product_url=props.get('link', '')
        )
    