from __future__ import annotations
import os
from typing import Any, Dict, Iterable, Optional, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

VI_BASE = "https://search.visenze.com"
RECS_PATH_TMPL = "/v1/product/recommendations/{product_id}"

def _resolve_auth(app_key: Optional[str], placement_id: Optional[str]) -> Tuple[str, str]:
    ak = app_key or os.getenv("VISENZE_APP_KEY")
    pid = placement_id or os.getenv("VISENZE_PLACEMENT_ID_RECS") or os.getenv("VISENZE_PLACEMENT_ID")
    if not ak or not pid:
        raise ValueError(
            "ViSenze app_key and placement_id are required. "
            "Pass as args or set VISENZE_APP_KEY and VISENZE_PLACEMENT_ID[_RECS]."
        )
    return ak, pid

def _session() -> requests.Session:
    s = requests.Session()
    # Gzip to reduce bandwidth as per docs
    s.headers.update({"Accept": "application/json", "Accept-Encoding": "gzip"})
    retries = Retry(
        total=3,
        backoff_factor=0.5,
        status_forcelist=(429, 500, 502, 503, 504),
        allowed_methods=frozenset(["GET"]),
        raise_on_status=False,
    )
    s.mount("https://", HTTPAdapter(max_retries=retries))
    return s

def vi_recommendations(
    product_id: str,
    *,
    # auth
    app_key: Optional[str] = None,
    placement_id: Optional[str] = None,

    # paging
    page: Optional[int] = None,
    limit: Optional[int] = None,

    # model outfit / alternatives
    alt_limit: Optional[int] = None,

    # filtering
    filters: Optional[str] = None,
    text_filters: Optional[str] = None,

    # attributes / facets
    attrs_to_get: Optional[Iterable[str]] = None,   # comma-joined
    facets: Optional[Iterable[str]] = None,         # comma-joined
    facets_limit: Optional[int] = None,
    facets_show_count: Optional[bool] = None,

    # scoring / sorting / dedup
    score: Optional[bool] = None,
    score_min: Optional[float] = None,
    score_max: Optional[float] = None,
    sort_by: Optional[str] = None,
    dedup: Optional[bool] = None,

    # analytics / session
    va_uid: Optional[str] = None,
    va_sid: Optional[str] = None,

    # toggles / extra info
    return_fields_mapping: Optional[bool] = None,
    return_product_info: Optional[bool] = None,
    show_pinned_pids: Optional[bool] = None,
    show_excluded_pids: Optional[bool] = None,
    use_set_based_ctl: Optional[bool] = None,
    set_limit: Optional[int] = None,
    show_best_product_images: Optional[bool] = None,
    non_product_based_recs: Optional[bool] = None,
    locale: Optional[str] = None,

    # grouping
    group_by_key: Optional[str] = None,
    group_limit: Optional[int] = None,

    # passthrough for any future flags
    **extra_params: Any,
) -> Dict[str, Any]:
    """
    ViSenze Recommendations API wrapper.

    Mirrors documented query params. Unknown/forward-compat options can be passed via **extra_params.
    Returns parsed JSON (dict). Raises for non-2xx (after retries).
    """
    app_key, placement_id = _resolve_auth(app_key, placement_id)
    url = f"{VI_BASE}{RECS_PATH_TMPL.format(product_id=product_id)}"

    params: Dict[str, Any] = {
        "app_key": app_key,
        "placement_id": placement_id,
    }

    # pagination
    if page is not None: params["page"] = page
    if limit is not None: params["limit"] = limit

    # alternatives
    if alt_limit is not None: params["alt_limit"] = alt_limit

    # filters
    if filters: params["filters"] = filters
    if text_filters: params["text_filters"] = text_filters

    # attrs / facets
    if attrs_to_get: params["attrs_to_get"] = ",".join(attrs_to_get)
    if facets: params["facets"] = ",".join(facets)
    if facets_limit is not None: params["facets_limit"] = facets_limit
    if facets_show_count is not None: params["facets_show_count"] = str(facets_show_count).lower()

    # scoring / sorting / dedup
    if score is not None: params["score"] = str(score).lower()
    if score_min is not None: params["score_min"] = score_min
    if score_max is not None: params["score_max"] = score_max
    if sort_by: params["sort_by"] = sort_by
    if dedup is not None: params["dedup"] = str(dedup).lower()

    # analytics
    if va_uid: params["va_uid"] = va_uid
    if va_sid: params["va_sid"] = va_sid

    # toggles / extras
    if return_fields_mapping is not None: params["return_fields_mapping"] = str(return_fields_mapping).lower()
    if return_product_info is not None: params["return_product_info"] = str(return_product_info).lower()
    if show_pinned_pids is not None: params["show_pinned_pids"] = str(show_pinned_pids).lower()
    if show_excluded_pids is not None: params["show_excluded_pids"] = str(show_excluded_pids).lower()
    if use_set_based_ctl is not None: params["use_set_based_ctl"] = str(use_set_based_ctl).lower()
    if set_limit is not None: params["set_limit"] = set_limit
    if show_best_product_images is not None: params["show_best_product_images"] = str(show_best_product_images).lower()
    if non_product_based_recs is not None: params["non_product_based_recs"] = str(non_product_based_recs).lower()
    if locale: params["locale"] = locale

    # grouping
    if group_by_key: params["group_by_key"] = group_by_key
    if group_limit is not None: params["group_limit"] = group_limit

    # forward-compat
    params.update(extra_params or {})

    s = _session()
    resp = s.get(url, params=params, timeout=30)
    resp.raise_for_status()
    return resp.json()
    