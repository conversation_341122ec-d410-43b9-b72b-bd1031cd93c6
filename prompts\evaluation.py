"""
Evaluation prompts for the <PERSON><PERSON>by agent.
Handles evaluating search result quality and relevance.
"""


def get_evaluation_prompt(search_results: list, customer_query: str, focused_context: str) -> str:
    """
    Generate a prompt for evaluating whether search results satisfy customer needs
    with focused context to reduce drift.
    
    Args:
        search_results (list): List of search results to evaluate
        customer_query (str): Original customer query (tool intent)
        focused_context (str): Focused, relevant context for the current turn
        
    Returns:
        str: A formatted prompt for evaluating search result quality
    """
    results_summary = ""
    if search_results:
        results_summary = f"Found {len(search_results)} products:\n"
        for i, result in enumerate(search_results[:3], 1):
            results_summary += f"{i}. {result.get('title', 'Unknown')} - {result.get('colour', 'No color')} - £{result.get('sale_price', 0)}\n"
    else:
        results_summary = "No products found"

    return f"""
    Customer query: "{customer_query}"
    Focused context: "{focused_context}"

    {results_summary}

    Decide whether these results are acceptable to present now.
    - If any are relevant to the query intent, prefer results_satisfactory: true.
    - If zero or clearly off-intent, set results_satisfactory: false and include suggested_broadening.

    EXAMPLES:

    Customer wanted: "green hoodie"
    Results: 2 green hoodies found
    Response: {{
        "results_satisfactory": true,
        "reasoning": "Found green hoodies matching the request."
    }}

    Customer wanted: "blue dress"
    Results: 1 blue top found (not dress)
    Response: {{
        "results_satisfactory": false,
        "reasoning": "Found blue item but wrong category.",
        "suggested_broadening": {{
            "expand_categories": ["dress", "skirt"],
            "reasoning": "Broaden to show dress options in blue."
        }}
    }}

    Customer wanted: "white cap"
    Results: 1 white cap found
    Response: {{
        "results_satisfactory": true,
        "reasoning": "Found white cap matching the request."
    }}

    Respond with valid JSON matching the schema.
    """
