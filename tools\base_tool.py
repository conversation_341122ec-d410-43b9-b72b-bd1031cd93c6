"""
Base tool class for actual database/API operations.
Tools perform concrete operations like database queries, API calls, file I/O, etc.
"""
from typing import Dict, Any
from abc import ABC, abstractmethod


class BaseTool(ABC):
    """Base class for all tools that perform actual operations"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    def get_name(self) -> str:
        """
        Get the tool name.
        
        Returns:
            Tool name string
        """
        return self.name
    
    @abstractmethod
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the tool's operation.
        
        Args:
            params: Parameters for this execution
            
        Returns:
            Dict containing success status and results
        """
        pass
