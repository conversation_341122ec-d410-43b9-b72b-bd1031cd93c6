# Task Completion Checklist

When completing any coding task in this project, follow these steps:

## Before Starting
1. **Understand Requirements**: Clarify what needs to be done
2. **Check Existing Code**: Look for similar patterns in the codebase
3. **Review Dependencies**: Ensure required packages are installed

## During Development
1. **Follow Code Style**: Match existing patterns and conventions
   - Use type hints for all function parameters and returns
   - Follow snake_case for functions, PascalCase for classes
   - Use async/await for I/O operations

2. **Maintain Architecture**:
   - Keep agents in `agents/` directory
   - Keep tools in `tools/` directory  
   - Register new agents/tools in their respective registries
   - Follow the base class patterns (BaseAgent, BaseTool)

## After Implementation
1. **Code Quality Checks**:
   - Ensure no syntax errors
   - Check that imports are correct
   - Verify type hints are proper
   - Review error handling

2. **Testing**:
   - Run `python test-setup.py` to verify setup
   - Test new functionality manually
   - Run the application to ensure it starts correctly
   - Check API endpoints if modified (`python api.py`)

3. **Integration Testing**:
   - Test in interactive mode: `python main.py`
   - Test via API: `python api.py` and check `/docs`
   - Verify frontend still works if applicable

4. **Documentation**:
   - Update docstrings if adding new classes/functions
   - Update README.md if adding major features
   - Add comments for complex logic

## Git Workflow
1. Check current status: `git status`
2. Review changes: `git diff`
3. Stage changes: `git add .`
4. Commit with descriptive message: `git commit -m "feat: description"`
5. Only push if explicitly requested

## Verification Commands
```bash
# Verify Python syntax (basic check)
python -m py_compile filename.py

# Test API server starts
python api.py

# Test interactive mode
python main.py

# Check Docker services
docker-compose ps
```

## Important Notes
- No dedicated linting tools installed (no ruff, black, flake8)
- No formal test suite beyond test-setup.py
- Focus on manual testing and verification
- Maintain backward compatibility
- Don't break existing functionality