"""
JSON schemas for structured LLM responses - Simplified for action execution
"""


STRATEGY_SCHEMA = {
    "type": "object",
    "properties": {
        "next_best_actions": {
            "type": "array",
            "items": {
                "type": "string",
                "enum": [
                    "ask_follow_up_question",
                    "search_products",
                    "product_details",
                    "suggest_complementary_products",
                    "update_basket",
                    "redirect_irrelevant_query"
                ]
            },
            "description": "Ordered list of tools to execute"
        },
        "tool_parameters": {
            "type": "object",
            "description": "Parameters for each tool (use empty object {} for tools that don't need parameters)",
            "additionalProperties": {
                "type": "object",
                "description": "Tool-specific parameters"
            }
        },
        "reasoning": {
            "type": "string",
            "description": "Why these tools were chosen"
        },
        "irrelevant_query": {
            "type": "boolean",
            "description": "Whether the query is unrelated to fashion retail"
        }
    },
    "required": ["next_best_actions", "tool_parameters", "reasoning", "irrelevant_query"]
}


WEAVIATE_QUERY_SCHEMA = {
    "type": "object",
    "properties": {
        "search_strategy": {
            "type": "string",
            "enum": ["specific_criteria", "semantic_exploration", "category_browse", "color_coordination", "occasion_based"]
        },
        "search_type": {
            "type": "string", 
            "enum": ["hybrid", "semantic", "keyword"]
        },
        "search_terms": {
            "type": "array",
            "items": {"type": "string"}
        },
        "primary_filters": {
            "type": "object",
            "properties": {
                "product_category": {"type": "string"},  
                "color": {"type": "string"},            
                "gender": {"type": "string"},
                "material": {"type": "string"},
                "style": {"type": "string"},
                "occasion": {"type": "string"},
                "pattern": {"type": "string"},
                "brand": {"type": "string"}
            }
        },
        "price_filters": {
            "type": "object", 
            "properties": {
                "min_price": {"type": "number"},
                "max_price": {"type": "number"}
            }
        },
        "result_limit": {"type": "integer"},
        "reasoning": {"type": "string"}
    },
    "required": ["search_strategy", "search_type", "search_terms", "primary_filters", "price_filters", "result_limit", "reasoning"]
}


VISENZE_QUERY_SCHEMA = {
    "type": "object",
    "properties": {
        "search_strategy": {
            "type": "string",
            "enum": ["specific_criteria", "semantic_exploration", "category_browse", "color_coordination", "occasion_based"]
        },
        "search_type": {
            "type": "string", 
            "enum": ["hybrid", "semantic", "keyword"]
        },
        "search_terms": {
            "type": "array",
            "items": {"type": "string"}
        },
        "primary_filters": {
            "type": "object",
            "properties": {
                "category": {"type": "string"},  
                "color": {"type": "string"},            
                "gender": {"type": "string"},
                "material": {"type": "string"},
                "style": {"type": "string"},
                "occasion": {"type": "string"},
                "pattern": {"type": "string"},
                "brand": {"type": "string"}
            }
        },
        "price_filters": {
            "type": "object", 
            "properties": {
                "min_price": {"type": "number"},
                "max_price": {"type": "number"}
            }
        },
        "result_limit": {"type": "integer"},
        "reasoning": {"type": "string"}
    },
    "required": ["search_strategy", "search_type", "search_terms", "primary_filters", "price_filters", "result_limit", "reasoning"]
}


RESULTS_PRESENTATION_SCHEMA = {
    "type": "object",
    "properties": {
        "response_text": {
            "type": "string",
            "description": "The complete response text to send to the customer including product listings"
        }
    },
    "required": ["response_text"]
}


FOLLOW_UP_SCHEMA = {
    "type": "object",
    "properties": {
        "response_text": {
            "type": "string",
            "description": "Follow-up question to ask the customer"
        }
    },
    "required": ["response_text"]
}


IRRELEVANT_REDIRECT_SCHEMA = {
    "type": "object",
    "properties": {
        "response_text": {
            "type": "string",
            "description": "Response redirecting to fashion topics"
        }
    },
    "required": ["response_text"]
}


PURCHASE_SCHEMA = {
    "type": "object",
    "properties": {
        "response_text": {
            "type": "string",
            "description": "Response for handling purchase requests"
        }
    },
    "required": ["response_text"]
}


RESULTS_EVALUATION_SCHEMA = {
    "type": "object",
    "properties": {
        "results_satisfactory": {
            "type": "boolean",
            "description": "Whether the search results are satisfactory for the customer"
        },
        "reasoning": {
            "type": "string", 
            "description": "Explanation of why results are or aren't satisfactory"
        },
        "suggested_broadening": {
            "type": "object",
            "description": "Suggestions for broadening search if results unsatisfactory",
            "properties": {
                "remove_size_filter": {"type": "boolean"},
                "remove_color_filter": {"type": "boolean"},
                "expand_color_options": {"type": "array", "items": {"type": "string"}},
                "expand_sizes": {"type": "array", "items": {"type": "string"}},
                "expand_categories": {"type": "array", "items": {"type": "string"}},
                "reduce_specificity": {"type": "boolean"},
                "reasoning": {"type": "string"}
            }
        }
    },
    "required": ["results_satisfactory", "reasoning"]
}

COMPLEMENTARY_PRODUCTS_SCHEMA = {
    "type": "object",
    "properties": {
        "selected_item": {
            "type": "string",
            "description": "The item the customer has chosen or shown interest in"
        },
        "selected_item_style": {
            "type": "string",
            "description": "Style characteristics of the selected item"
        },
        "complementary_search_focus": {
            "type": "string",
            "description": "What to focus on when searching for complementary items"
        },
        "complementary_categories": {
            "type": "array",
            "items": {"type": "string"},
            "description": "Product categories that would complement the selected item"
        },
        "search_requirements": {
            "type": "string",
            "description": "Specific search requirements for complementary items"
        },
        "style_direction": {
            "type": "string",
            "description": "The overall style direction for complementary items"
        },
        "occasion_context": {
            "type": "string",
            "description": "The occasion or context for wearing these items"
        },
        "reasoning": {
            "type": "string",
            "description": "Why these complementary items were chosen"
        }
    },
    "required": [
        "selected_item",
        "selected_item_style",
        "complementary_search_focus",
        "complementary_categories",
        "search_requirements",
        "style_direction",
        "occasion_context",
        "reasoning"
    ]
}
