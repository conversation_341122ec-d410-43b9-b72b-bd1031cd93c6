"""
Conversation management service handling MongoDB persistence and session state.
Manages conversation history, session persistence, and conversation context.
"""
import uuid
import logging
import asyncio

from typing import List, Optional
from datetime import datetime, timezone
from core.entities import ConversationEntry
from services.mongodb_utils.history_service import ChatHistoryService  
from services.mongodb_utils.conversational_schema import ConversationData

from motor.motor_asyncio import AsyncIOMotorClient


logger = logging.getLogger(__name__)


class ConversationService:
    """
    Manages conversation history and session state persistence using MongoDB.
    
    Handles saving/loading conversation messages, managing session state,
    and providing conversation context for AI decision making.
    """
    
    def __init__(self, mongodb_url: str, database_name: str, collection_name: str):
        """
        Initialise the conversation service with MongoDB connection details.
        
        Args:
            mongodb_url: MongoDB connection string
            database_name: Name of the database to use
            collection_name: Name of the collection for conversation storage
        """
        self.mongodb_url = mongodb_url
        self.database_name = database_name
        self.collection_name = collection_name
        self.mongo_client: Optional[AsyncIOMotorClient] = None
        self.history_service: Optional[ChatHistoryService] = None
        self.connected = False
    
    async def connect(self) -> bool:
        """
        Establish connection to MongoDB for conversation persistence.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Create MongoDB client with timeouts to prevent hanging connections
            self.mongo_client = AsyncIOMotorClient(
                self.mongodb_url,
                serverSelectionTimeoutMS=5000,  # 5 second timeout for server selection
                connectTimeoutMS=5000,          # 5 second timeout for connection
                socketTimeoutMS=5000,           # 5 second timeout for socket operations
                maxPoolSize=1,                  # Limit connection pool size
                minPoolSize=0                   # Start with no connections
            )
            
            self.history_service = ChatHistoryService(
                mongo_client=self.mongo_client,
                mongo_database=self.database_name,
                mongo_collection=self.collection_name
            )
            
            # Test the connection with timeout
            await asyncio.wait_for(
                self.mongo_client.admin.command('ping'),
                timeout=5.0  # 5 second timeout for ping
            )
            
            logger.info(f"Connected to MongoDB: {self.database_name}.{self.collection_name}")
            self.connected = True
            return True
            
        except Exception as e:
            logger.error(f"MongoDB connection failed: {e}")
            self.connected = False
            return False
    
    async def save_message(self, user_id: str, session_id: str, role: str, content: str, metadata: dict = None):
        """
        Save a conversation message to persistent storage.
        
        Args:
            user_id: Unique identifier for the user
            session_id: Unique identifier for the conversation session
            role: Message role ("user" or "assistant")
            content: Message content
            metadata: Optional metadata dictionary
        """
        if not self.connected:
            logger.warning("Not connected to MongoDB, skipping message save")
            return
        
        try:
            conversation_data = ConversationData(
                role=role,
                content=content,
                timestamp=datetime.now(timezone.utc).isoformat()
            )
            
            await self.history_service.save_history(
                user_id=user_id,
                session_id=session_id,
                data=conversation_data
            )
            
            logger.debug(f"Saved {role} message for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to save conversation message: {e}")
    
    async def get_conversation_history(self, user_id: str, session_id: str, 
                                     limit: Optional[int] = None, 
                                     exclude_last: bool = False) -> List[ConversationEntry]:
        """
        Retrieve conversation history from persistent storage.
        
        Args:
            user_id: Unique identifier for the user
            session_id: Unique identifier for the session
            limit: Maximum number of messages to return (None for all)
            exclude_last: Whether to exclude the most recent entry
            
        Returns:
            List of ConversationEntry objects ordered chronologically
        """
        if not self.connected:
            logger.warning("Not connected to MongoDB, returning empty history")
            return []
        
        try:
            history_data = await self.history_service.get_history(
                user_id=user_id,
                session_id=session_id,
                include_obsolete=False
            )
            
            # Convert to ConversationEntry format
            conversation_entries = []
            for entry in history_data:
                conversation_entries.append(
                    ConversationEntry(
                        role=entry["role"],
                        content=entry["content"],
                        timestamp=entry.get("timestamp", datetime.now().isoformat())
                    )
                )
            
            # Apply exclusions and limits
            if exclude_last and conversation_entries:
                conversation_entries = conversation_entries[:-1]
            
            if limit:
                conversation_entries = conversation_entries[-limit:]
            
            return conversation_entries
            
        except Exception as e:
            logger.error(f"Failed to fetch conversation history: {e}")
            return []
    
    async def clear_session_history(self, user_id: str, session_id: str):
        """
        Clear all conversation history for a specific session.
        
        Args:
            user_id: Unique identifier for the user
            session_id: Unique identifier for the session
        """
        if not self.connected:
            logger.warning("Not connected to MongoDB, cannot clear history")
            return
        
        try:
            await self.history_service.mark_history_as_obsolete(user_id, session_id)
            logger.info(f"Cleared conversation history for session {session_id}")
        except Exception as e:
            logger.error(f"Failed to clear conversation history: {e}")
    
    def format_conversation_history(self, entries: List[ConversationEntry], exclude_last: bool = False) -> str:
        """
        Format conversation entries into a string for AI context.
        
        Args:
            entries: List of conversation entries to format
            exclude_last: Whether to exclude the most recent entry
            
        Returns:
            Formatted string representation of the conversation
        """
        if exclude_last and entries:
            entries = entries[:-1]
        
        if not entries:
            return "No previous conversation"
        
        formatted_entries = []
        for entry in entries:
            role = "User" if entry.is_user_message() else "Assistant"
            formatted_entries.append(f"{role}: {entry.content}")
        
        return "\n".join(formatted_entries)
    
    def generate_session_id(self) -> str:
        """
        Generate a new unique session ID.
        
        Returns:
            String representation of a new UUID
        """
        return str(uuid.uuid4())
    
    async def get_session_summary(self, user_id: str, session_id: str) -> dict:
        """
        Generate a summary of the conversation session.
        
        Args:
            user_id: Unique identifier for the user
            session_id: Unique identifier for the session
            
        Returns:
            Dictionary containing session statistics and summary
        """
        try:
            history = await self.get_conversation_history(user_id, session_id)
            
            user_messages = [entry for entry in history if entry.is_user_message()]
            assistant_messages = [entry for entry in history if entry.is_assistant_message()]
            
            # Calculate session duration
            duration = "Just started"
            if len(history) >= 2:
                try:
                    start_time = datetime.fromisoformat(history[0].timestamp.replace('Z', '+00:00'))
                    end_time = datetime.fromisoformat(history[-1].timestamp.replace('Z', '+00:00'))
                    duration_delta = end_time - start_time
                    
                    minutes = int(duration_delta.total_seconds() // 60)
                    if minutes < 1:
                        duration = "Less than a minute"
                    elif minutes == 1:
                        duration = "1 minute"
                    else:
                        duration = f"{minutes} minutes"
                        
                except (ValueError, AttributeError):
                    duration = "Unknown duration"
            
            return {
                "message_count": len(history),
                "user_messages": len(user_messages),
                "assistant_messages": len(assistant_messages),
                "session_duration": duration,
                "session_active": len(history) > 0
            }
            
        except Exception as e:
            logger.error(f"Failed to generate session summary: {e}")
            return {
                "message_count": 0,
                "user_messages": 0,
                "assistant_messages": 0,
                "session_duration": "Unknown",
                "session_active": False
            }
    
    async def close(self):
        """Close the MongoDB connection."""
        try:
            if self.mongo_client:
                # Close the MongoDB client properly
                self.mongo_client.close()
                self.mongo_client = None
                self.connected = False
                logger.info("✅ MongoDB connection closed successfully")
        except Exception as e:
            logger.error(f"Error closing MongoDB connection: {e}")
            # Ensure client is set to None even if close fails
            self.mongo_client = None
            self.connected = False
