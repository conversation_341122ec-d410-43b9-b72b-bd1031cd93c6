"""
Visenze search tool for executing product searches using Visenze API.
"""
from typing import Dict, Any
from .base_tool import BaseTool
from core.entities import SearchQuery
import logging

logger = logging.getLogger(__name__)


class VisenzeSearchTool(BaseTool):
    """Tool for executing actual product searches against Visenze API"""
    
    def __init__(self, search_engine, session_cache_service=None):
        super().__init__(
            name="visenze_search",
            description="Execute product search against Visenze API and return results"
        )
        self.search_engine = search_engine
        self.session_cache_service = session_cache_service
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Get search query from params
            search_query = params.get("search_query")
            context = params.get("context", {})
            
            return await self._execute_single_product_search(params)
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error executing product search"
            }
    
    async def _execute_single_product_search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute single product search using Visenze API"""
        try:
            search_query = params.get("search_query")
            context = params.get("context", {})
            
            if not search_query:
                return {
                    "success": False,
                    "error": "No search query provided",
                    "message": "Search query not found"
                }
            
            # Execute the search - this is the actual API call
            search_state = self.search_engine.search_products(search_query)
            
            if not search_state.has_results():
                return {
                    "success": True,
                    "results": [],
                    "message": "Search completed but no products found",
                    "search_state": search_state
                }
            
            # Enhance search results with available sizes information
            enhanced_results = []
            for product in search_state.current_results:
                available_sizes = getattr(product, 'available_sizes', [product.size] if product.size else [])
                enhanced_product = {
                    "title": product.title,
                    "brand": product.brand,
                    "price": product.sale_price,  # Use sale_price for consistency with search filtering
                    "colour": product.colour,
                    "size": product.size,
                    "category": product.category,
                    "url": product.product_url,
                    "available_sizes": available_sizes,
                    "has_multiple_sizes": len(available_sizes) > 1
                }
                enhanced_results.append(enhanced_product)
            
            # Cache the search results if service available
            if self.session_cache_service and search_state.has_results():
                try:
                    session_id = context.get("session_id", "default")
                    user_id = context.get("user_id", "default")
                    
                    await self.session_cache_service.cache_search_results(
                        session_id=session_id,
                        user_id=user_id,
                        search_terms=search_query.search_terms,
                        products=search_state.current_results,
                        search_type=search_query.search_type
                    )
                    
                    logger.info("Successfully cached search results")
                    
                except Exception as e:
                    logger.warning(f"Failed to cache search results: {e}")
            
            return {
                "success": True,
                "results": search_state.current_results,
                "enhanced_results": enhanced_results,
                "message": f"Search completed successfully. Found {len(search_state.current_results)} products.",
                "search_state": search_state
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error executing product search"
            }
