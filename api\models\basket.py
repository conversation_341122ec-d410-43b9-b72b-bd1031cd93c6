"""
Shopping basket-related Pydantic models.
"""

from typing import Optional, List
from pydantic import BaseModel, Field


class BasketItem(BaseModel):
    name: str
    price: float
    quantity: int
    size: str = ""


class BasketResponse(BaseModel):
    items: List[BasketItem]
    total_items: int
    total_price: float


class AddToBasketRequest(BaseModel):
    product_name: str = Field(..., min_length=1)
    size: str = Field(..., min_length=1)
    quantity: int = Field(1, ge=1)
    conversation_context: Optional[str] = None


class RemoveFromBasketRequest(BaseModel):
    product_name: str = Field(..., min_length=1)
    size: str = Field(..., min_length=1)
    quantity: Optional[int] = Field(None, ge=1)


class UpdateQuantityRequest(BaseModel):
    product_name: str = Field(..., min_length=1)
    size: str = Field(..., min_length=1)
    quantity: int = Field(..., ge=1)
