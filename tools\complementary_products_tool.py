"""
Complementary products tool for the <PERSON><PERSON>by agent.
This tool chains query construction and presentation to suggest complementary products.
"""
import logging
from typing import Dict, Any, List

from .base_tool import BaseTool
from .presentation_tools import PresentProductsTool
from services.llm_service import LLMService
from core.search_engine import SearchEngine

logger = logging.getLogger(__name__)


class SuggestComplementaryProductsTool(BaseTool):
    """Tool for suggesting complementary products by chaining query construction and presentation"""
    
    def __init__(self, llm_service: LLMService, search_engine: SearchEngine):
        super().__init__(
            name="suggest_complementary_products",
            description="Suggest complementary products by constructing search queries and presenting results"
        )
        self.llm_service = llm_service
        self.search_engine = search_engine
        self.presentation_tool = PresentProductsTool(llm_service)
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the complementary products suggestion tool"""
        try:
            product_name = params.get("product_name", "")
            product_category = params.get("product_category", "")
            user_preferences = params.get("user_preferences", "")
            context = params.get("context", {})
            
            if not product_name:
                return {
                    "success": False,
                    "error": "Product name is required",
                    "message": "No product specified for complementary suggestions"
                }
            
            # Step 1: Construct search query for complementary products using LLM service directly
            query_dict = await self.llm_service.construct_search_query(
                f"complementary products for {product_name}",
                f"Find complementary items for {product_name} in {product_category} category. {user_preferences}"
            )
            
            if query_dict.get("error"):
                return {
                    "success": False,
                    "error": f"Query construction failed: {query_dict.get('error')}",
                    "message": "Could not construct search query for complementary products"
                }
            
            # Create SearchQuery object
            from core.entities import SearchQuery
            search_query = SearchQuery(
                search_terms=query_dict.get("search_terms", []),
                primary_filters=query_dict.get("primary_filters", {}),
                price_filters=query_dict.get("price_filters", {}),
                search_type=query_dict.get("search_type", "hybrid"),
                result_limit=query_dict.get("result_limit", 5)
            )
            
            # Step 2: Execute search using the search engine
            try:
                search_state = self.search_engine.search_products(search_query)
                complementary_products = search_state.current_results if search_state.current_results else []
                
                if not complementary_products:
                    return {
                        "success": True,
                        "complementary_products": [],
                        "response_text": f"I couldn't find any complementary products for {product_name} at the moment.",
                        "message": "No complementary products found"
                    }
                
                # Step 3: Present the complementary products
                presentation_params = {
                    "customer_requirements": f"complementary products for {product_name}",
                    "focused_instructions": f"Present complementary items for {product_name}. {user_preferences}",
                    "context": {
                        "current_search_results": complementary_products,
                        "user_input": f"complementary products for {product_name}",
                        "conversation_history": context.get("conversation_history", ""),
                        "focused_instructions": user_preferences,
                    }
                }
                
                presentation_result = await self.presentation_tool.execute(presentation_params)
                if not presentation_result.get("success"):
                    return {
                        "success": False,
                        "error": f"Presentation failed: {presentation_result.get('error', 'Unknown error')}",
                        "message": "Could not present complementary products"
                    }
                
                response_text = presentation_result.get("response_text", "")
                
                return {
                    "success": True,
                    "complementary_products": complementary_products,
                    "response_text": response_text,
                    "search_query": search_query,
                    "message": f"Found {len(complementary_products)} complementary products for {product_name}"
                }
                
            except Exception as e:
                logger.error(f"Error executing search for complementary products: {e}")
                return {
                    "success": False,
                    "error": f"Search execution failed: {str(e)}",
                    "message": "Could not search for complementary products"
                }
            
        except Exception as e:
            logger.error(f"Error in complementary products tool: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Error suggesting complementary products"
            }
    
    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema for the LLM"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "product_name": {
                        "type": "string",
                        "description": "Name of the product to find complementary items for"
                    },
                    "product_category": {
                        "type": "string",
                        "description": "Category of the product (e.g., 'jeans', 'shirts')"
                    },
                    "user_preferences": {
                        "type": "string",
                        "description": "User preferences and context for complementary product suggestions"
                    },
                    "context": {
                        "type": "object",
                        "description": "Additional context for the search"
                    }
                },
                "required": ["product_name"]
            }
        }
