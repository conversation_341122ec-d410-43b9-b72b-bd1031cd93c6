"""
Debug and testing utilities router for the Shoeby Agent API.
"""

import logging
from fastapi import APIRouter, Depends

from ..dependencies import verify_api_key, get_agent
from main import ShoebyAgent

router = APIRouter()


@router.post("/api/debug/toggle-json")
async def toggle_json_debug(
    api_key: str = Depends(verify_api_key),
    shoeby_agent: ShoebyAgent = Depends(get_agent),
):
    """Toggle JSON debug output"""
    shoeby_agent.config.show_json = not shoeby_agent.config.show_json
    return {
        "json_debug_enabled": shoeby_agent.config.show_json,
        "message": f"JSON debug {'enabled' if shoeby_agent.config.show_json else 'disabled'}",
    }


@router.post("/api/debug/json-on")
async def enable_json_debug(shoeby_agent: ShoebyAgent = Depends(get_agent)):
    """Enable detailed JSON debug logging (like CLI 'json on')"""
    shoeby_agent.config.show_json = True
    logger = logging.getLogger(__name__)
    logger.info("📊 API: JSON debug output ENABLED")
    return {"json_debug_enabled": True, "message": "Detailed logging enabled"}


@router.post("/api/debug/json-off")
async def disable_json_debug(shoeby_agent: ShoebyAgent = Depends(get_agent)):
    """Disable detailed JSON debug logging (like CLI 'json off')"""
    shoeby_agent.config.show_json = False
    logger = logging.getLogger(__name__)
    logger.info("📊 API: JSON debug output DISABLED")
    return {"json_debug_enabled": False, "message": "Detailed logging disabled"}


@router.get("/api/debug/status")
async def get_debug_status(shoeby_agent: ShoebyAgent = Depends(get_agent)):
    """Check current debug status"""
    return {
        "json_debug_enabled": shoeby_agent.config.show_json,
        "current_provider": shoeby_agent.llm_service.get_current_provider(),
        "available_providers": shoeby_agent.llm_service.list_available_providers(),
        "log_level": logging.getLogger().level,
    }


@router.post("/api/debug/force-brainpowa")
async def force_brainpowa_provider(shoeby_agent: ShoebyAgent = Depends(get_agent)):
    """Force the provider back to BrainPowa"""
    try:
        success = shoeby_agent.llm_service.force_brainpowa()
        if success:
            return {
                "success": True,
                "message": "Provider forced back to BrainPowa",
                "current_provider": shoeby_agent.llm_service.get_current_provider(),
            }
        else:
            return {
                "success": False,
                "message": "Failed to force BrainPowa provider",
                "current_provider": shoeby_agent.llm_service.get_current_provider(),
            }
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Error forcing BrainPowa provider: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Error occurred while forcing provider",
        }


@router.post("/api/debug/test-search")
async def test_visenze_search(
    query: str = "blue dress",
    search_type: str = "hybrid",
    limit: int = 5,
    shoeby_agent: ShoebyAgent = Depends(get_agent),
):
    """Test Visenze search directly to see logging output"""
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"🧪 DEBUG: Testing Visenze search with query: '{query}'")

        from core.entities import SearchQuery

        # Create a test search query
        test_query = SearchQuery(
            search_terms=[query],
            primary_filters={},
            price_filters={},
            search_type=search_type,
            result_limit=limit,
        )

        # Get the search engine from the agent
        if hasattr(shoeby_agent, "search_engine") and shoeby_agent.search_engine:
            search_engine = shoeby_agent.search_engine
        else:
            # Try to find it in other places
            search_engine = getattr(shoeby_agent, "search_engine", None)
            if not search_engine:
                return {
                    "success": False,
                    "error": "Search engine not available in agent",
                    "message": "Cannot test search - search engine not found",
                }

        # Execute the search
        logger.info("🧪 DEBUG: Executing test search...")
        search_state = search_engine.search_products(test_query)

        results = search_state.current_results if search_state else []

        return {
            "success": True,
            "query": query,
            "search_type": search_type,
            "limit": limit,
            "results_count": len(results),
            "results": [
                {
                    "title": p.title,
                    "brand": p.brand,
                    "price": p.sale_price,
                    "category": p.category,
                    "color": p.colour,
                }
                for p in results[:10]  # Limit to first 10 for readability
            ],
            "search_state": {
                "retry_count": search_state.retry_count if search_state else 0,
                "has_results": search_state.has_results() if search_state else False,
            },
            "message": f"Test search completed. Found {len(results)} products.",
        }

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"❌ Test search error: {e}")
        logger.exception("Full test search error traceback:")
        return {"success": False, "error": str(e), "message": "Test search failed"}
