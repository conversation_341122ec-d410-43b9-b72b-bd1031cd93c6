"""
Purchase-related prompts for the <PERSON><PERSON>by agent.
Handles purchase processing and complementary product suggestions.
"""


def get_complementary_products_prompt(
    product_terms: str, 
    focused_instructions: str
) -> str:
    """
    Generate a prompt for determining what complementary products to search for based on customer context.
    
    Args:
        product_terms (str): The specific product terms to find complementary items for
        focused_instructions (str): Focused, relevant instructions for the current turn
        
    Returns:
        str: A formatted prompt for determining complementary product search strategy
    """
    
    return f"""
Product terms to find complements for: "{product_terms}"
Focused context: "{focused_context}"

You are a fashion styling expert for Shoeby. Based on the conversation, the customer has settled on a specific item and you need to determine what complementary products to search for to complete their look.

analyse THE CONVERSATION TO UNDERSTAND:
- What item they've chosen (extract from history)
- The style/occasion/context
- What complementary categories would work
- Specific search requirements for those categories

Your job is to determine the search strategy for complementary products, which will then be used to construct the actual Weaviate search.

EXAMPLES:

Query: "I really like this black leather jacket"
History: "Customer searched for jackets, found Black Leather Jacket - £79.99, showing interest"
Response: {{
    "selected_item": "Black Leather Jacket",
    "selected_item_style": "edgy, leather, black",
    "complementary_search_focus": "items that complement edgy black leather jacket style",
    "complementary_categories": ["jeans", "t-shirts", "boots", "accessories"],
    "search_requirements": "Dark wash jeans, band t-shirts, combat boots, edgy accessories in black/grey/silver",
    "style_direction": "edgy rock/biker aesthetic",
    "occasion_context": "casual wear, nights out, concerts",
    "reasoning": "Black leather jacket is statement piece - need supporting items that enhance edgy style without competing"
}}

Query: "Perfect for the wedding, what would go with this?"
History: "Customer found Floral Midi Dress for wedding guest outfit, expressed satisfaction"
Response: {{
    "selected_item": "Floral Midi Dress",
    "selected_item_style": "feminine, floral, midi length, wedding guest appropriate",
    "complementary_search_focus": "elegant accessories and pieces for wedding guest outfit",
    "complementary_categories": ["shoes", "accessories", "bags", "outerwear"],
    "search_requirements": "Nude/neutral heeled sandals, delicate jewelry, small clutch bags, light cardigans",
    "style_direction": "elegant feminine wedding guest",
    "occasion_context": "wedding guest, formal daytime event",
    "reasoning": "Floral dress is main piece - need refined accessories that enhance femininity and wedding appropriateness"
}}

Please respond with valid JSON following the schema provided.
"""
