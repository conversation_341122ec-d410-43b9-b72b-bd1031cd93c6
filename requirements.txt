# Core dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
python-multipart>=0.0.6

# OpenAI client (used by BrainPowa provider)
openai>=1.12.0

# Database and search
weaviate-client>=4.4.0 #3.25.0
pymongo>=4.6.0
motor>=3.3.0

# Utilities
python-dotenv>=1.0.0
requests>=2.31.0
pytz>=2023.3

# LangGraph framework (optional)
langgraph>=0.0.40
langchain>=0.1.0
langchain-core>=0.1.0

# OpenTelemetry (observability)
opentelemetry-api>=1.36.0
opentelemetry-sdk>=1.36.0
opentelemetry-exporter-otlp-proto-http>=1.36.0
opentelemetry-instrumentation-logging>=0.57b0
opentelemetry-instrumentation-openai>=0.45.6
opentelemetry-instrumentation-pymongo>=0.57b0
opentelemetry-instrumentation-weaviate>=0.45.6
opentelemetry-instrumentation-fastapi>=0.57b0
