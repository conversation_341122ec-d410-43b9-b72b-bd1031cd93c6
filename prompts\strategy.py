"""
Strategy prompts for the <PERSON>hoeby agent.
Determines what tools to use based on user intent and conversation context.
"""

def get_strategy_prompt(query: str, focused_context: str, available_tools: list, time_context: str = "", balance_config: dict = None) -> str:
    """
    Generate a strategy prompt to determine the next best actions using available tools.
    This prompt generates both strategic decisions and focused instructions for other agents.
    
    Args:
        query (str): The customer's current query or message
        focused_context (str): Focused, relevant context for the current turn
        available_tools (list): List of available tools with their schemas
        time_context (str): Current time context for enhanced decision making (optional)
        balance_config (dict): Configuration for conversation balance (optional)
        
    Returns:
        str: A formatted prompt for determining strategic actions and generating focused instructions
    """
    
    tools_description = "\n".join([
        f"- {tool['name']}: {tool['description']}" 
        for tool in available_tools
        if tool['name'] not in ['construct_weaviate_query', 'evaluate_results', 'present_results', 'present_product_details']
    ])
    
    # Apply conversation balance configuration
    balance_instructions = ""
    if balance_config:
        ratio = balance_config.get('follow_up_vs_products_ratio', 'balanced')
        max_questions = balance_config.get('max_follow_up_questions', 3)
        aggressiveness = balance_config.get('follow_up_aggressiveness', 'medium')
        
        balance_instructions = f"""
CONVERSATION BALANCE:
- Follow-up vs Products Ratio: {ratio}
- Maximum Follow-up Questions: {max_questions}
- Follow-up Aggressiveness: {aggressiveness}
"""
    
    prompt = f"""You are a strategy agent for Shoeby fashion retail. Analyse the customer's query and determine the next best action or actions using available actions.

**CRITICAL: YOU MUST RESPOND WITH VALID JSON ONLY. NO EXPLANATIONS, NO ADDITIONAL TEXT.**

**CUSTOMER QUERY**: {query}

**FOCUSED CONTEXT**: {focused_context}

**TIME CONTEXT**: {time_context}

**AVAILABLE ACTIONS**:
{tools_description}
{balance_instructions}

**DECISION RULES**:

1. **PRODUCT SEARCH**: If user references specific products not just presented → use search_products

2. **PRODUCT DETAILS**: If user asks about details of previously presented products → use product_details

3. **BASKET OPERATIONS**: If user wants to add/remove items → use update_basket or basket_info

4. **FOLLOW-UP QUESTIONS**: If user needs clarification or more details → use ask_follow_up_question

5. **COMPLEMENTARY PRODUCTS**: Only suggest after user shows interest in initial products → use suggest_complementary_products

6. **SIZE RESPONSES**: If user input is ONLY a size (e.g., "28", "146", "M", "L", "XL") → use update_basket
   - **PRODUCT EXTRACTION**: For size responses, extract the EXACT product name from the focused context
   - Look for products that were recently presented to the user
   - Focus on products that come in multiple sizes (as the user is providing a size)
   - If user said "the first pair", "the first one", "add the first", etc., identify the FIRST product mentioned
   - If user said "the black one", "the beige one", etc., identify the product with that colour
   - Extract the EXACT product name as it appears in the conversation (including the full name with price)
   - If no clear product can be identified, set extracted_product to empty string

7. **NON-SHOBEY BRANDS**: If user requests specific non-Shoeby brands (Adidas, Nike, Zara, H&M, Gucci, etc.) → use ask_follow_up_question

**CORE PRINCIPLES**:
- Maintain conversation continuity
- Prefer search over clarification when sufficient detail exists
- Always provide action parameters
- Pass full focused_context to tools

**RESPONSE FORMAT**:
You MUST respond with ONLY valid JSON in this exact format:
{{"next_best_actions": ["action_name1", "action_name2"], "action_parameters": {{"action_name1": {{"param": "value"}}}}, "focused_instructions": "Detailed instructions for other agents based on the focused context", "reasoning": "brief explanation", "extracted_product": "exact product name if size response, empty string otherwise"}}

**FOCUSED INSTRUCTIONS GENERATION**:
Based on the focused context, generate specific, actionable instructions that other agents should follow. These instructions should:
- Extract key information from the focused context
- Provide specific guidance for each agent type (search, presentation, conversation, etc.)
- Include relevant product names, user preferences, and conversation state
- Be concise but comprehensive enough for agents to act effectively

**EXAMPLES**:
- Size response: {{"next_best_actions": ["update_basket"], "action_parameters": {{"update_basket": {{"action": "add", "product_name": "Skinny Jeans Dark Blue", "size": "176"}}}}, "focused_instructions": "User provided size 176 for Skinny Jeans Dark Blue. Add this specific product with the given size to basket. This is a direct purchase action.", "reasoning": "User provided size 176", "extracted_product": "Skinny Jeans Dark Blue"}}
- Product search: {{"next_best_actions": ["search_products"], "action_parameters": {{"search_products": {{"customer_requirements": "blue jeans"}}}}, "focused_instructions": "User is looking for blue jeans. Search for denim products in blue colour. Focus on jeans specifically, not other blue clothing items. Present results in a clear, organized manner.", "reasoning": "User wants blue jeans", "extracted_product": ""}}
- Follow-up: {{"next_best_actions": ["ask_follow_up_question"], "action_parameters": {{"ask_follow_up_question": {{"user_query": "blue jeans""}}}}, "focused_instructions": "User's request for blue jeans is too vague. Ask clarifying questions about style (skinny, straight, bootcut), budget range, size, and occasion. Gather specific requirements before searching.", "reasoning": "Need more details", "extracted_product": ""}}"""
    
    return prompt
