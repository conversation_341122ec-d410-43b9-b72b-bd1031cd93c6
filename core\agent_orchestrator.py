"""
Agent Orchestrator for the Shoeby Conversational Agent.

This module provides the main agent orchestration using LangGraph framework,
handling the complete workflow from user input to response generation.
"""
import time
import logging

from config.settings import AgentConfig
from prompts.context import get_context_extraction_prompt
from typing import Dict, Any, <PERSON>, Tuple, TypedDict, Annotated
from config.constants import ErrorMessages, ContextMessages, LogMessages, ResponseMessages, ActionMessages

# LangGraph imports
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage


logger = logging.getLogger(__name__)


class ShoebyState(TypedDict):
    """State definition for the Shoeby LangGraph workflow."""
    messages: Annotated[List[BaseMessage], add_messages]
    user_input: str
    conversation_history: str
    focused_context: str
    focused_instructions: str
    time_context: str
    strategy_decision: Dict[str, Any]
    actions: List[str]
    response: str
    reasoning_data: Dict[str, Any]
    error: str
    current_search_results: List[Any]
    enhanced_search_results: List[Any]


class AgentOrchestrator:
    """
    Main agent orchestrator using LangGraph framework.
    
    This orchestrator uses LangGraph's graph-based state machine for orchestration,
    providing robust agent coordination and state management.
    """
    
    def __init__(self, config: AgentConfig):
        """Initialise the agent orchestrator."""
        self.config = config
        self.llm_service = None
        self.conversation_service = None
        self.datetime_service = None
        self.session_cache = None
        self.search_engine = None
        self.agents_registry = None
        self.tools_registry = None
        self._current_session_id = None
        self._current_user_id = None
        self._conversation_history = ""  # Store conversation history in memory
        self.basket_tool = None
        self.graph = None
        self.initialised = False
    
    async def setup(self) -> bool:
        """
        Set up the orchestrator and all required services.
        
        Returns:
            True if setup successful, False otherwise
        """
        logger.info("🔧 Setting up Shoeby agent orchestrator...")
        
        try:
            # Set up common services
            setup_success = await self._setup_common_services()
            if not setup_success:
                logger.error("❌ Common services setup failed")
                return False
            
            # Set up LangGraph-specific components
            langgraph_success = await self._setup_langgraph_components()
            if not langgraph_success:
                logger.error("❌ LangGraph components setup failed")
                return False
            
            self.initialised = True
            logger.info("✅ Agent orchestrator setup complete")
            return True
            
        except Exception as e:
            logger.error(f"❌ Agent orchestrator setup failed: {e}")
            return False
    
    async def _setup_common_services(self) -> bool:
        """
        Set up common services that the orchestrator uses.
        
        Returns:
            True if setup successful, False otherwise
        """
        try:
            # Import services here to avoid circular imports
            from services.llm_service import LLMService
            from services.conversation_service import ConversationService
            from services.datetime_context import DateTimeContextService
            from services.mongodb_utils.session_cache import SessionCacheService
            from core.visenze_search_engine import VisenzeSearchEngine
            from agents.registry import AgentsRegistry
            
            # Initialise LLM service
            self.llm_service = LLMService(self.config)
            logger.info("✅ LLM service initialised")
            
            # Initialise search engine
            self.search_engine = VisenzeSearchEngine.from_config(self.config)
            logger.info("✅ Visenze search engine initialised")
            
            # Initialise conversation service
            self.conversation_service = ConversationService(
                self.config.mongodb_url,
                self.config.mongodb_database,
                self.config.mongodb_collection
            )
            
            # Connect to MongoDB
            connection_success = await self.conversation_service.connect()
            if not connection_success:
                logger.warning("⚠️ MongoDB connection failed - conversation history will not be persisted")
            else:
                logger.info("✅ Conversation service initialised")
            
            # Initialise datetime context service
            self.datetime_service = DateTimeContextService(self.config.timezone_name)
            logger.info("✅ DateTime context service initialised")
            
            # Initialise session cache service
            self.session_cache = SessionCacheService(
                self.config.mongodb_url,
                self.config.mongodb_database
            )
            
            # Connect to MongoDB
            cache_connection_success = await self.session_cache.connect()
            if not cache_connection_success:
                logger.warning("⚠️ Session cache MongoDB connection failed - caching will not be available")
            else:
                logger.info("✅ Session cache service initialised")
            
            # Initialise agents registry
            self.agents_registry = AgentsRegistry()
            logger.info("✅ Agents registry initialised")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Common services setup failed: {e}")
            return False
    
    async def _setup_langgraph_components(self) -> bool:
        """
        Set up LangGraph-specific components.
        
        Returns:
            True if setup successful, False otherwise
        """
        try:
            # Import tools registry
            from tools.registry import ToolsRegistry
            
            # Initialise tools registry
            self.tools_registry = ToolsRegistry()
            logger.info("✅ Tools registry initialised")
            
            # Register all tools
            await self._register_tools()
            
            # Register all agents
            await self._register_agents()
            
            # Build the LangGraph workflow
            self.graph = self._build_workflow()
            logger.info("✅ LangGraph workflow built")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ LangGraph components setup failed: {e}")
            return False
    
    async def _register_tools(self):
        """Register all available tools in the tools registry."""
        try:
            from tools import (
                UpdateBasketTool, 
                BasketInfoTool,
                PresentProductsTool,
                SuggestComplementaryProductsTool,
                ProductDetailsTool
            )
            
            # Register basket tool
            self.basket_tool = UpdateBasketTool(self.session_cache)
            self.tools_registry.register_tool(self.basket_tool)
            
            # Note: Conversation tools (AskFollowUpQuestionTool, RedirectIrrelevantQueryTool) 
            # have been removed. These functionalities are now handled directly by the LLM service.
            
            # Register basket info tool
            self.basket_info_tool = BasketInfoTool(self.llm_service)
            self.tools_registry.register_tool(self.basket_info_tool)
            
            # Register presentation tool
            self.presentation_tool = PresentProductsTool(self.llm_service)
            self.tools_registry.register_tool(self.presentation_tool)
            
            # Note: Search tools (ConstructWeaviateQueryTool, EvaluateProductsTool) 
            # have been removed. These functionalities are now handled directly by the LLM service.
            
            # Register complementary products tool
            self.tools_registry.register_tool(SuggestComplementaryProductsTool(self.llm_service, self.search_engine))
            
            # Register product details tool
            self.tools_registry.register_tool(ProductDetailsTool(self.session_cache))
            
            logger.info("✅ Tools registered successfully")
            
        except Exception as e:
            logger.error(f"Error registering tools: {e}")
    
    async def _register_agents(self):
        """Register all available agents in the agents registry."""
        try:
            from agents import (
                StrategyAgent
            )
            
            # Register strategy agent
            self.agents_registry.register_agent(
                StrategyAgent(self.llm_service)
            )
            
            logger.info("✅ Agents registered successfully")
            
        except Exception as e:
            logger.error(f"Error registering agents: {e}")
    
    def _build_workflow(self) -> StateGraph:
        """
        Build the LangGraph workflow for agent orchestration.
        
        Returns:
            Configured StateGraph instance
        """
        # Create the state graph
        workflow = StateGraph(ShoebyState)
        
        # Add nodes for each step in the workflow
        workflow.add_node("receive_input", self._receive_input)
        workflow.add_node("get_context", self._get_context)
        workflow.add_node("decide_strategy", self._decide_strategy)
        workflow.add_node("execute_search", self._execute_search)
        workflow.add_node("evaluate_products", self._evaluate_products)
        workflow.add_node("present_products", self._present_products)
        workflow.add_node("get_product_details", self._get_product_details)
        workflow.add_node("present_product_details", self._present_product_details)
        workflow.add_node("get_complementary_products", self._get_complementary_products)
        workflow.add_node("present_complementary_products", self._present_complementary_products)
        workflow.add_node("handle_conversation", self._handle_conversation)
        workflow.add_node("generate_response", self._generate_response)
        workflow.add_node("handle_error", self._handle_error)
        
        # Define the workflow edges
        workflow.set_entry_point("receive_input")
        
        workflow.add_edge("receive_input", "get_context")
        workflow.add_edge("get_context", "decide_strategy")
        
        # Conditional routing based on strategy
        workflow.add_conditional_edges(
            "decide_strategy",
            self._route_strategy,
            {
                "search": "execute_search",
                "product_details": "get_product_details",
                "suggest_complementary_products": "get_complementary_products",
                "conversation": "handle_conversation",
                "basket": "handle_conversation",  # Basket operations go to conversation handler
                "error": "handle_error"
            }
        )
        
        workflow.add_edge("execute_search", "evaluate_products")
        workflow.add_edge("evaluate_products", "present_products")
        workflow.add_edge("present_products", "generate_response")
        
        # Product details workflow
        workflow.add_edge("get_product_details", "present_product_details")
        workflow.add_edge("present_product_details", "generate_response")
        
        # Complementary products workflow
        workflow.add_edge("get_complementary_products", "present_complementary_products")
        workflow.add_edge("present_complementary_products", "generate_response")
        
        workflow.add_edge("handle_conversation", "generate_response")
        workflow.add_edge("handle_error", "generate_response")
        workflow.add_edge("generate_response", END)
        
        return workflow.compile()
    
    async def _receive_input(self, state: ShoebyState) -> ShoebyState:
        """Receive and process user input."""
        start_time = time.time()
        try:
            user_input = state.get("user_input", "")
            
            # Add user message to conversation
            messages = state.get("messages", [])
            messages.append(HumanMessage(content=user_input))
            
            response_time = time.time() - start_time
            
            return {
                **state,
                "messages": messages,
                "user_input": user_input
            }
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"Error in receive_input after {response_time:.3f}s: {e}")
            return {**state, "error": str(e)}
    
    async def _get_context(self, state: ShoebyState) -> ShoebyState:
        """Get conversation and time context, and extract focused context."""
        start_time = time.time()
        try:
            # Get conversation history from memory
            conversation_history = self._conversation_history
            
            # Get time context
            time_context_obj = self.datetime_service.get_current_time_context()
            time_context = f"Current time: {time_context_obj.current_time.strftime('%Y-%m-%d %H:%M:%S')}, Season: {time_context_obj.season}, Day: {time_context_obj.day_name}"
            
            # Generate focused context using context extraction prompt
            user_input = state.get("user_input", "")
            if not user_input:
                raise ValueError("User input is required but not provided")
            
            # # For first interaction, use a default context if no conversation history exists
            # if not conversation_history:
            #     conversation_history = "First interaction with customer"
            #     logger.info("🆕 First interaction - using default conversation context")
            
            context_prompt = get_context_extraction_prompt(conversation_history, user_input)
            focused_context = await self.llm_service._get_context_summary(context_prompt)
            logger.info(f"🧠 Generated focused context: {focused_context}...")
            
            response_time = time.time() - start_time
            logger.info(f"⏱️ Context extraction completed in {response_time:.3f}s")
            
            return {
                **state,
                "conversation_history": conversation_history,
                "focused_context": focused_context,
                "time_context": time_context
            }
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"Error in get_context after {response_time:.3f}s: {e}")
            return {**state, "error": str(e)}
    
    async def _decide_strategy(self, state: ShoebyState) -> ShoebyState:
        """Decide on the strategy for handling the user input."""
        start_time = time.time()
        try:            
            # Use strategy agent to decide on approach 
            from agents.strategy_agent import StrategyAgent
            strategy_agent = StrategyAgent(self.llm_service)
            
            strategy_decision = await strategy_agent.decide_strategy(
                user_input=state["user_input"],
                conversation_history=state.get("conversation_history", ""),
                time_context=state.get("time_context", "")
            )
            
            response_time = time.time() - start_time
            logger.info(f"⏱️ Strategy decision completed in {response_time:.3f}s")
            
            return {
                **state,
                "strategy_decision": strategy_decision,
                "focused_instructions": strategy_decision.get("focused_instructions", "")
            }
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"Error in decide_strategy after {response_time:.3f}s: {e}")
            return {**state, "error": str(e)}
    
    def _route_strategy(self, state: ShoebyState) -> str:
        """Route to appropriate workflow based on strategy decision."""
        strategy = state.get("strategy_decision", {})
        action = strategy.get("action", "search")
        
        if action == "search":
            return "search"
        elif action == "product_details":
            return "product_details"
        elif action == "suggest_complementary_products":
            return "suggest_complementary_products"
        elif action in ["conversation", "basket"]:
            return "conversation"
        else:
            return "error"
    
    async def _execute_search(self, state: ShoebyState) -> ShoebyState:
        """Execute product search based on user input."""
        try:
            # Use LLM service directly to construct search query
            query_dict = await self.llm_service.construct_search_query(
                state["user_input"], 
                state.get("focused_instructions", "")
            )
            
            if query_dict.get("error"):
                return {**state, "error": f"{ErrorMessages.QUERY_CONSTRUCTION_FAILED}: {query_dict.get('error')}"}
            
            # Create SearchQuery object
            from core.entities import SearchQuery
            search_query = SearchQuery(
                search_terms=query_dict.get("search_terms", []),
                primary_filters=query_dict.get("primary_filters", {}),
                price_filters=query_dict.get("price_filters", {}),
                search_type=query_dict.get("search_type", "hybrid"),
                result_limit=query_dict.get("result_limit", 5)
            )
            
            # Execute search using search engine
            search_state = self.search_engine.search_products(search_query)
            products = search_state.current_results if search_state.current_results else []
            
            logger.info(f"🔍 Search executed: Found {len(products)} products")
            logger.info(f"🔍 First product: {products[0] if products else 'None'}")
            
            return {
                **state,
                "actions": state.get("actions", []) + [ActionMessages.SEARCH_EXECUTED],
                "current_search_results": products,  # Store products for presentation agent
                "reasoning_data": {
                    **state.get("reasoning_data", {}),
                    "search_query": search_query,
                    "products_found": len(products) if products else 0
                }
            }
        except Exception as e:
            logger.error(LogMessages.SEARCH_EXECUTION_ERROR.format(error=e))
            return {**state, "error": str(e)}
    
    async def _evaluate_products(self, state: ShoebyState) -> ShoebyState:
        """Evaluate found products."""
        try:
            # Use LLM service directly to evaluate products
            search_results = state.get("current_search_results", [])
            if not search_results:
                return {**state, "error": "No search results to evaluate"}
            
            # Convert products to dictionaries for LLM processing
            results_dict = []
            for product in search_results:
                if hasattr(product, 'title'):
                    results_dict.append({
                        "title": product.title,
                        "brand": product.brand,
                        "price": getattr(product, 'get_display_price', lambda: 0.0)(),
                        "colour": product.colour,
                        "size": product.size,
                        "category": product.category,
                        "url": product.product_url,
                    })
                else:
                    results_dict.append({
                        "title": product.get("title", ""),
                        "brand": product.get("brand", ""),
                        "price": product.get("price", 0.0),
                        "colour": product.get("colour", ""),
                        "size": product.get("size", ""),
                        "category": product.get("category", ""),
                        "url": product.get("url", ""),
                    })
            
            evaluation_result = await self.llm_service.evaluate_search_results(
                results_dict, 
                state["user_input"], 
                state.get("focused_instructions", "")
            )
            
            return {
                **state,
                "actions": state.get("actions", []) + [ActionMessages.PRODUCTS_EVALUATED],
                "reasoning_data": {
                    **state.get("reasoning_data", {}),
                    "evaluation": evaluation_result
                }
            }
        except Exception as e:
            logger.error(LogMessages.PRODUCT_EVALUATION_ERROR.format(error=e))
            return {**state, "error": str(e)}
    
    async def _present_products(self, state: ShoebyState) -> ShoebyState:
        """Present products to the user."""
        try:
            logger.info(f"📊 Present products: current_search_results = {len(state.get('current_search_results', []))} products")
            logger.info(f"📊 Present products: state keys = {list(state.keys())}")
            
            # Use presentation tool to format response
            present_tool = self.tools_registry.get_tool("present_results")
            if present_tool:
                presentation_result = await present_tool.execute(
                    params={
                        "customer_requirements": state["user_input"],
                        "focused_instructions": state.get("focused_instructions"),
                        "context": {
                            "current_search_results": state.get("current_search_results", []),
                            "enhanced_search_results": state.get("enhanced_search_results"),
                            "user_input": state["user_input"],
                            "conversation_history": state.get("conversation_history", ""),
                            "focused_instructions": state.get("focused_instructions"),
                            "reasoning_data": state.get("reasoning_data", {})
                        }
                    }
                )
                
                if presentation_result.get("success"):
                    response = presentation_result.get("response_text", ResponseMessages.DEFAULT_PRODUCT_RESPONSE)
                    return {
                        **state,
                        "response": response,
                        "actions": state.get("actions", []) + [ActionMessages.PRODUCTS_PRESENTED]
                    }
                else:
                    return {**state, "error": f"{ErrorMessages.PRODUCT_PRESENTATION_FAILED}: {presentation_result.get('error', ErrorMessages.UNKNOWN_ERROR)}"}
            
            return {**state, "error": "Presentation tool not available"}
        except Exception as e:
            logger.error(LogMessages.PRODUCT_PRESENTATION_ERROR.format(error=e))
            return {**state, "error": str(e)}
    
    async def _get_product_details(self, state: ShoebyState) -> ShoebyState:
        """Get detailed product information."""
        try:
            # Use product details tool
            product_details_tool = self.tools_registry.get_tool("get_product_details")
            if not product_details_tool:
                raise RuntimeError("Product details tool not available")
            
            product_details_result = await product_details_tool.execute(
                params={
                    "user_query": state["user_input"],
                    "focused_instructions": state.get("focused_instructions"),
                    "context": state
                }
            )
            
            if not product_details_result.get("success"):
                raise RuntimeError(f"Product details retrieval failed: {product_details_result.get('error', 'Unknown error')}")
            
            return {
                **state,
                "response": product_details_result.get("response"),
                "actions": state.get("actions", []) + [ActionMessages.PRODUCT_DETAILS_RETRIEVED]
            }
        except Exception as e:
            logger.error(f"Error in get_product_details: {e}")
            return {**state, "error": str(e)}
    
    async def _present_product_details(self, state: ShoebyState) -> ShoebyState:
        """Present product details to the user."""
        try:
            # Product details are already formatted by the tool
            # This node can add additional formatting if needed
            response = state.get("response", "")
            
            return {
                **state,
                "response": response,
                "actions": state.get("actions", []) + [ActionMessages.PRODUCT_DETAILS_PRESENTED]
            }
        except Exception as e:
            logger.error(f"Error in present_product_details: {e}")
            return {**state, "error": str(e)}
    
    async def _get_complementary_products(self, state: ShoebyState) -> ShoebyState:
        """Get complementary products."""
        try:
            # Use complementary products tool
            complementary_tool = self.tools_registry.get_tool("suggest_complementary_products")
            if not complementary_tool:
                raise RuntimeError("Complementary products tool not available")
            
            complementary_result = await complementary_tool.execute(
                params={
                    "user_query": state["user_input"],
                    "focused_instructions": state.get("focused_instructions"),
                    "context": state
                }
            )
            
            if not complementary_result.get("success"):
                raise RuntimeError(f"Complementary products retrieval failed: {complementary_result.get('error', 'Unknown error')}")
            
            return {
                **state,
                "response": complementary_result.get("response"),
                "actions": state.get("actions", []) + [ActionMessages.COMPLEMENTARY_PRODUCTS_RETRIEVED]
            }
        except Exception as e:
            logger.error(f"Error in get_complementary_products: {e}")
            return {**state, "error": str(e)}
    
    async def _present_complementary_products(self, state: ShoebyState) -> ShoebyState:
        """Present complementary products to the user."""
        try:
            # Complementary products are already formatted by the tool
            # This node can add additional formatting if needed
            response = state.get("response", "")
            
            return {
                **state,
                "response": response,
                "actions": state.get("actions", []) + [ActionMessages.COMPLEMENTARY_PRODUCTS_PRESENTED]
            }
        except Exception as e:
            logger.error(f"Error in present_complementary_products: {e}")
            return {**state, "error": str(e)}
    
    async def _handle_conversation(self, state: ShoebyState) -> ShoebyState:
        """Handle conversational interactions and basket operations."""
        try:
            strategy = state.get("strategy_decision", {})
            action = strategy.get("action", "conversation")
            
            # Handle basket operations 
            if action == "basket":
                return await self._handle_basket_operation(state)
            
            # Use LLM service directly for follow-up questions
            response = await self.llm_service.generate_follow_up_question_async(
                state["user_input"],
                state.get("focused_instructions", ""),
                "",
                state
            )
            
            if not response:
                raise RuntimeError("LLM service returned empty response")
            
            return {
                **state,
                "response": response,
                "actions": state.get("actions", []) + [ActionMessages.CONVERSATION_HANDLED]
            }
        except Exception as e:
            logger.error(LogMessages.CONVERSATION_HANDLING_ERROR.format(error=e))
            return {**state, "error": str(e)}
    
    async def _handle_basket_operation(self, state: ShoebyState) -> ShoebyState:
        """Handle basket operations like adding items with size confirmation."""
        try:
            user_input = state["user_input"].strip()
            conversation_history = state.get("conversation_history", "")
            
            # Check if this is a size response (just a size number/letter)
            # Simple heuristic: if input is 1-3 characters and looks like a size
            if len(user_input) <= 3 and (user_input.isdigit() or user_input.upper() in ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL']):
                # This is a size response - get the extracted product from strategy decision
                logger.info(f"🔍 Using extracted product from strategy for size response: '{user_input}'")
                strategy_decision = state.get("strategy_decision", {})
                product_name = strategy_decision.get("extracted_product", "")
                
                if not product_name:
                    # Use LLM service directly to generate a dynamic clarification request
                    response = await self.llm_service.generate_follow_up_question_async(
                        "Could not identify which product to add to basket",
                        f"User wants to add something to basket but I couldn't identify which product from: {conversation_history}",
                        "",
                        state
                    )
                    
                    if not response:
                        raise RuntimeError("LLM service returned empty response")
                    
                    return {
                        **state,
                        "response": response,
                        "actions": state.get("actions", []) + ["basket_clarification_needed"]
                    }
                
                # Get current basket state before update
                basket_tool = self.tools_registry.get_tool("update_basket")
                if not basket_tool:
                    raise RuntimeError("Basket tool 'update_basket' not available")
                
                previous_basket_data = basket_tool.get_basket_data()
                
                # Use basket tool to add the item with the specified size
                basket_result = await basket_tool.execute(
                    params={
                        "action": "add",
                        "product_name": product_name,
                        "size": user_input,
                        "focused_instructions": state.get("focused_instructions")
                    }
                )
                
                if not basket_result.get("success"):
                    raise RuntimeError(f"Basket operation failed: {basket_result.get('error', 'Unknown error')}")
                
                # Get updated basket state
                current_basket_data = basket_result.get("basket_status", {})
                current_basket_data["items"] = basket_result.get("basket_items", [])
                
                # Generate dynamic response using basket info tool
                basket_info_tool = self.tools_registry.get_tool("basket_info")
                if basket_info_tool:
                    basket_info_params = {
                        "basket_data": current_basket_data,
                        "action": "add",
                        "focused_instructions": state.get("focused_instructions"),
                        "product_name": product_name,
                        "previous_basket_data": previous_basket_data
                    }
                    
                    basket_info_result = await basket_info_tool.execute(basket_info_params)
                    if basket_info_result.get("success"):
                        basket_response = basket_info_result.get("response_text", "")
                        logger.info(f"🛒 Generated basket response: {basket_response[:100]}...")
                    else:
                        logger.warning(f"🛒 Failed to generate basket response: {basket_info_result.get('error', 'Unknown error')}")
                        basket_response = f"Added {product_name} to your basket"
                else:
                    basket_response = f"Added {product_name} to your basket"
                
                # Get complementary product suggestions using the dedicated tool
                complementary_tool = self.tools_registry.get_tool("suggest_complementary_products")
                complementary_response = ""
                
                if complementary_tool:
                    logger.info(f"🛍️ Triggering complementary products tool for: {product_name}")
                    complementary_result = await complementary_tool.execute(
                        params={
                            "product_name": product_name,
                            "product_category": "jeans",
                            "user_preferences": state.get("focused_instructions"),
                            "context": {
                                "current_search_results": state.get("current_search_results", []),
                                "enhanced_search_results": state.get("enhanced_search_results"),
                                "user_input": ContextMessages.BASKET_ADD_SUCCESS.format(product_name=product_name, size=user_input),
                                "conversation_history": state.get("conversation_history", ""),
                                "focused_instructions": state.get("focused_instructions"),
                                "basket_result": basket_result,
                                "product_name": product_name,
                                "size": user_input
                            }
                        }
                    )
                    
                    if complementary_result.get("success"):
                        complementary_response = complementary_result.get("response_text", "")
                        logger.info(f"🛍️ Complementary products suggested: {complementary_response[:100]}...")
                    else:
                        logger.warning(f"🛍️ Failed to get complementary products: {complementary_result.get('error', 'Unknown error')}")
                
                # Use multi-tool presentation to combine basket update and complementary products
                present_tool = self.tools_registry.get_tool("present_results")
                if not present_tool:
                    raise RuntimeError("Presentation tool 'present_results' not available")
                
                # Prepare tool results for multi-tool presentation
                executed_tools = ["update_basket", "basket_info", "suggest_complementary_products"]
                tool_results = {
                    "update_basket": basket_result,
                    "basket_info": {
                        "success": True,
                        "response_text": basket_response,
                        "basket_data": current_basket_data
                    },
                    "suggest_complementary_products": {
                        "success": True,
                        "response_text": complementary_response,
                        "complementary_products": complementary_result.get("complementary_products", []) if complementary_result.get("success") else []
                    }
                }
                
                # Create context for multi-tool presentation
                presentation_context = {
                    "is_multi_tool_presentation": True,
                    "executed_tools": executed_tools,
                    "tool_results": tool_results,
                    "current_search_results": state.get("current_search_results", []),
                    "enhanced_search_results": state.get("enhanced_search_results"),
                    "user_input": ContextMessages.BASKET_ADD_SUCCESS.format(product_name=product_name, size=user_input),
                    "conversation_history": state.get("conversation_history", ""),
                    "focused_context": state.get("focused_context"),
                    "product_name": product_name,
                    "size": user_input
                }
                
                # Generate combined response using multi-tool presentation
                presentation_result = await present_tool.execute(
                    params={
                        "customer_requirements": ContextMessages.BASKET_ADD_SUCCESS.format(product_name=product_name, size=user_input),
                        "focused_instructions": state.get("focused_instructions"),
                        "context": presentation_context
                    }
                )
                
                if not presentation_result.get("success"):
                    raise RuntimeError(f"Presentation failed: {presentation_result.get('error', 'Unknown error')}")
                
                response = presentation_result.get("response_text")
                if not response:
                    raise RuntimeError("Presentation agent returned empty response")
                
                return {
                    **state,
                    "response": response,
                    "actions": state.get("actions", []) + [ActionMessages.ITEM_ADDED_TO_BASKET]
                }
            else:
                # This is a basket addition request - use LLM service directly
                response = await self.llm_service.generate_follow_up_question_async(
                    state["user_input"],
                    state.get("focused_instructions", ""),
                    "",
                    state
                )
                
                if not response:
                    raise RuntimeError("LLM service returned empty response")
                
                return {
                    **state,
                    "response": response,
                    "actions": state.get("actions", []) + ["conversation_handled"]
                }
                
        except Exception as e:
            logger.error(f"Error in handle_basket_operation: {e}")
            raise  # Re-raise the exception to break the code as requested
    
    async def _generate_response(self, state: ShoebyState) -> ShoebyState:
        """Generate final response."""
        try:
            response = state.get("response")
            if not response:
                raise RuntimeError("No response in state")
            
            # Add AI message to conversation
            messages = state.get("messages", [])
            messages.append(AIMessage(content=response))
            
            return {
                **state,
                "messages": messages,
                "response": response
            }
        except Exception as e:
            logger.error(f"Error in generate_response: {e}")
            raise  # Re-raise the exception to break the code 
    
    async def _handle_error(self, state: ShoebyState) -> ShoebyState:
        """Handle errors in the workflow."""
        error_msg = state.get("error")
        if not error_msg:
            raise RuntimeError("No error message in state")
        
        logger.error(LogMessages.WORKFLOW_ERROR.format(error_msg=error_msg))
        raise RuntimeError(f"Workflow error: {error_msg}")  # Re-raise to break the code as requested
    
    async def process_query_with_reasoning(self, user_input: str) -> Tuple[str, Dict[str, Any]]:
        """
        Process a user query and return both response and reasoning data.
        
        Args:
            user_input: User's input message
            
        Returns:
            Tuple of (response_text, reasoning_data)
        """
        total_start_time = time.time()
        try:
            # Initialise state
            initial_state = ShoebyState(
                messages=[],
                user_input=user_input,
                conversation_history="",
                focused_context="",
                focused_instructions="",
                time_context="",
                strategy_decision={},
                actions=[],
                response="",
                reasoning_data={},
                error="",
                current_search_results=[],
                enhanced_search_results=[],
            )
            
            # Run the workflow
            workflow_start_time = time.time()
            result = await self.graph.ainvoke(initial_state)
            workflow_time = time.time() - workflow_start_time
            
            # Extract response and reasoning
            response = result.get("response")
            if not response:
                raise RuntimeError("No response generated from workflow")
            reasoning_data = {
                "framework": "langgraph",
                "user_query": user_input,
                "timestamp": time.time(),
                "strategy_decision": result.get("strategy_decision", {}),
                "actions": result.get("actions", []),
                "reasoning_data": result.get("reasoning_data", {}),
                "error": result.get("error", ""),
                "workflow_time": workflow_time
            }
            
            # Update conversation history
            self._update_conversation_history(user_input, response)
            
            total_time = time.time() - total_start_time
            logger.info(f"⏱️ Total query processing completed in {total_time:.3f}s")
            
            return response, reasoning_data
            
        except Exception as e:
            total_time = time.time() - total_start_time
            logger.error(f"Error processing query after {total_time:.3f}s: {e}")
            raise  # Re-raise the exception to break the code
    
    def _update_conversation_history(self, user_input: str, response: str):
        """Update the conversation history with the latest exchange."""
        if not self._conversation_history:
            self._conversation_history = f"User: {user_input}\nAgent: {response}"
        else:
            self._conversation_history += f"\nUser: {user_input}\nAgent: {response}"
    
    def get_framework_type(self) -> str:
        """Get the type of this framework."""
        return "langgraph"
    
    def get_framework_info(self) -> Dict[str, Any]:
        """Get information about this framework."""
        return {
            "name": "LangGraph Agent Orchestrator",
            "type": "langgraph",
            "description": "Graph-based agent orchestration using LangGraph",
            "agents_count": len(self.agents_registry.list_agents()) if self.agents_registry else 0,
            "tools_count": len(self.tools_registry.list_tools()) if self.tools_registry else 0,
            "initialised": self.initialised
        }
    
    def list_available_agents(self) -> List[str]:
        """List available agents in this framework."""
        if self.agents_registry:
            return self.agents_registry.list_agents()
        return []
    
    async def cleanup(self):
        """Clean up orchestrator resources and connections."""
        logger.info("Cleaning up agent orchestrator...")
        
        try:
            # Clean up common services
            await self._cleanup_common_services()
            
            # Clear references
            self.graph = None
            self.initialised = False
            
            logger.info("✅ Agent orchestrator cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during orchestrator cleanup: {e}")
    
    async def _cleanup_common_services(self):
        """Clean up common services."""
        try:
            if self.conversation_service:
                await self.conversation_service.close()
                logger.info("✅ Conversation service cleaned up")
            
            if self.search_engine:
                self.search_engine.close()
                logger.info("✅ Search engine cleaned up")
            
            if self.session_cache:
                await self.session_cache.close()
                logger.info("✅ Session cache service cleaned up")
            
            # Clear references
            self.llm_service = None
            self.conversation_service = None
            self.datetime_service = None
            self.session_cache = None
            self.search_engine = None
            self.agents_registry = None
            self.tools_registry = None
            
        except Exception as e:
            logger.error(f"Error during common services cleanup: {e}")
