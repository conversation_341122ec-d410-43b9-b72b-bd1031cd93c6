"""
Core business entities representing the fundamental concepts of the agent.
These are framework-agnostic and contain only business logic.
"""
from datetime import datetime
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional


@dataclass
class Product:
    """
    Represents a fashion product in the Shoeby catalogue.
    
    Core business entity that encapsulates all product-related data
    without any dependencies on external systems or frameworks.
    """
    title: str
    brand: str
    price: float
    sale_price: float
    colour: str
    size: str
    material: str
    category: str
    style: str = ""
    occasion: str = ""
    description: str = ""
    product_url: str = ""
    special_offer: str = ""
    product_id: str = ""
    
    def get_display_price(self) -> float:
        """Return the price to display to customers (sale price if available, otherwise regular price)."""
        return self.sale_price if self.sale_price > 0 else self.price
    
    def is_on_sale(self) -> bool:
        """Check if the product is currently on sale."""
        return self.sale_price > 0 and self.sale_price < self.price
    
    def get_discount_percentage(self) -> float:
        """Calculate the discount percentage if on sale."""
        if not self.is_on_sale():
            return 0.0
        return ((self.price - self.sale_price) / self.price) * 100
    
    def matches_colour_preference(self, preferred_colour: str) -> bool:
        """Check if product colour matches user's preference (case-insensitive)."""
        return self.colour.lower() == preferred_colour.lower()
    
    def has_special_offer(self) -> bool:
        """Check if the product has any special offers or discounts."""
        return bool(self.special_offer and self.special_offer.strip())
    
    def get_special_offer_info(self) -> str:
        """Get information about special offers or discounts."""
        return self.special_offer if self.has_special_offer() else ""


@dataclass
class BasketItem:
    """
    Represents a product item that has been added to a customer's shopping basket.
    
    Contains the product reference plus customer-specific selections like quantity.
    """
    name: str
    size: str = ""
    price: float = 0.0
    quantity: int = 1
    added_timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def get_total_price(self) -> float:
        """Calculate total price for this basket item including quantity."""
        return self.price * self.quantity
    
    def update_quantity(self, new_quantity: int):
        """Update the quantity of this basket item."""
        if new_quantity <= 0:
            raise ValueError("Quantity must be positive")
        self.quantity = new_quantity


@dataclass
class SearchQuery:
    """
    Represents a structured product search query with filters and parameters.
    
    Encapsulates all search criteria in a clean, immutable format.
    """
    search_terms: List[str] = field(default_factory=list)
    primary_filters: Dict[str, Any] = field(default_factory=dict)
    price_filters: Dict[str, Any] = field(default_factory=dict)
    search_type: str = "hybrid"
    result_limit: int = 10
    
    def has_search_terms(self) -> bool:
        """Check if the query contains any search terms."""
        return len(self.search_terms) > 0
    
    def has_filters(self) -> bool:
        """Check if the query contains any filters."""
        return len(self.primary_filters) > 0 or len(self.price_filters) > 0
    
    def get_max_price(self) -> Optional[float]:
        """Extract maximum price filter if present."""
        return self.price_filters.get("max_price")
    
    def get_min_price(self) -> Optional[float]:
        """Extract minimum price filter if present."""
        return self.price_filters.get("min_price")


@dataclass
class ConversationEntry:
    """
    Represents a single message in the conversation history.
    
    Simple data structure for storing and retrieving conversation messages.
    """
    role: str  # "user" or "assistant"
    content: str
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    
    def is_user_message(self) -> bool:
        """Check if this entry is from the user."""
        return self.role == "user"
    
    def is_assistant_message(self) -> bool:
        """Check if this entry is from the assistant."""
        return self.role == "assistant"


@dataclass
class SearchState:
    """Encapsulates the current search state including query parameters and results."""
    current_results: List[Product] = field(default_factory=list)
    current_query: SearchQuery = field(default_factory=SearchQuery)
    retry_count: int = 0
    
    def has_results(self) -> bool:
        """Check if search has produced any results."""
        return len(self.current_results) > 0
    
    def get_result_count(self) -> int:
        """Get the number of search results."""
        return len(self.current_results)
    