"""
Database and API operation tools.
These tools perform actual database queries, API calls, and data persistence operations.
"""
from typing import Dict, Any, List
import asyncio
from .base_tool import BaseTool
from core.entities import SearchQuery
import logging

logger = logging.getLogger(__name__)

class WeaviateSearchTool(BaseTool):
    """Tool for executing actual product searches against Weaviate database"""
    
    def __init__(self, search_engine, session_cache_service=None):
        super().__init__(
            name="weaviate_search",
            description="Execute product search against Weaviate database and return results"
        )
        self.search_engine = search_engine
        self.session_cache_service = session_cache_service
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Get search query from params
            search_query = params.get("search_query")
            context = params.get("context", {})
            
            return await self._execute_single_product_search(params)
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error executing product search"
            }
    
    
    async def _execute_single_product_search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute single product search"""
        try:
            search_query = params.get("search_query")
            context = params.get("context", {})
            
            if not search_query:
                return {
                    "success": False,
                    "error": "No search query provided",
                    "message": "Search query not found"
                }
            
            # Execute the search - this is the actual database call
            search_state = self.search_engine.search_products(search_query)
            
            if not search_state.has_results():
                return {
                    "success": True,
                    "results": [],
                    "message": "Search completed but no products found",
                    "search_state": search_state
                }
            
            # Enhance search results with available sizes information
            enhanced_results = []
            for product in search_state.current_results:
                available_sizes = getattr(product, 'available_sizes', [product.size] if product.size else [])
                enhanced_product = {
                    "title": product.title,
                    "brand": product.brand,
                    "price": product.sale_price,  # Use sale_price for consistency with search filtering
                    "colour": product.colour,
                    "size": product.size,
                    "category": product.category,
                    "url": product.product_url,
                    "available_sizes": available_sizes,
                    "has_multiple_sizes": len(available_sizes) > 1
                }
                enhanced_results.append(enhanced_product)
            
            # Cache the search results if service available
            if self.session_cache_service and search_state.has_results():
                try:
                    session_id = context.get("session_id", "default")
                    user_id = context.get("user_id", "default")
                    
                    await self.session_cache_service.cache_search_results(
                        session_id=session_id,
                        user_id=user_id,
                        search_terms=search_query.search_terms,
                        products=search_state.current_results,
                        search_type=search_query.search_type
                    )
                    
                    logger.info("Successfully cached search results")
                    
                except Exception as e:
                    logger.warning(f"Failed to cache search results: {e}")
            
            return {
                "success": True,
                "results": search_state.current_results,
                "enhanced_results": enhanced_results,
                "message": f"Search completed successfully. Found {len(search_state.current_results)} products.",
                "search_state": search_state
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error executing product search"
            }


class MongoDBCacheTool(BaseTool):
    """Tool for MongoDB caching operations"""
    
    def __init__(self, session_cache_service):
        super().__init__(
            name="mongodb_cache",
            description="Cache and retrieve data from MongoDB"
        )
        self.session_cache_service = session_cache_service
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        try:
            operation = params.get("operation")  # "cache" or "retrieve"
            
            if operation == "cache":
                return await self._cache_results(params)
            elif operation == "retrieve":
                return await self._retrieve_results(params)
            else:
                return {
                    "success": False,
                    "error": f"Unknown operation: {operation}",
                    "message": "Operation must be 'cache' or 'retrieve'"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error executing MongoDB operation"
            }
    
    async def _cache_results(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Cache search results to MongoDB"""
        try:
            session_id = params.get("session_id")
            user_id = params.get("user_id")
            search_terms = params.get("search_terms", [])
            products = params.get("products", [])
            search_type = params.get("search_type", "default")
            
            await self.session_cache_service.cache_search_results(
                session_id=session_id,
                user_id=user_id,
                search_terms=search_terms,
                products=products,
                search_type=search_type
            )
            
            return {
                "success": True,
                "message": f"Cached {len(products)} products for session {session_id}"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error caching results to MongoDB"
            }
    
    async def _retrieve_results(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Retrieve cached results from MongoDB"""
        try:
            session_id = params.get("session_id")
            user_id = params.get("user_id")
            
            cached_results = await self.session_cache_service.get_any_cached_results(
                session_id=session_id,
                user_id=user_id
            )
            
            if cached_results:
                # Convert CachedProduct objects to dict format
                search_results = []
                for cached_product in cached_results:
                    search_results.append({
                        "title": cached_product.title,
                        "brand": cached_product.brand,
                        "price": cached_product.price,
                        "colour": cached_product.colour,
                        "size": cached_product.size,
                        "category": cached_product.category,
                        "url": cached_product.product_url,
                        "available_sizes": cached_product.available_sizes
                    })
                
                return {
                    "success": True,
                    "results": search_results,
                    "message": f"Retrieved {len(search_results)} cached products"
                }
            else:
                return {
                    "success": True,
                    "results": [],
                    "message": "No cached results found"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error retrieving cached results from MongoDB"
            }


class BrainPowaLLMTool(BaseTool):
    """Tool for BrainPowa LLM API calls"""
    
    def __init__(self, llm_service):
        super().__init__(
            name="brainpowa_llm",
            description="Make calls to BrainPowa LLM service"
        )
        self.llm_service = llm_service
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        try:
            prompt = params.get("prompt")
            if not prompt:
                return {
                    "success": False,
                    "error": "No prompt provided",
                    "message": "Prompt is required for LLM call"
                }
            
            # This is the actual API call to BrainPowa
            response = await self.llm_service.generate_response(prompt)
            
            return {
                "success": True,
                "response": response,
                "message": "LLM response generated successfully"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error calling BrainPowa LLM service"
            }
