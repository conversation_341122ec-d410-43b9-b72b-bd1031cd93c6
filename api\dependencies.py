"""
Shared dependencies for the Shoeby Agent API.

Contains authentication, agent instance management, and other
common dependency functions used across multiple endpoints.
"""

from typing import Optional
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from main import <PERSON>hoebyAgent


# Security
security = HTTPBearer()

# Global agent instance (initialised in main app lifespan)
agent: Optional[ShoebyAgent] = None


async def verify_api_key(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """
    Simple API key authentication for team testing.
    In production, replace with proper JWT validation.
    """
    # For now, accept any non-empty credentials for team testing
    if not credentials.credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return credentials.credentials


async def get_agent() -> ShoebyAgent:
    """Get the initialised agent instance"""
    if agent is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Agent not initialised",
        )
    return agent


def set_agent(agent_instance: ShoebyAgent) -> None:
    """Set the global agent instance (called during app startup)"""
    global agent
    agent = agent_instance
