"""
Comprehensive Shoeby Agent Evaluation
Tests all tools/agents with diverse scenarios and fixes analysis issues
"""
import asyncio
import json
import time
import uuid
import random
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from openai import AsyncOpenAI
import logging
from datetime import datetime
from pathlib import Path

from config.settings import AgentConfig
from main import ShoebyAgent
from services.model_providers import ModelProvider

# Setup logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@dataclass
class ConversationTurn:
    """Represents a single turn in a conversation."""
    speaker: str  # 'customer' or 'agent'
    message: str
    timestamp: float

@dataclass
class TestConversation:
    """Represents a complete test conversation."""
    conversation_id: str
    turns: List[ConversationTurn]
    total_duration: float
    customer_intent: str
    model_provider: str
    tools_tested: List[str] = None

class ComprehensiveCustomerSimulator:
    """Simulates customer behavior with comprehensive tool testing scenarios."""
    
    def __init__(self, openai_api_key: str):
        self.client = AsyncOpenAI(api_key=openai_api_key)
        
        # Base components for generating diverse personas
        self.names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
        self.ages = ["early 20s", "mid 20s", "late 20s", "early 30s", "mid 30s", "late 30s", "early 40s", "mid 40s"]
        self.occupations = ["teacher", "nurse", "engineer", "artist", "student", "freelancer", "manager", "consultant", "designer", "developer"]
        self.styles = ["minimalist", "trendy", "classic", "bohemian", "sporty", "edgy", "elegant", "casual", "vintage", "modern"]
        self.budgets = ["tight budget", "moderate budget", "flexible budget", "high-end budget"]
        self.occasions = ["work", "casual outings", "special events", "travel", "gym/fitness", "date nights", "family gatherings", "everyday wear"]
        self.priorities = ["comfort", "style", "durability", "sustainability", "brand reputation", "uniqueness", "versatility", "affordability"]
        self.shopping_styles = ["quick decision maker", "careful researcher", "impulse buyer", "bargain hunter", "brand loyalist", "trend follower"]
        
        # Comprehensive test scenarios that trigger different tools/agents
        self.test_scenarios = [
            {
                "name": "product_search_scenario",
                "description": "Customer searches for products",
                "tools_to_test": ["search_products", "construct_weaviate_query", "evaluate_products"],
                "opening_intent": "I'm looking for {style} {item_type} for {occasion}"
            },
            {
                "name": "product_details_scenario", 
                "description": "Customer asks for specific product details",
                "tools_to_test": ["product_details", "present_products"],
                "opening_intent": "I want to know more about {specific_product}"
            },
            {
                "name": "follow_up_question_scenario",
                "description": "Agent asks follow-up questions",
                "tools_to_test": ["ask_follow_up_question"],
                "opening_intent": "I need help finding something"
            },
            {
                "name": "complementary_products_scenario",
                "description": "Agent suggests complementary products",
                "tools_to_test": ["suggest_complementary_products", "present_products"],
                "opening_intent": "I found this {item} I like"
            },
            {
                "name": "basket_management_scenario",
                "description": "Customer manages shopping basket",
                "tools_to_test": ["update_basket", "basket_tool"],
                "opening_intent": "I want to add this to my basket"
            }
        ]
    
    def generate_test_persona(self, conversation_num: int) -> Dict[str, str]:
        """Generate a persona for comprehensive testing."""
        random.seed(conversation_num * 42)
        
        name = random.choice(self.names)
        age = random.choice(self.ages)
        occupation = random.choice(self.occupations)
        style = random.choice(self.styles)
        budget = random.choice(self.budgets)
        occasion = random.choice(self.occasions)
        priority = random.choice(self.priorities)
        shopping_style = random.choice(self.shopping_styles)
        
        # Select a test scenario
        scenario = self.test_scenarios[conversation_num % len(self.test_scenarios)]
        
        # Generate specific intent based on scenario
        if scenario["name"] == "product_search_scenario":
            item_types = ["dresses", "shirts", "pants", "jackets", "shoes", "accessories"]
            item_type = random.choice(item_types)
            intent = f"Looking for {style} {item_type} for {occasion} with {budget}, prioritizing {priority}"
        elif scenario["name"] == "product_details_scenario":
            products = ["Nike Air Max", "Levi's 501", "Zara blazer", "H&M dress", "Adidas sneakers"]
            specific_product = random.choice(products)
            intent = f"Want detailed information about {specific_product} for {occasion}"
        elif scenario["name"] == "follow_up_question_scenario":
            intent = f"Need help finding {style} clothing for {occasion} but unsure about specifics"
        elif scenario["name"] == "complementary_products_scenario":
            intent = f"Found a {style} item and need complementary pieces for {occasion}"
        elif scenario["name"] == "basket_management_scenario":
            intent = f"Ready to purchase {style} items for {occasion} and manage shopping basket"
        else:
            intent = f"Looking for {style} clothing for {occasion} with {budget}, prioritizing {priority}"
        
        personality = f"{age} {occupation} who is a {shopping_style} and values {priority}"
        
        return {
            "name": name,
            "age": age,
            "occupation": occupation,
            "intent": intent,
            "personality": personality,
            "style_preference": style,
            "budget_level": budget,
            "primary_occasion": occasion,
            "shopping_priority": priority,
            "shopping_style": shopping_style,
            "test_scenario": scenario,
            "tools_to_test": scenario["tools_to_test"]
        }
    
    async def generate_customer_message(
        self, 
        persona: Dict[str, str], 
        conversation_history: List[ConversationTurn], 
        turn_number: int
    ) -> str:
        """Generate a customer message that dynamically responds to the agent's messages."""
        
        scenario = persona.get("test_scenario", {})
        
        # For first message, generate based on scenario opening intent
        if turn_number == 0:
            opening_intent = scenario.get("opening_intent", "I need help finding something")
            
            # Format the opening intent with persona details
            formatted_intent = opening_intent.format(
                style=persona.get('style_preference', 'casual'),
                item_type='clothing',
                occasion=persona.get('primary_occasion', 'everyday wear'),
                specific_product='this item',
                item='this piece'
            )
            
            opening_prompt = f"""Generate a natural opening message for a customer with this profile:

CUSTOMER PROFILE:
- Name: {persona['name']}
- Intent: {persona['intent']}
- Personality: {persona['personality']}
- Shopping style: {persona.get('shopping_style', 'typical shopper')}
- Test scenario: {scenario.get('description', 'general shopping')}

OPENING INTENT: {formatted_intent}
TOOLS TO TEST: {', '.join(persona.get('tools_to_test', []))}

Create a realistic first message that this person would send to a fashion retailer. 
The message should naturally lead to testing the specified tools.
Keep it under 30 words and natural.

Message:"""
            
            try:
                response = await self.client.chat.completions.create(
                    model="gpt-4o-mini",  # Use available model instead of o3-mini
                    messages=[
                        {"role": "system", "content": "Generate realistic customer opening messages for comprehensive e-commerce testing."},
                        {"role": "user", "content": opening_prompt}
                    ],
                    max_tokens=60,
                    temperature=0.8
                )
                
                # Debug logging (commented out to reduce noise)
                # logger.info(f"OpenAI Response: {response}")
                
                if not response.choices or not response.choices[0].message.content:
                    raise ValueError(f"Empty response from OpenAI. Response: {response}")
                
                opening_line = response.choices[0].message.content.strip()
                if opening_line.startswith('"') and opening_line.endswith('"'):
                    opening_line = opening_line[1:-1]
                
                if not opening_line or len(opening_line.strip()) < 3:
                    raise ValueError(f"LLM generated invalid opening message. Got: '{opening_line}'. Full response: {response}")
                    
            except Exception as e:
                logger.error(f"Error calling OpenAI API: {e}")
                raise ValueError(f"Failed to generate opening message: {e}")
            
            logger.info(f"Generated opening: {opening_line}")
            return opening_line
        
        # For follow-up messages, generate dynamic responses based on agent's actual messages
        recent_history = conversation_history[-4:] if len(conversation_history) > 4 else conversation_history
        history_text = ""
        for turn in recent_history:
            role = "You" if turn.speaker == "customer" else "Shoeby Agent" 
            history_text += f"{role}: {turn.message}\n"
        
        # Get the agent's last message to respond to
        agent_messages = [turn for turn in conversation_history if turn.speaker == "agent"]
        last_agent_message = agent_messages[-1].message if agent_messages else "Hello, how can I help you?"
        
        follow_up_prompt = f"""You are {persona['name']}, a customer shopping online. Generate a natural response to the agent's message.

CUSTOMER PROFILE:
- Name: {persona['name']}
- Goal: {persona['intent']}
- Personality: {persona['personality']}
- Shopping style: {persona.get('shopping_style', 'typical shopper')}

TOOLS TO TEST: {', '.join(persona.get('tools_to_test', []))}
SCENARIO: {scenario.get('description', 'general shopping')}

CONVERSATION HISTORY:
{history_text}

AGENT'S LAST MESSAGE: "{last_agent_message}"

INSTRUCTIONS:
1. Respond naturally as {persona['name']} would to the agent's message
2. Your response should help test the specified tools: {', '.join(persona.get('tools_to_test', []))}
3. Be conversational and realistic - don't just ask for tools directly
4. Keep response under 40 words
5. If the agent asked a question, answer it naturally
6. If the agent showed products, react to them appropriately
7. If the agent suggested something, respond as this customer would
8. IMPORTANT: After turn {turn_number}, consider ending the conversation naturally if you've gotten what you need or if the conversation feels complete. Use phrases like "Thanks, that's helpful!", "Perfect, I'll think about it", "Great, I'll check those out", etc.

Your response:"""

        try:
            response = await self.client.chat.completions.create(
                model="gpt-4o-mini",  # Use available model instead of o3-mini
                messages=[
                    {"role": "system", "content": "Generate realistic customer responses that dynamically react to agent messages for comprehensive e-commerce testing."},
                    {"role": "user", "content": follow_up_prompt}
                ],
                max_tokens=100,
                temperature=0.8
            )
            
            if not response.choices or not response.choices[0].message.content:
                raise ValueError(f"Empty response from OpenAI at turn {turn_number}. Response: {response}")
            
            customer_message = response.choices[0].message.content.strip()
            if customer_message.startswith('"') and customer_message.endswith('"'):
                customer_message = customer_message[1:-1]
            
            # Validate the response - fail if empty or invalid
            if not customer_message or len(customer_message.strip()) < 3:
                raise ValueError(f"LLM generated invalid customer message at turn {turn_number}. Got: '{customer_message}'. Full response: {response}")
                
        except Exception as e:
            logger.error(f"Error calling OpenAI API for follow-up message: {e}")
            raise ValueError(f"Failed to generate customer message at turn {turn_number}: {e}")
        
        logger.info(f"Generated dynamic customer message: '{customer_message}'")
        return customer_message

class Conversationanalyser:
    """analyses conversations and generates comprehensive AI summaries."""
    
    def __init__(self, openai_api_key: str):
        self.client = AsyncOpenAI(api_key=openai_api_key)
    
    async def analyse_conversations(
        self, 
        conversations: List[TestConversation], 
        personas: List[Dict[str, str]]
    ) -> Dict[str, Any]:
        """Generate comprehensive AI analysis from conversations."""
        
        # Build comprehensive conversation data
        conversation_summaries = []
        tools_tested = set()
        
        for conv, persona in zip(conversations, personas):
            summary = {
                "persona": f"{persona['name']} ({persona.get('age', 'unknown age')}, {persona.get('occupation', 'unknown occupation')})",
                "intent": persona['intent'],
                "scenario": persona.get('test_scenario', {}).get('name', 'unknown'),
                "tools_expected": persona.get('tools_to_test', []),
                "turns": len(conv.turns),
                "duration": f"{conv.total_duration:.2f}s",
                "model": conv.model_provider,
                "transcript": []
            }
            
            for turn in conv.turns:
                summary["transcript"].append({
                    "speaker": turn.speaker,
                    "message": turn.message
                })
            
            conversation_summaries.append(summary)
            tools_tested.update(persona.get('tools_to_test', []))
        
        # Generate comprehensive AI analysis
        analysis_prompt = f"""analyse these {len(conversations)} customer service conversations and provide insights.

CONVERSATION DATA:
{json.dumps(conversation_summaries, indent=2)}

TOOLS TESTED: {list(tools_tested)}

Provide analysis in this JSON format:
{{
    "overall_summary": "Brief overview of the conversations",
    "key_strengths": ["strength1", "strength2"],
    "key_weaknesses": ["weakness1", "weakness2"],
    "conversation_quality": "assessment of conversation quality",
    "customer_diversity": "assessment of customer handling",
    "tool_coverage": "assessment of tool usage",
    "response_patterns": "patterns in agent responses",
    "scenario_effectiveness": "effectiveness of test scenarios",
    "recommendations": ["recommendation1", "recommendation2"],
    "technical_issues": ["issue1", "issue2"],
    "performance_metrics": {{
        "avg_conversation_length": "average turns",
        "avg_response_time": "average response time",
        "tool_activation_rate": "tool usage percentage",
        "natural_ending_rate": "natural ending percentage"
    }},
    "detailed_conversation_analysis": [
        {{
            "conversation_id": "id",
            "persona_summary": "customer description",
            "overall_quality": "poor/fair/good/excellent",
            "conversation_flow": "how conversation progressed",
            "tool_usage_effectiveness": "tool usage assessment",
            "ending_quality": "how naturally it ended",
            "turn_by_turn_analysis": [
                {{
                    "turn": 1,
                    "speaker": "customer/agent",
                    "message_excerpt": "first 50 chars...",
                    "quality_rating": "poor/fair/good/excellent",
                    "issues": ["issue1", "issue2"],
                    "strengths": ["strength1", "strength2"],
                    "response_appropriateness": "response assessment",
                    "tool_usage": "tools used"
                }}
            ],
            "conversation_issues": ["major issue1", "major issue2"],
            "conversation_strengths": ["major strength1", "major strength2"],
            "improvement_suggestions": ["suggestion1", "suggestion2"]
        }}
    ]
}}

analyse each turn for quality issues and tool usage effectiveness."""

        response = await self.client.chat.completions.create(
            model="gpt-4o-mini",  # Use available model instead of o3-mini
            messages=[
                {"role": "system", "content": "You are analyzing customer service conversations. Provide insights in valid JSON format only. Do not use markdown code blocks."},
                {"role": "user", "content": analysis_prompt}
            ],
            max_tokens=2000,
            temperature=0.3
        )
        
        analysis_text = response.choices[0].message.content.strip()
        analysis_data = self._extract_json(analysis_text)
        
        # Add tool coverage analysis
        analysis_data["tools_tested"] = list(tools_tested)
        analysis_data["total_tools_available"] = len(tools_tested)
        
        return analysis_data
    
    def _extract_json(self, text: str) -> Dict[str, Any]:
        """Extract JSON from response text with improved parsing."""
        import re
        
        # First, try direct JSON parsing
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            pass
        
        # Try to extract from markdown code blocks with more flexible patterns
        json_patterns = [
            r'```json\s*(\{[\s\S]*?\})\s*```',  # ```json { ... } ```
            r'```\s*(\{[\s\S]*?\})\s*```',      # ``` { ... } ```
            r'```json\s*([\s\S]*?)\s*```',      # ```json ... ``` (without braces)
            r'```json\s*(\{[\s\S]*?)\s*$',      # ```json { ... (no closing ```)
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL | re.MULTILINE)
            for match in matches:
                try:
                    # Clean up the match
                    cleaned = match.strip()
                    if not cleaned.startswith('{'):
                        # If it doesn't start with {, look for the first {
                        start_idx = cleaned.find('{')
                        if start_idx != -1:
                            cleaned = cleaned[start_idx:]
                    
                    # Try to find the end of the JSON object
                    if not cleaned.endswith('}'):
                        # Count braces to find the end
                        brace_count = 0
                        end_idx = 0
                        for i, char in enumerate(cleaned):
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                if brace_count == 0:
                                    end_idx = i + 1
                                    break
                        if end_idx > 0:
                            cleaned = cleaned[:end_idx]
                    
                    return json.loads(cleaned)
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON decode error with pattern {pattern}: {e}")
                    continue
        
        # Try to find JSON without code blocks
        json_patterns_no_blocks = [
            r'\{[\s\S]*\}',  # Any JSON object
        ]
        
        for pattern in json_patterns_no_blocks:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
        
        # If all else fails, try to extract partial JSON
        try:
            # Look for the start of JSON
            start_idx = text.find('{')
            if start_idx != -1:
                # Try to find a reasonable end point
                brace_count = 0
                end_idx = start_idx
                for i, char in enumerate(text[start_idx:], start_idx):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i + 1
                            break
                
                if end_idx > start_idx:
                    partial_json = text[start_idx:end_idx]
                    return json.loads(partial_json)
        except Exception as e:
            logger.warning(f"Partial JSON extraction failed: {e}")
            pass
        
        # If JSON extraction completely fails, create a fallback response
        logger.error(f"Failed to extract JSON from response. Response length: {len(text)}")
        logger.error(f"First 1000 chars: {text[:1000]}")
        
        return {
            "overall_summary": "Analysis failed - JSON extraction error",
            "key_strengths": ["System completed conversations"],
            "key_weaknesses": ["AI analysis parsing failed"],
            "conversation_quality": "Unable to assess due to parsing error",
            "customer_diversity": "Unable to assess due to parsing error",
            "tool_coverage": "Unable to assess due to parsing error",
            "response_patterns": "Unable to assess due to parsing error",
            "scenario_effectiveness": "Unable to assess due to parsing error",
            "recommendations": ["Fix JSON parsing system", "Review AI response format"],
            "technical_issues": ["JSON extraction completely failed"],
            "performance_metrics": {
                "avg_conversation_length": "Unknown",
                "avg_response_time": "Unknown",
                "tool_activation_rate": "Unknown",
                "natural_ending_rate": "Unknown"
            },
            "detailed_conversation_analysis": []
        }

class ComprehensiveShoebyTester:
    """Main testing orchestrator for comprehensive Shoeby agent evaluation."""
    
    def __init__(self, agent_config: AgentConfig, openai_api_key: str):
        self.agent_config = agent_config
        self.customer_simulator = ComprehensiveCustomerSimulator(openai_api_key)
        self.analyser = Conversationanalyser(openai_api_key)
        
        # Track results and personas
        self.conversations: List[TestConversation] = []
        self.personas: List[Dict[str, str]] = []
        
        # Progressive saving setup
        self.session_dir: Optional[Path] = None
        self.results_file: Optional[Path] = None
        self.start_time: datetime = None
    
    async def run_comprehensive_evaluation(self, num_conversations: int = 6) -> Dict[str, Any]:
        """Run comprehensive evaluation testing all tools/agents."""
        
        print("🔬 Starting Comprehensive Shoeby Agent Evaluation")
        print("Testing all tools and agents with diverse scenarios")
        print("=" * 70)
        
        # Setup progressive saving
        await self._setup_progressive_saving()
        
        # Update initial file with planned conversation count
        await self._update_planned_conversations(num_conversations)
        
        # Run conversations with progressive saving
        await self._run_comprehensive_conversations(num_conversations)
        
        # Generate final AI analysis
        print("\n🤖 Generating comprehensive AI analysis...")
        analysis = await self.analyser.analyse_conversations(self.conversations, self.personas)
        
        # Create final results structure
        results = await self._create_final_results(analysis)
        
        # Save final results with analysis
        await self._save_final_results(results)
        
        # Generate comparison report
        await self._generate_comparison_report(self.session_dir, results)
        
        # Display summary
        self._display_summary(results)
        
        return results
    
    async def _run_comprehensive_conversations(self, num_conversations: int):
        """Run conversations designed to test all tools/agents."""
        
        for i in range(num_conversations):
            agent = None
            try:
                print(f"\n--- Conversation {i + 1} ---")
                
                # Generate test persona with specific scenario
                persona = self.customer_simulator.generate_test_persona(i)
                scenario = persona.get('test_scenario', {})
                
                print(f"👤 Customer: {persona['name']} ({persona['personality']})")
                print(f"🎯 Intent: {persona['intent']}")
                print(f"🧪 Scenario: {scenario.get('description', 'General shopping')}")
                print(f"🔧 Tools to test: {', '.join(persona.get('tools_to_test', []))}")
                
                # Create fresh agent
                agent = ShoebyAgent(self.agent_config)
                setup_success = await agent.setup()
                
                if not setup_success:
                    raise RuntimeError(f"Failed to setup agent for conversation {i + 1}")
                
                # Ensure OpenAI provider is selected
                success = agent.framework.llm_service.switch_provider(ModelProvider.OPENAI)
                if not success:
                    raise RuntimeError(f"Failed to switch to OpenAI provider")
                
                # Run conversation
                conversation = await self._run_single_conversation(agent, persona, i)
                
                # Store results
                self.conversations.append(conversation)
                self.personas.append(persona)
                
                # Display immediate results
                print(f"✅ Completed: {len(conversation.turns)} turns, {conversation.total_duration:.2f}s")
                
                # Save progress after each conversation
                await self._save_progress(i + 1, num_conversations)
                
            except Exception as e:
                logger.error(f"Error in conversation {i + 1}: {e}")
                # Save progress even on error to preserve completed conversations
                await self._save_progress(i + 1, num_conversations, error=str(e))
                raise
            finally:
                # Always cleanup agent resources
                if agent:
                    await agent.cleanup()
                await asyncio.sleep(1)
    
    async def _run_single_conversation(
        self, 
        agent: ShoebyAgent, 
        persona: Dict[str, str], 
        conversation_num: int
    ) -> TestConversation:
        """Run a single conversation with comprehensive tool testing."""
        
        conversation = TestConversation(
            conversation_id=str(uuid.uuid4()),
            turns=[],
            total_duration=0,
            customer_intent=persona['intent'],
            model_provider="OPENAI",
            tools_tested=persona.get('tools_to_test', [])
        )
        
        start_time = time.perf_counter()
        
        # Run conversation turns (max 10 turns = 20 messages for comprehensive testing)
        for turn_num in range(10):
            # Customer's turn - generate dynamic response based on conversation history
            customer_message = await self.customer_simulator.generate_customer_message(
                persona, conversation.turns, turn_num
            )
            
            conversation.turns.append(ConversationTurn(
                speaker="customer",
                message=customer_message,
                timestamp=time.perf_counter() - start_time
            ))
            
            print(f"👤 Customer: {customer_message}")
            
            # Check for natural end signals
            end_signals = [
                "thanks, i'll think about it", "need to go", "bye", "that's all", "i'm done", 
                "perfect", "that works", "i'm good", "thanks, that's helpful", "great, i'll check",
                "perfect, i'll think", "sounds good, thanks", "that's exactly what i needed",
                "awesome, thanks", "brilliant, thank you", "that's great, thanks", "wonderful, thanks",
                "i'll look into", "i'll consider", "i'll browse", "i'll have a look"
            ]
            if any(signal in customer_message.lower() for signal in end_signals):
                print(f"🛑 Natural conversation end detected: '{customer_message}'")
                break
            
            # Agent's turn
            agent_response, _ = await agent.process_query_with_reasoning(customer_message)
            
            conversation.turns.append(ConversationTurn(
                speaker="agent", 
                message=agent_response,
                timestamp=time.perf_counter() - start_time
            ))
            
            # Truncate for display
            display_response = agent_response[:150] + "..." if len(agent_response) > 150 else agent_response
            print(f"🤖 AGENT: {display_response}")
            
            # Check if agent is ending the conversation
            agent_end_signals = ["is there anything else", "anything else i can help", "let me know if you need", "feel free to ask"]
            if any(signal in agent_response.lower() for signal in agent_end_signals):
                print(f"🛑 Agent suggesting conversation end")
                break
        
        conversation.total_duration = time.perf_counter() - start_time
        return conversation
    
    async def _setup_progressive_saving(self):
        """Setup directories and files for progressive saving."""
        # Create evals directory structure
        evals_dir = Path("evals")
        evals_dir.mkdir(exist_ok=True)
        
        # Create datetime subfolder
        self.start_time = datetime.now()
        timestamp = self.start_time.strftime("%Y%m%d_%H%M%S")
        self.session_dir = evals_dir / timestamp
        self.session_dir.mkdir(exist_ok=True)
        
        # Setup results file
        self.results_file = self.session_dir / "comprehensive_evaluation_results.json"
        
        # Create initial results structure
        initial_results = {
            "timestamp": self.start_time.isoformat(),
            "session_id": timestamp,
            "status": "in_progress",
            "total_conversations_planned": 0,
            "completed_conversations": 0,
            "evaluation_type": "comprehensive_tool_testing",
            "raw_conversations": [],
            "progress_log": []
        }
        
        # Save initial file
        with open(self.results_file, 'w') as f:
            json.dump(initial_results, f, indent=2)
        
        print(f"📁 Progressive results will be saved to: {self.results_file}")
    
    async def _update_planned_conversations(self, total: int):
        """Update the planned conversation count in the results file."""
        try:
            with open(self.results_file, 'r') as f:
                results = json.load(f)
            
            results["total_conversations_planned"] = total
            
            with open(self.results_file, 'w') as f:
                json.dump(results, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Failed to update planned conversations: {e}")
    
    async def _save_progress(self, completed: int, total: int, error: str = None):
        """Save current progress after each conversation."""
        try:
            # Read current results
            with open(self.results_file, 'r') as f:
                results = json.load(f)
            
            # Update progress info
            results["completed_conversations"] = completed
            results["total_conversations_planned"] = total
            results["last_updated"] = datetime.now().isoformat()
            
            if error:
                results["status"] = "error"
                results["error"] = error
            elif completed >= total:
                results["status"] = "conversations_complete"
            else:
                results["status"] = "in_progress"
            
            # Add progress log entry
            log_entry = {
                "conversation": completed,
                "timestamp": datetime.now().isoformat(),
                "status": "completed" if not error else "error"
            }
            if error:
                log_entry["error"] = error
            
            results["progress_log"].append(log_entry)
            
            # Update raw conversations with current data
            results["raw_conversations"] = []
            for conv, persona in zip(self.conversations, self.personas):
                conv_data = {
                    "conversation_id": conv.conversation_id,
                    "persona": persona,
                    "scenario": persona.get('test_scenario', {}).get('name', 'unknown'),
                    "tools_expected": persona.get('tools_to_test', []),
                    "duration": conv.total_duration,
                    "turns": []
                }
                
                for turn in conv.turns:
                    conv_data["turns"].append({
                        "speaker": turn.speaker,
                        "message": turn.message,
                        "timestamp": turn.timestamp
                    })
                
                results["raw_conversations"].append(conv_data)
            
            # Save updated results
            with open(self.results_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"💾 Progress saved: {completed}/{total} conversations completed")
            
        except Exception as e:
            logger.warning(f"Failed to save progress: {e}")
    
    async def _create_final_results(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create final results structure with AI analysis."""
        results = {
            "timestamp": self.start_time.isoformat(),
            "session_id": self.session_dir.name,
            "status": "completed",
            "total_conversations": len(self.conversations),
            "evaluation_type": "comprehensive_tool_testing",
            "ai_analysis": analysis,
            "raw_conversations": []
        }
        
        # Add raw conversation data
        for conv, persona in zip(self.conversations, self.personas):
            conv_data = {
                "conversation_id": conv.conversation_id,
                "persona": persona,
                "scenario": persona.get('test_scenario', {}).get('name', 'unknown'),
                "tools_expected": persona.get('tools_to_test', []),
                "duration": conv.total_duration,
                "turns": []
            }
            
            for turn in conv.turns:
                conv_data["turns"].append({
                    "speaker": turn.speaker,
                    "message": turn.message,
                    "timestamp": turn.timestamp
                })
            
            results["raw_conversations"].append(conv_data)
        
        return results
    
    async def _save_final_results(self, results: Dict[str, Any]):
        """Save final results with AI analysis."""
        with open(self.results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📄 Final results saved to: {self.results_file}")
    
    async def _save_results(self, results: Dict[str, Any]):
        """Save results to evals folder with datetime subfolder and comparison."""
        
        # Create evals directory structure
        evals_dir = Path("evals")
        evals_dir.mkdir(exist_ok=True)
        
        # Create datetime subfolder
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_dir = evals_dir / timestamp
        session_dir.mkdir(exist_ok=True)
        
        # Save main results
        results_file = session_dir / "comprehensive_evaluation_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📄 Results saved to: {results_file}")
        
        # Generate comparison with previous results
        await self._generate_comparison_report(session_dir, results)
    
    async def _generate_comparison_report(self, current_dir: Path, current_results: Dict[str, Any]):
        """Generate comparison report with previous results."""
        
        evals_dir = Path("evals")
        if not evals_dir.exists():
            return
        
        # Find previous results (most recent before current)
        all_sessions = sorted([d for d in evals_dir.iterdir() if d.is_dir() and d.name != current_dir.name])
        
        if not all_sessions:
            print("📊 No previous results found - this is the first comprehensive evaluation")
            return
        
        # Get most recent previous session
        previous_dir = all_sessions[-1]
        previous_results_file = previous_dir / "comprehensive_evaluation_results.json"
        
        if not previous_results_file.exists():
            # Try regular evaluation results
            previous_results_file = previous_dir / "evaluation_results.json"
            if not previous_results_file.exists():
                print("⚠️  Previous results file not found")
                return
        
        try:
            with open(previous_results_file, 'r') as f:
                previous_results = json.load(f)
            
            # Generate AI comparison
            comparison_prompt = f"""Compare these two Shoeby agent evaluation sessions and provide insights on improvement/regression.

PREVIOUS SESSION ({previous_dir.name}):
- Conversations: {previous_results.get('total_conversations', 0)}
- Evaluation Type: {previous_results.get('evaluation_type', 'unknown')}
- AI Analysis: {json.dumps(previous_results.get('ai_analysis', {}), indent=2)}

CURRENT SESSION ({current_dir.name}):
- Conversations: {current_results.get('total_conversations', 0)}
- Evaluation Type: {current_results.get('evaluation_type', 'unknown')}
- AI Analysis: {json.dumps(current_results.get('ai_analysis', {}), indent=2)}

Provide comparison in JSON format:
{{
    "overall_change": "improved/declined/similar",
    "key_improvements": ["improvement1", "improvement2"],
    "key_regressions": ["regression1", "regression2"],
    "conversation_quality_change": "assessment of quality changes",
    "tool_coverage_change": "assessment of tool testing improvements",
    "recommendations": ["recommendation1", "recommendation2"],
    "summary": "brief summary of changes between sessions"
}}"""

            response = await self.analyser.client.chat.completions.create(
                model="gpt-4o-mini",  # Use available model instead of o3-mini
                messages=[
                    {"role": "system", "content": "Compare two evaluation sessions and identify improvements/regressions."},
                    {"role": "user", "content": comparison_prompt}
                ],
                max_tokens=800,
                temperature=0.3
            )
            
            comparison_text = response.choices[0].message.content.strip()
            comparison_data = self.analyser._extract_json(comparison_text)
            
            # Save comparison report
            comparison_file = current_dir / "comparison_report.json"
            comparison_report = {
                "current_session": current_dir.name,
                "previous_session": previous_dir.name,
                "comparison": comparison_data
            }
            
            with open(comparison_file, 'w') as f:
                json.dump(comparison_report, f, indent=2)
            
            print(f"📈 Comparison report saved to: {comparison_file}")
            
            # Display comparison summary
            print(f"\n📊 COMPARISON WITH PREVIOUS SESSION ({previous_dir.name}):")
            print(f"Overall Change: {comparison_data.get('overall_change', 'unknown')}")
            if comparison_data.get('key_improvements'):
                print("✅ Key Improvements:")
                for improvement in comparison_data.get('key_improvements', []):
                    print(f"  • {improvement}")
            if comparison_data.get('key_regressions'):
                print("⚠️  Key Regressions:")
                for regression in comparison_data.get('key_regressions', []):
                    print(f"  • {regression}")
            
        except Exception as e:
            logger.error(f"Error generating comparison report: {e}")
            print("⚠️  Could not generate comparison report")
    
    def _display_summary(self, results: Dict[str, Any]):
        """Display comprehensive evaluation summary with AI insights."""
        
        analysis = results.get('ai_analysis', {})
        
        print(f"\n" + "="*80)
        print("🎯 COMPREHENSIVE SHOEBY AGENT EVALUATION SUMMARY")
        print("="*80)
        
        print(f"\n📊 OVERVIEW:")
        print(f"   Total Conversations: {results['total_conversations']}")
        print(f"   Evaluation Type: {results.get('evaluation_type', 'comprehensive')}")
        print(f"   Timestamp: {results['timestamp']}")
        
        print(f"\n🤖 AI ANALYSIS:")
        print(f"   {analysis.get('overall_summary', 'No summary available')}")
        
        if analysis.get('key_strengths'):
            print(f"\n✅ KEY STRENGTHS:")
            for strength in analysis.get('key_strengths', []):
                print(f"   • {strength}")
        
        if analysis.get('key_weaknesses'):
            print(f"\n⚠️  KEY WEAKNESSES:")
            for weakness in analysis.get('key_weaknesses', []):
                print(f"   • {weakness}")
        
        print(f"\n🔍 CONVERSATION QUALITY:")
        print(f"   {analysis.get('conversation_quality', 'No assessment available')}")
        
        print(f"\n👥 CUSTOMER DIVERSITY:")
        print(f"   {analysis.get('customer_diversity', 'No assessment available')}")
        
        print(f"\n🔧 TOOL COVERAGE:")
        print(f"   {analysis.get('tool_coverage', 'No assessment available')}")
        if analysis.get('tools_tested'):
            print(f"   Tools Tested: {', '.join(analysis.get('tools_tested', []))}")
        
        print(f"\n🎭 SCENARIO EFFECTIVENESS:")
        print(f"   {analysis.get('scenario_effectiveness', 'No assessment available')}")
        
        if analysis.get('performance_metrics'):
            metrics = analysis.get('performance_metrics', {})
            print(f"\n📈 PERFORMANCE METRICS:")
            for metric, value in metrics.items():
                print(f"   {metric.replace('_', ' ').title()}: {value}")
        
        if analysis.get('recommendations'):
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in analysis.get('recommendations', []):
                print(f"   • {rec}")
        
        if analysis.get('technical_issues'):
            print(f"\n⚠️  TECHNICAL ISSUES:")
            for issue in analysis.get('technical_issues', []):
                print(f"   • {issue}")
        
        # Display detailed conversation analysis
        if analysis.get('detailed_conversation_analysis'):
            print(f"\n🔍 DETAILED CONVERSATION ANALYSIS:")
            for i, conv_analysis in enumerate(analysis.get('detailed_conversation_analysis', []), 1):
                print(f"\n--- Conversation {i} ---")
                print(f"   Persona: {conv_analysis.get('persona_summary', 'Unknown')}")
                print(f"   Overall Quality: {conv_analysis.get('overall_quality', 'Unknown')}")
                print(f"   Ending Quality: {conv_analysis.get('ending_quality', 'Unknown')}")
                
                # Show major issues if any
                if conv_analysis.get('conversation_issues'):
                    print(f"   ⚠️  Issues: {', '.join(conv_analysis.get('conversation_issues', []))}")
                
                # Show turn-by-turn issues (only poor/fair quality turns)
                turn_analysis = conv_analysis.get('turn_by_turn_analysis', [])
                poor_turns = [t for t in turn_analysis if t.get('quality_rating') in ['poor', 'fair']]
                if poor_turns:
                    print(f"   🔴 Problematic Turns:")
                    for turn in poor_turns[:3]:  # Show max 3 poor turns per conversation
                        speaker = turn.get('speaker', 'unknown')
                        turn_num = turn.get('turn', '?')
                        rating = turn.get('quality_rating', 'unknown')
                        excerpt = turn.get('message_excerpt', 'No excerpt')
                        issues = turn.get('issues', [])
                        print(f"      Turn {turn_num} ({speaker}, {rating}): \"{excerpt}\"")
                        if issues:
                            print(f"        Issues: {', '.join(issues)}")
        
        print("\n" + "="*80)
    
    async def _cleanup_resources(self):
        """Clean up any remaining resources to prevent ResourceWarnings."""
        try:
            # Note: Individual agent cleanup is handled in _run_comprehensive_conversations
            # This is for any additional cleanup that might be needed
            logger.info("🧹 Cleaning up evaluation resources...")
            
            # Close AsyncOpenAI clients
            if hasattr(self.customer_simulator.client, 'close'):
                await self.customer_simulator.client.close()
            if hasattr(self.analyser.client, 'close'):
                await self.analyser.client.close()
                
        except Exception as e:
            logger.warning(f"Warning during cleanup: {e}")

async def main():
    """
    Main comprehensive evaluation function for Shoeby agent.
    
    Runs conversations with diverse scenarios designed to test all tools/agents
    and provides comprehensive AI-powered analysis.
    """
    
    # Configuration
    import os
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    if not OPENAI_API_KEY:
        print("❌ OPENAI_API_KEY environment variable not set")
        print("💡 Please set OPENAI_API_KEY in your .env file")
        return
    
    print("🔬 Comprehensive Shoeby Agent Evaluation")
    print("Testing all tools and agents with diverse scenarios")
    print("="*70)
    
    tester = None
    try:
        # Load agent configuration
        agent_config = AgentConfig()
        
        # Create comprehensive tester
        tester = ComprehensiveShoebyTester(agent_config, OPENAI_API_KEY)
        
        # Run comprehensive evaluation (6 conversations testing all scenarios)
        results = await tester.run_comprehensive_evaluation(num_conversations=6)
        
        print("\n🎉 Comprehensive evaluation complete!")
        print(f"analysed {results['total_conversations']} conversations with comprehensive tool testing")
        
    except Exception as e:
        logger.error(f"❌ Comprehensive evaluation failed: {e}")
        print(f"❌ Testing failed: {e}")
        raise
    finally:
        # Cleanup any remaining resources
        if tester:
            await tester._cleanup_resources()

if __name__ == "__main__":
    asyncio.run(main())
