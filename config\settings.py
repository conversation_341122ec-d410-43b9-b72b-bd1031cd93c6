"""
Configuration settings and dataclasses for the agent.
Contains all configuration parameters.
"""
import os
import weaviate
from typing import Optional, Union
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()


@dataclass
class AgentConfig:
    """Configuration settings for the Shoeby Agent 
    including database connections and API keys."""
    environment = "local"
    # Weaviate configuration
    weaviate_host: str = os.getenv('WEAVIATE_HOST', "localhost")
    weaviate_port: int = int(os.getenv('WEAVIATE_PORT', "8080"))
    weaviate_grpc_port: int = int(os.getenv('WEAVIATE_GRPC_PORT', "50051"))
    weaviate_api_key: str = os.getenv('WEAVIATE_API_KEY', "")
    collection_name: str = os.getenv('WEAVIATE_COLLECTION', "ShoebyAgenticStoreProducts")
    custom_llm_model_name: str = os.getenv(
        "CUSTOM_LLM_MODEL_NAME",
        "brainpowa-core-model",
    )
    # Provider configuration 
    default_model_provider: str = os.getenv('DEFAULT_MODEL_PROVIDER', "openai")
    
    # Framework is now fixed to LangGraph - no configuration needed
    
    # BrainPowa configuration (uses OpenAI-compatible API)
    brainpowa_api_key: str = os.getenv('BRAINPOWA_API_KEY', "")
    brainpowa_base_url: str = os.getenv('BRAINPOWA_API_URL', "https://brainpowa-model-garden.dev.az.rezolve.com/v1")
    
    # OpenAI API key (used by BrainPowa provider)
    openai_api_key: str = os.getenv('OPENAI_API_KEY', "")
    
    # Search configuration
    max_search_retries: int = 3
    default_result_limit: int = 10
    
    # Visenze configuration
    visenze_app_key: str = os.getenv('VISENZE_APP_KEY', "")
    visenze_placement_id: str = os.getenv('VISENZE_PLACEMENT_ID', "")
    visenze_placement_id_search: str = os.getenv('VISENZE_PLACEMENT_ID_SEARCH', "")
    visenze_placement_id_recs: str = os.getenv('VISENZE_PLACEMENT_ID_RECS', "")
    
    # MongoDB configuration
    mongodb_url: str = os.getenv('MONGODB_URL', "**************************************************************")
    mongodb_database: str = os.getenv('MONGODB_DATABASE', "shoeby_conversations")
    mongodb_collection: str = os.getenv('MONGODB_COLLECTION', "chat_history")    
    # Observability provider name
    observability_provider_name: str = os.getenv(
        "OBSERVABILITY_PROVIDER_NAME",
        "null",
    )
    otel_base_url: str = os.getenv(
        "OTEL_EXPORTER_OTLP_ENDPOINT",
        "http://localhost:4318",
    )
    otel_service_name: str = os.getenv(
        "OTEL_SERVICE_NAME",
        "my-brain-api",
    )
    otel_mongo_tracing: Union[str, None] = os.getenv(
        "OTEL_mongo_TRACING",
        None,
    )
    otel_weaviate_tracing: Union[str, None] = os.getenv(
        "OTEL_WEAVIATE_TRACING",
        None,
    )
    otel_trace_tags: str = os.getenv(
        "OTEL_TRACE_TAGS",
        "merchant",
    )
    # Limit for string attributes added to spans
    trace_str_limits: int = int(os.getenv("OTEL_TRACE_STRING_LIMIT", "1000"))

    # Session management
    user_id: str = os.getenv('USER_ID', "demo_user")
    session_id: Optional[str] = None  # Will auto-generate a UUID if None
    
    # Display configuration
    show_json: bool = False  # Disable heavy JSON debug output by default
    show_reasoning: bool = False  # Show strategy reasoning to users
    # Provider behaviour
    force_brainpowa: bool = False  # If True, API will force BrainPowa for chat requests
    
    # Timezone configuration
    timezone_name: str = os.getenv('TIMEZONE', "UTC")  # Timezone for datetime context

    # API server configuration
    api_host: str = os.getenv("API_HOST", "0.0.0.0")
    api_port: int = int(os.getenv("API_PORT", "8877"))
    api_log_level: str = os.getenv("API_LOG_LEVEL", "info")
    # Performance and limits
    llm_timeout_seconds: int = int(os.getenv('LLM_TIMEOUT_SECONDS', "20"))  # Per-call LLM timeout
    presentation_results_limit: int = int(os.getenv('PRESENTATION_RESULTS_LIMIT', "6"))  # Cap items sent to LLM
    tool_timeout_seconds: int = int(os.getenv('TOOL_TIMEOUT_SECONDS', "25"))  # Per-tool execution timeout
    llm_max_tokens_default: int = int(os.getenv('LLM_MAX_TOKENS', "1000"))  # Default token cap per response

    def __post_init__(self):
        """Set up provider defaults after initialisation."""
        # Use OpenAI key for BrainPowa if no specific key provided
        if not self.brainpowa_api_key:
            self.brainpowa_api_key = self.openai_api_key
        
        # Framework is now fixed to LangGraph - no environment variable needed
    
    def get_weaviate_connection_params(self) -> dict:
        """Get Weaviate connection parameters as a dictionary."""
        params = {
            "http_host": self.weaviate_host,
            "http_port": self.weaviate_port,
            "http_secure": False,
            "grpc_host": self.weaviate_host,
            "grpc_port": self.weaviate_grpc_port,
            "grpc_secure": False,
        }
        
        # Add authentication if API key is provided
        if self.weaviate_api_key:
            params["headers"] = {"X-OpenAI-Api-Key": self.weaviate_api_key}
            params["auth_credentials"] = weaviate.auth.AuthApiKey(self.weaviate_api_key)
        
        return params

    def validate_configuration(self) -> tuple[bool, str]:
        """
        Validate that all required configuration is present.

        Returns:
            Tuple of (is_valid, error_message)
        """
        if not self.mongodb_url:
            return False, "MongoDB URL is required"

        if not self.collection_name:
            return False, "Weaviate collection name is required"
        
        # Check for required API keys
        if not self.brainpowa_api_key and not self.openai_api_key:
            return False, "Either BRAINPOWA_API_KEY or OPENAI_API_KEY environment variable is required"
        
        # Check for Visenze configuration
        if not self.visenze_app_key:
            return False, "VISENZE_APP_KEY environment variable is required"
        
        if not self.visenze_placement_id and not self.visenze_placement_id_search:
            return False, "Either VISENZE_PLACEMENT_ID or VISENZE_PLACEMENT_ID_SEARCH environment variable is required"
        
        return True, "Configuration is valid"
