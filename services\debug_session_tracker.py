"""
Debug session tracker for the <PERSON><PERSON><PERSON> agent.
Tracks all prompts, agents, tools, and their responses in a JSON file per session.
"""
import json
import logging
import os
from datetime import datetime, timezone
from typing import Dict, Any, List
from pathlib import Path

logger = logging.getLogger(__name__)

class DebugSessionTracker:
    """Tracks debug information for each session in a JSON file."""
    
    def __init__(self, session_id: str, user_id: str = "default"):
        self.session_id = session_id
        self.user_id = user_id
        self.debug_dir = Path("debug_sessions")
        self.debug_dir.mkdir(exist_ok=True)
        self.debug_file = self.debug_dir / f"session_{session_id}_{user_id}.json"
        
        # initialise session data
        self.session_data = {
            "session_id": session_id,
            "user_id": user_id,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "turns": []
        }
        
        # Load existing data if file exists
        if self.debug_file.exists():
            try:
                with open(self.debug_file, 'r', encoding='utf-8') as f:
                    self.session_data = json.load(f)
            except Exception as e:
                logger.warning(f"Could not load existing debug file: {e}")
    
    def start_turn(self, user_query: str) -> int:
        """Start a new turn and return the turn index."""
        turn_data = {
            "turn_number": len(self.session_data["turns"]) + 1,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_query": user_query,
            "context_extraction": {},
            "strategy_decision": {},
            "agents_used": [],
            "tools_executed": [],
            "prompts": [],
            "responses": [],
            "final_response": "",
            "errors": []
        }
        
        self.session_data["turns"].append(turn_data)
        self._save()
        return len(self.session_data["turns"]) - 1
    
    def log_context_extraction(self, turn_index: int, prompt: str, response: str):
        """Log context extraction prompt and response."""
        if turn_index < len(self.session_data["turns"]):
            self.session_data["turns"][turn_index]["context_extraction"] = {
                "prompt": prompt,
                "response": response,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self._save()
    
    def log_strategy_decision(self, turn_index: int, prompt: str, decision: Dict[str, Any]):
        """Log strategy decision prompt and response."""
        if turn_index < len(self.session_data["turns"]):
            self.session_data["turns"][turn_index]["strategy_decision"] = {
                "prompt": prompt,
                "decision": decision,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self._save()
    
    def log_agent_execution(self, turn_index: int, agent_name: str, params: Dict[str, Any], result: Any):
        """Log agent execution."""
        if turn_index < len(self.session_data["turns"]):
            agent_data = {
                "agent_name": agent_name,
                "params": params,
                "result": result,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.session_data["turns"][turn_index]["agents_used"].append(agent_data)
            self._save()
    
    def log_tool_execution(self, turn_index: int, tool_name: str, params: Dict[str, Any], result: Any):
        """Log tool execution."""
        if turn_index < len(self.session_data["turns"]):
            tool_data = {
                "tool_name": tool_name,
                "params": params,
                "result": result,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.session_data["turns"][turn_index]["tools_executed"].append(tool_data)
            self._save()
    
    def log_prompt(self, turn_index: int, prompt_type: str, prompt: str, response: str = ""):
        """Log any prompt and its response."""
        if turn_index < len(self.session_data["turns"]):
            prompt_data = {
                "type": prompt_type,
                "prompt": prompt,
                "response": response,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.session_data["turns"][turn_index]["prompts"].append(prompt_data)
            self._save()
    
    def log_response(self, turn_index: int, response_type: str, response: str):
        """Log any response."""
        if turn_index < len(self.session_data["turns"]):
            response_data = {
                "type": response_type,
                "response": response,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.session_data["turns"][turn_index]["responses"].append(response_data)
            self._save()
    
    def log_final_response(self, turn_index: int, response: str):
        """Log the final response to the user."""
        if turn_index < len(self.session_data["turns"]):
            self.session_data["turns"][turn_index]["final_response"] = response
            self._save()
    
    def log_error(self, turn_index: int, error: str, context: str = ""):
        """Log an error."""
        if turn_index < len(self.session_data["turns"]):
            error_data = {
                "error": error,
                "context": context,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.session_data["turns"][turn_index]["errors"].append(error_data)
            self._save()
    
    def log_llm_interaction(self, turn_index: int, interaction_type: str, prompt: str, response: str, metadata: Dict[str, Any] = None):
        """Log a complete LLM interaction with full prompt and response."""
        if turn_index < len(self.session_data["turns"]):
            interaction_data = {
                "type": interaction_type,
                "prompt": prompt,
                "response": response,
                "metadata": metadata or {},
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Add to prompts list for this turn
            self.session_data["turns"][turn_index]["prompts"].append(interaction_data)
            self._save()
    
    def _save(self):
        """Save the session data to file."""
        try:
            # Update last_updated timestamp
            self.session_data["last_updated"] = datetime.now(timezone.utc).isoformat()
            
            with open(self.debug_file, 'w', encoding='utf-8') as f:
                json.dump(self.session_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Could not save debug file: {e}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get a summary of the session."""
        return {
            "session_id": self.session_id,
            "user_id": self.user_id,
            "total_turns": len(self.session_data["turns"]),
            "created_at": self.session_data["created_at"],
            "debug_file": str(self.debug_file)
        }

# Global tracker instance
_debug_tracker = None

def get_debug_tracker(session_id: str, user_id: str = "default") -> DebugSessionTracker:
    """Get or create a debug tracker for the session."""
    global _debug_tracker
    if _debug_tracker is None or _debug_tracker.session_id != session_id:
        _debug_tracker = DebugSessionTracker(session_id, user_id)
    return _debug_tracker

def log_debug_info(session_id: str, user_id: str, turn_index: int, info_type: str, **kwargs):
    """Convenience function to log debug information."""
    tracker = get_debug_tracker(session_id, user_id)
    
    if info_type == "context_extraction":
        tracker.log_context_extraction(turn_index, kwargs.get("prompt", ""), kwargs.get("response", ""))
    elif info_type == "strategy_decision":
        tracker.log_strategy_decision(turn_index, kwargs.get("prompt", ""), kwargs.get("decision", {}))
    elif info_type == "agent_execution":
        tracker.log_agent_execution(turn_index, kwargs.get("agent_name", ""), kwargs.get("params", {}), kwargs.get("result", ""))
    elif info_type == "tool_execution":
        tracker.log_tool_execution(turn_index, kwargs.get("tool_name", ""), kwargs.get("params", {}), kwargs.get("result", ""))
    elif info_type == "prompt":
        tracker.log_prompt(turn_index, kwargs.get("prompt_type", ""), kwargs.get("prompt", ""), kwargs.get("response", ""))
    elif info_type == "llm_interaction":
        tracker.log_llm_interaction(turn_index, kwargs.get("interaction_type", ""), kwargs.get("prompt", ""), kwargs.get("response", ""), kwargs.get("metadata", {}))
    elif info_type == "response":
        tracker.log_response(turn_index, kwargs.get("response_type", ""), kwargs.get("response", ""))
    elif info_type == "final_response":
        tracker.log_final_response(turn_index, kwargs.get("response", ""))
    elif info_type == "error":
        tracker.log_error(turn_index, kwargs.get("error", ""), kwargs.get("context", ""))
