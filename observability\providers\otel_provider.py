import asyncio
import functools
import inspect
import json
import logging
from typing import Any, <PERSON><PERSON><PERSON><PERSON><PERSON>, Call<PERSON>, Dict, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Union

from opentelemetry import context as otel_context
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.openai import OpenAIInstrumentor
from opentelemetry.instrumentation.pymongo import PymongoInstrumentor
from opentelemetry.instrumentation.weaviate import WeaviateInstrumentor
from opentelemetry.sdk.trace import Resource, SpanLimits, TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.trace.status import StatusCode

from .otel_utils.logging import (
    init_logging_instrumentor,
)
from .otel_utils.weaviate import (
    CUSTOM_WEAVIATE_WRAPPED_METHODS,
)
from ..types import ProviderFunctions, TraceableFunction
from config.settings import AgentConfig

# Global settings instance for helper functions
settings = AgentConfig()


def _init() -> None:
    # Refresh settings in case env changed at runtime
    global settings
    settings = AgentConfig()
    service_name = settings.otel_service_name
    span_limits = SpanLimits(
        max_events=1024,
    )
    otel_collector_endpoint = settings.otel_base_url

    provider = TracerProvider(
        span_limits=span_limits,
        resource=Resource(
            attributes={
                "service.name": service_name,
                "service.environment": settings.environment,
            },
        ),
    )

    processor = BatchSpanProcessor(
        OTLPSpanExporter(
            endpoint=f"{otel_collector_endpoint}/v1/traces",
        ),
    )

    provider.add_span_processor(processor)
    trace.set_tracer_provider(provider)

    OpenAIInstrumentor().instrument()
    logging.info(
        f"OpenTelemetry & Traceloop initialised. Traces will be sent to {otel_collector_endpoint}",
    )

    if settings.otel_mongo_tracing:
        PymongoInstrumentor().instrument()
        logging.info("Pymongo Instrumentor initialised.")

    if settings.otel_weaviate_tracing:
        WeaviateInstrumentor().instrument()
        logging.info(
            f"Weaviate Instrumentor initialised. Custom Weaviate Wrapped Methods: {CUSTOM_WEAVIATE_WRAPPED_METHODS}",
        )

    init_logging_instrumentor()
    logging.info(
        f"Logging Instrumentor initialised. Root logger handlers: {logging.getLogger().handlers}",
    )


def _update_trace(
    *args: Any,
    **kwargs: Any,
) -> bool:
    try:
        current_span = trace.get_current_span()
        if current_span.is_recording():
            for key, value in kwargs.items():
                if "id" in key:
                    key = key.replace("_id", ".id")

                if not isinstance(value, (str, int, float, bool)):
                    if isinstance(value, list):
                        value = [str(v) for v in value]
                    else:
                        value = str(value)
                current_span.set_attribute(key, value)

            return True
        return False
    except Exception:
        return False


def _set_common_attributes(
    span: trace.Span,
    include_args: bool,
    attribute_prefix: str,
    args: Tuple[Any, ...],
    kwargs: Dict[str, Any],
):
    """Sets common attributes for a span based on function arguments."""
    if include_args:
        for i, arg_val in enumerate(args):
            arg_val_str = str(arg_val)
            if len(arg_val_str) > settings.trace_str_limits:
                arg_val_str = arg_val_str[: settings.trace_str_limits] + "..."
            if i == 0:
                continue
            try:
                span.set_attribute(f"{attribute_prefix}.{i}", arg_val_str)
            except Exception as e:
                span.set_attribute(
                    f"{attribute_prefix}.{i}.error",
                    f"Failed to serialize arg {i}: {e}",
                )
        for key, arg_val in kwargs.items():
            try:
                arg_val_str = str(arg_val)
                if len(arg_val_str) > 1000:
                    arg_val_str = arg_val_str[:1000] + "..."
                span.set_attribute(f"{attribute_prefix}.{key}", arg_val_str)
            except Exception as e:
                span.set_attribute(
                    f"{attribute_prefix}.{key}.error",
                    f"Failed to serialize kwarg {key}: {e}",
                )


def _set_result_attributes(
    span: trace.Span,
    include_res: bool,
    result: Any,
):
    """Sets result attributes for a span when include_res is True."""
    if include_res:
        try:
            # Convert result to string and truncate if too long (OpenTelemetry has attribute limits)
            result_str = str(result)
            if len(result_str) > settings.trace_str_limits:
                result_str = result_str[: settings.trace_str_limits] + "..."
            span.set_attribute("func.result", result_str)
            span.set_attribute("func.result.type", type(result).__name__)
        except Exception as e:
            span.set_attribute("func.result.error", f"Failed to serialize result: {e}")


async def iter_wrapper(span, result, include_iter_input_output):
    out, context = "", []
    token = None
    try:
        ctx = trace.set_span_in_context(span)
        token = otel_context.attach(ctx)
        async for item in result:
            # Add a custom event or attribute for chat_stream spans
            if include_iter_input_output:
                res = json.loads(item)
                if res.get("response", None) is not None:
                    out += res["response"]
                if res.get("contexts", None) is not None:
                    for con in res["contexts"]:
                        context.append(str(con))
            yield item
        if include_iter_input_output:
            _update_trace(
                output=out,
                context=context,
            )
        span.set_status(StatusCode.OK)
    except json.JSONDecodeError as e:
        logging.warning(
            f"Malformed JSON in streaming response: {e} - item: {item!r}",
        )
    except Exception as e:
        span.record_exception(e)
        span.set_status(
            StatusCode.ERROR,
            description=str(e),
        )
        raise
    finally:
        if token is not None:
            otel_context.detach(token)
        span.end()


class _SyncGeneratorTracer:
    """Wraps a synchronous generator to manage its span lifecycle."""

    def __init__(
        self,
        gen_obj: Iterator[Any],
        span: trace.Span,
        include_res: bool = False,
    ):
        self._gen_obj = gen_obj
        self._span = span
        self._include_res = include_res

    def __iter__(self):
        return self

    def __next__(self):
        with trace.use_span(self._span, end_on_exit=False):
            try:
                item = next(self._gen_obj)
                self._span.add_event(f"yielded_sync_item {item}")
                if self._include_res:
                    _set_result_attributes(self._span, True, item)
                return item
            except StopIteration:
                self._span.set_status(StatusCode.OK)
                self._span.end()
                raise
            except Exception as e:
                self._span.record_exception(e)
                self._span.set_status(StatusCode.ERROR, description=str(e))
                self._span.end()
                raise


class _AsyncGeneratorTracer:
    """Wraps an asynchronous generator to manage its span lifecycle."""

    def __init__(
        self,
        gen_obj: AsyncIterator[Any],
        span: trace.Span,
        include_res: bool = False,
    ):
        self._gen_obj = gen_obj
        self._span = span
        self._include_res = include_res

    def __aiter__(self):
        return self

    async def __anext__(self):
        with trace.use_span(self._span, end_on_exit=False):
            try:
                item = await self._gen_obj.__anext__()
                self._span.add_event(f"yielded_async_item {item}")
                if self._include_res:
                    _set_result_attributes(self._span, True, item)
                return item
            except StopAsyncIteration:
                self._span.set_status(StatusCode.OK)
                self._span.end()
                raise
            except Exception as e:
                self._span.record_exception(e)
                self._span.set_status(StatusCode.ERROR, description=str(e))
                self._span.end()
                raise


def _trace_function(
    *args: Any,
    **kwargs: Any,
) -> Callable[[TraceableFunction], TraceableFunction]:
    # Support both OTEL-specific arg names and Langfuse-style synonyms
    span_name = kwargs.get("span_name") or kwargs.get("name")
    span_kind = kwargs.get("span_kind", trace.SpanKind.INTERNAL)
    include_args = kwargs.get("include_args")
    if include_args is None:
        include_args = bool(kwargs.get("capture_input", False))
    include_res = kwargs.get("include_res")
    if include_res is None:
        include_res = bool(kwargs.get("capture_output", False))
    attribute_prefix = kwargs.get("attribute_prefix", "func.arg")
    include_iter_input_output = kwargs.get("include_iter_input_output", False)

    def decorator(func):
        # Determine if the function is a generator (sync or async)
        is_async_gen_func = inspect.isasyncgenfunction(func)
        is_sync_gen_func = inspect.isgeneratorfunction(func)

        current_span_name = (
            span_name
            if span_name
            else f"{func.__name__}"
            + (".iteration" if (is_async_gen_func or is_sync_gen_func) else "")
        )

        if is_async_gen_func or is_sync_gen_func:

            @functools.wraps(func)
            def generator_wrapper(
                *args: Any,
                **kwargs: Any,
            ) -> Union[Iterator[Any], AsyncIterator[Any]]:
                # Span name specific for generators, indicating an iteration
                # Start the span immediately when the generator function is called

                parent_span = trace.get_current_span()
                parent_ctx = trace.set_span_in_context(parent_span)

                span = trace.get_tracer(__name__).start_span(
                    current_span_name,
                    context=parent_ctx,
                    kind=span_kind,
                )
                # Set common attributes
                _set_common_attributes(
                    span,
                    include_args,
                    attribute_prefix,
                    args,
                    kwargs,
                )
                span.set_attribute("func.file", func.__code__.co_filename)
                span.set_attribute("func.module", func.__module__)
                span.set_attribute("func.name", func.__name__)

                try:
                    # Call the original generator function to get the generator object
                    # Ensure this span is the current one while constructing the generator_wrapper
                    # nested generator spans
                    with trace.use_span(span, end_on_exit=False):
                        gen_obj = func(*args, **kwargs)
                    # Return the appropriate tracer wrapper
                    return (
                        _AsyncGeneratorTracer(gen_obj, span, include_res)
                        if is_async_gen_func
                        else _SyncGeneratorTracer(gen_obj, span, include_res)
                    )
                except Exception as e:
                    # If an error occurs during generator setup, end the span immediately
                    span.record_exception(e)
                    span.set_status(StatusCode.ERROR, description=str(e))
                    span.end()
                    raise  # Re-raise the exception

            return generator_wrapper
        else:
            # --- Path for Regular Functions (Sync or Async) ---
            if asyncio.iscoroutinefunction(func):

                @functools.wraps(func)
                async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
                    parent_span = trace.get_current_span()
                    parent_ctx = trace.set_span_in_context(parent_span)

                    span = trace.get_tracer(__name__).start_span(
                        current_span_name,
                        kind=span_kind,
                        context=parent_ctx,
                    )
                    is_streaming_result = False
                    try:
                        _set_common_attributes(
                            span,
                            include_args,
                            attribute_prefix,
                            args,
                            kwargs,
                        )
                        span.set_attribute("func.file", func.__code__.co_filename)
                        span.set_attribute("func.module", func.__module__)
                        span.set_attribute("func.name", func.__name__)

                        with trace.use_span(span, end_on_exit=False):
                            result = await func(*args, **kwargs)

                        if hasattr(result, "__aiter__"):
                            is_streaming_result = True
                            return iter_wrapper(span, result, include_iter_input_output)

                        _set_result_attributes(span, include_res, result)
                        span.set_status(StatusCode.OK)
                        return result
                    except Exception as e:
                        span.record_exception(e)
                        span.set_status(StatusCode.ERROR, description=str(e))
                        raise
                    finally:
                        if not is_streaming_result:
                            span.end()

                return async_wrapper
            else:

                @functools.wraps(func)
                def sync_wrapper(*args: Any, **kwargs: Any) -> Any:  # noqa: WPS503
                    # Span lives within the 'with' block
                    with trace.get_tracer(__name__).start_as_current_span(
                        current_span_name,
                        kind=span_kind,
                    ) as span:
                        _set_common_attributes(
                            span,
                            include_args,
                            attribute_prefix,
                            args,
                            kwargs,
                        )
                        span.set_attribute("func.file", func.__code__.co_filename)
                        span.set_attribute("func.module", func.__module__)
                        span.set_attribute("func.name", func.__name__)
                        try:
                            result = func(*args, **kwargs)
                            _set_result_attributes(span, include_res, result)
                            return result
                        except Exception as e:
                            span.record_exception(e)
                            span.set_status(StatusCode.ERROR, description=str(e))
                            raise

                return sync_wrapper

    return decorator


def _observe(
    func: Callable[..., Any],
    *args: Any,
    **kwargs: Any,
) -> Callable[..., Any]:
    return _trace_function(*args, **kwargs)(func)


def get_provider_functions() -> ProviderFunctions:
    return {
        "update_trace": _update_trace,
        "observe": _observe,
        "name": "otel",
        "init": _init,
    }
