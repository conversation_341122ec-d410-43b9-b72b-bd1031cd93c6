"""
Strategy agent for deciding on the approach to handle user queries.

This agent Analyses user input and conversation context to determine
the best strategy for processing the request.
"""
import logging

from typing import Dict, Any
from agents.base_agent import BaseAgent

logger = logging.getLogger(__name__)


class StrategyAgent(BaseAgent):
    """
    Agent responsible for deciding on the strategy for handling user queries.
    
    Analyses user input, conversation history, and time context to determine
    whether the request is for search, conversation, basket operations, etc.
    """
    
    def __init__(self, llm_service):
        """
        Initialise the strategy agent.
        
        Args:
            llm_service: LLM service for making decisions
        """
        super().__init__(
            name="strategy_agent",
            description="Decide on the strategy for handling user queries",
            required_params=["user_input"]
        )
        self.llm_service = llm_service
    
    async def decide_strategy(self, user_input: str, conversation_history: str = "", time_context: str = "") -> Dict[str, Any]:
        """
        Decide on the strategy for handling the user input using LLM.
        
        Args:
            user_input: The user's input message
            conversation_history: Previous conversation context
            time_context: Current time context
            
        Returns:
            Dictionary containing strategy decision
        """
        try:
            # Use the existing strategy prompt
            from prompts.strategy import get_strategy_prompt
            
            # Define available tools for the strategy prompt
            available_tools = [
                {"name": "search_products", "description": "Search for products based on customer requirements"},
                {"name": "ask_follow_up_question", "description": "Ask follow-up questions to gather more information"},
                {"name": "product_details", "description": "Get detailed information about specific products"},
                {"name": "update_basket", "description": "Add, remove, or manage items in the customer's basket"},
                {"name": "basket_info", "description": "Get information about the current basket contents"},
                {"name": "suggest_complementary_products", "description": "Suggest complementary products for items of interest"}
            ]
            
            # Create focused context from conversation history
            focused_context = conversation_history if conversation_history else "First interaction with customer"
            
            # Get the strategy prompt
            strategy_prompt = get_strategy_prompt(
                query=user_input,
                focused_context=focused_context,
                available_tools=available_tools,
                time_context=time_context
            )
            
            # Define the expected JSON schema for strategy response
            strategy_schema = {
                "type": "object",
                "properties": {
                    "next_best_actions": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of tools to execute in order"
                    },
                    "tool_parameters": {
                        "type": "object",
                        "description": "Parameters for each tool"
                    },
                    "focused_instructions": {
                        "type": "string",
                        "description": "Detailed instructions for other agents based on the focused context"
                    },
                    "reasoning": {
                        "type": "string",
                        "description": "Brief explanation of tool sequence and why"
                    },
                    "extracted_product": {
                        "type": "string",
                        "description": "The specific product name extracted from context when user provides size response, empty string if not applicable"
                    }
                },
                "required": ["next_best_actions", "tool_parameters", "focused_instructions", "reasoning", "extracted_product"]
            }
            
            # Get structured LLM response
            response = await self.llm_service.generate_structured_response(
                strategy_prompt, strategy_schema, "strategy"
            )
            
            # Check if the structured response contains an error
            if "error" in response:
                logger.error(f"Structured response error: {response['error']}")
                raise ValueError(f"LLM provider error: {response['error']}")
            
            # The response should already be a parsed dictionary
            strategy_decision = response
            logger.info(f"🔍 Strategy response: {strategy_decision}")
            
            # Extract the action from the response
            if "next_best_actions" in strategy_decision:
                actions = strategy_decision["next_best_actions"]
                if actions and len(actions) > 0:
                    
                    primary_action = actions[0]
                    
                    strategy_result = {
                        "actions": actions,
                        "reasoning": strategy_decision.get("reasoning", f"Selected {primary_action} as primary action"),
                        "tools": actions,
                        "tool_parameters": strategy_decision.get("tool_parameters", {}),
                        "focused_instructions": strategy_decision.get("focused_instructions", ""),
                        "extracted_product": strategy_decision.get("extracted_product", "")
                    }
                else:
                    raise ValueError("No actions in response")
            else:
                raise ValueError("No next_best_actions in response")
            
            return strategy_result
            
        except Exception as e:
            logger.error(f"Error in strategy decision: {e}")
            # Let the error propagate instead of using fallback logic
            raise ValueError(f"Strategy agent failed: {e}")
    
    async def execute(self, params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the strategy agent with given parameters and context.
        
        Args:
            params: Parameters containing user_input
            context: Additional context information
            
        Returns:
            Dictionary containing strategy decision
        """
        user_input = params.get("user_input", "")
        conversation_history = context.get("conversation_history", "")
        time_context = context.get("time_context", "")
        
        strategy = await self.decide_strategy(user_input, conversation_history, time_context)
        return {
            "success": True,
            "strategy": strategy,
            "action": strategy.get("action", "search")
        }
    
    def _get_parameter_schema(self) -> Dict[str, Any]:
        """
        Return the parameter schema for this agent.
        
        Returns:
            Dictionary containing parameter schema
        """
        return {
            "user_input": {
                "type": "string",
                "description": "The user's input message"
            }
        }
    
    async def process(self, user_input: str, conversation_history: str = "", time_context: str = "") -> str:
        """
        Process user input and return strategy decision.
        
        Args:
            user_input: User's input message
            conversation_history: Previous conversation context
            time_context: Current time context
            
        Returns:
            Strategy decision as string
        """
        strategy = await self.decide_strategy(user_input, conversation_history, time_context)
        return strategy.get("action", "search")
