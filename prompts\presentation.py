"""
Result presentation prompts for the <PERSON><PERSON>by agent.
Handles presenting search results and product details in natural, conversational ways.
"""


def get_results_presentation_prompt(
    search_results: list, 
    customer_query: str, 
    focused_instructions: str = ""
    ) -> str:
    """
    Generate a prompt for presenting search results in a natural, conversational way.
    
    Args:
        search_results (list): List of search results to present
        customer_query (str): The customer's original query
        focused_instructions (str): Focused, relevant instructions for the current turn
        
    Returns:
        str: A formatted prompt for presenting search results
    """
    
    # Format the search results
    results_section = ""
    if not search_results:
        results_section = "No products found"
    else:
        for i, product in enumerate(search_results, 1):
            title = product.get('title', 'Unknown Product')
            price = product.get('sale_price') if product.get('sale_price') is not None else product.get('price', 'N/A')
            available_sizes = product.get('available_sizes', [])
            url = product.get('url', '')
            brand = product.get('brand', '')
            colour = product.get('colour', '')
            category = product.get('category', '')
            
            sizes_text = f"Available sizes: {', '.join(map(str, available_sizes))}" if available_sizes else "Size information not available"
            
            results_section += f"""
            **Product {i}**: {title}
            - Price: £{price}
            - Sizes: {sizes_text}
            - Brand: {brand if brand else 'Not specified'}
            - Colour: {colour if colour else 'Not specified'}
            - Category: {category if category else 'Not specified'}
            - URL: {url}
            """
    
    context_section = f"\n**INSTRUCTIONS**: {focused_instructions}" if focused_instructions else ""
    
    return f"""
    You are a professional fashion retail assistant. Present these search results in a helpful, natural way.
    
    **CUSTOMER QUERY**: "{customer_query}"{context_section}
    
    **SEARCH RESULTS**:
    {results_section}
    
    **INSTRUCTIONS**:
    - Present the results in a conversational, helpful manner
    - Highlight key features and benefits of each product
    - Use a professional but friendly tone
    - Include relevant follow-up questions to continue the conversation
    - Format URLs as: 🔗 [URL] on their own line
    - Focus on being informative and useful
    
    Present the information naturally and helpfully.
    """


def get_product_details_presentation_prompt(products: list, user_query: str, focused_context: str = "") -> str:
    """
    Generate a prompt for presenting product details in a natural, conversational way.
    
    Args:
        products (list): List of product information dictionaries, or single product dict
        user_query (str): The user's original query
        focused_context (str): Focused context about the conversation and what the user is asking about
        
    Returns:
        str: Formatted prompt for product details presentation
    """
    
    # Handle both single product and multiple products
    if isinstance(products, dict):
        products = [products]
    
    # Format product information
    products_section = ""
    for i, product in enumerate(products, 1):
        title = product.get('title', 'Unknown')
        brand = product.get('brand', 'Unknown')
        price = product.get('price', 0)
        sale_price = product.get('sale_price', 0)
        special_offer = product.get('special_offer', 'No special offers available')
        colour = product.get('colour', 'Unknown')
        category = product.get('category', 'Unknown')
        available_sizes = product.get('available_sizes', [])
        url = product.get('url', '')
        
        sizes_text = ', '.join(available_sizes) if available_sizes else 'Size information not available'
        price_text = f"£{sale_price:.2f}" if sale_price and sale_price < price else f"£{price:.2f}"
        if sale_price and sale_price < price:
            price_text += f" (was £{price:.2f})"
        
        products_section += f"""
        **Product {i}**: {title}
        - Brand: {brand}
        - Price: {price_text}
        - Special Offer: {special_offer}
        - Colour: {colour}
        - Category: {category}
        - Available Sizes: {sizes_text}
        - URL: {url}
        """
    
    context_section = f"\n**FOCUSED CONTEXT**: {focused_context}" if focused_context else ""
    
    return f"""
    You are a fashion retail assistant. Present these product details to answer the user's question.
    
    **USER'S QUERY**: "{user_query}"{context_section}
    
    **PRODUCTS**:
    {products_section}
    
    **INSTRUCTIONS**:
    - Answer the user's question directly and comprehensively
    - Use only the product information provided - don't make up details
    - Be helpful and informative without being verbose
    - Include relevant follow-up questions
    - Format URLs as: 🔗 [URL] on their own line
    - Use a professional but friendly tone
    
    Present the information naturally and helpfully.
    """


def get_complementary_products_presentation_prompt(
    primary_product: dict,
    complementary_products: list,
    user_query: str,
    focused_context: str = ""
    ) -> str:
    """
    Generate a prompt for presenting complementary products alongside a primary product.
    
    Args:
        primary_product (dict): The main product the user is interested in
        complementary_products (list): List of complementary products to suggest
        user_query (str): The user's original query
        focused_context (str): Focused, relevant context for the current turn
        
    Returns:
        str: Formatted prompt for complementary products presentation
    """
    
    # Format primary product info
    primary_price = primary_product.get('sale_price') if primary_product.get('sale_price') is not None else primary_product.get('price', 0)
    primary_sizes = ', '.join(primary_product.get('available_sizes', [])) if primary_product.get('available_sizes') else 'Size information not available'
    
    primary_info = f"""
    **Primary Product**: {primary_product.get('title', 'Unknown')}
    - Brand: {primary_product.get('brand', 'Unknown')}
    - Price: £{primary_price:.2f}
    - Category: {primary_product.get('category', 'Unknown')}
    - Available Sizes: {primary_sizes}
    """
    
    # Format complementary products info
    complementary_info = ""
    for i, product in enumerate(complementary_products, 1):
        comp_price = product.get('sale_price') if product.get('sale_price') is not None else product.get('price', 0)
        comp_sizes = ', '.join(product.get('available_sizes', [])) if product.get('available_sizes') else 'Size information not available'
        
        complementary_info += f"""
        **Product {i}**: {product.get('title', 'Unknown')}
        - Brand: {product.get('brand', 'Unknown')}
        - Price: £{comp_price:.2f}
        - Category: {product.get('category', 'Unknown')}
        - Available Sizes: {comp_sizes}
        - URL: {product.get('url', '')}
        """
    
    context_section = f"\n**FOCUSED CONTEXT**: {focused_context}" if focused_context else ""
    
    return f"""
    You are a fashion retail assistant. Present these complementary products that would work well with the primary product.
    
    **USER'S QUERY**: "{user_query}"{context_section}
    
    **PRIMARY PRODUCT**:
    {primary_info}
    
    **COMPLEMENTARY PRODUCTS**:
    {complementary_info}
    
    **INSTRUCTIONS**:
    - Acknowledge their interest in the primary product
    - Explain why these complementary products work well together
    - Highlight the value of creating a complete look
    - Be specific about how each complementary product enhances the primary product
    - Include relevant follow-up questions
    - Format URLs as: 🔗 [URL] on their own line
    - Use a warm, enthusiastic tone
    
    Present the information naturally and helpfully.
    """


def get_multi_tool_presentation_prompt(
    tool_results: list,
    user_query: str,
    focused_context: str = ""
    ) -> str:
    """
    Generate a prompt for presenting results from multiple tool executions.
    
    Args:
        tool_results (list): List of dictionaries containing tool results with keys:
            - tool_name (str): Name of the tool that was executed
            - tool_description (str): Description of what the tool does
            - result (any): The actual result from the tool
            - success (bool): Whether the tool execution was successful
        user_query (str): The user's original query
        focused_context (str): Focused, relevant context for the current turn
        
    Returns:
        str: Formatted prompt for multi-tool presentation
    """
    
    # Build the tool results section
    tool_results_section = ""
    for tool_result in tool_results:
        tool_name = tool_result.get('tool_name', 'Unknown Tool')
        tool_description = tool_result.get('tool_description', 'Tool executed')
        result = tool_result.get('result', 'No result available')
        success = tool_result.get('success', False)
        
        if success:
            tool_results_section += f"""
            **{tool_name}** ({tool_description}):
            {result}
            """
        else:
            tool_results_section += f"""
            **{tool_name}** ({tool_description}): Failed - {result}
            """
    
    context_section = f"\n**FOCUSED CONTEXT**: {focused_context}" if focused_context else ""
    
    return f"""
    You are a fashion retail assistant. Present the results from multiple tools in a natural, helpful way.
    
    **USER'S QUERY**: "{user_query}"{context_section}
    
    **TOOL RESULTS**:
    {tool_results_section}
    
    **INSTRUCTIONS**:
    - Answer the user's question directly and comprehensively
    - Present the information in a logical, conversational flow
    - Combine insights from all tools into a coherent response
    - Be helpful and informative without being verbose
    - Use a professional but friendly tone
    - Focus on what the user actually needs to know
    - If any tools failed, acknowledge it briefly but focus on successful results
    
    Present the information naturally and helpfully.
    """
