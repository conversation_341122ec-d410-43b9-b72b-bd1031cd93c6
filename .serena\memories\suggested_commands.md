# Suggested Commands for Development

## Running the Application

### Backend Server
```bash
# Start FastAPI server (API mode)
python api.py

# Run in interactive mode for testing
python main.py

# Run with uvicorn directly
uvicorn api:app --reload
```

### Frontend Development
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Database Services
```bash
# Start MongoDB and Weaviate with Docker
docker-compose up -d

# Check Docker services status
docker-compose ps

# Stop services
docker-compose down
```

## Python Environment
```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment (Windows)
.venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install with pyproject.toml (using pip)
pip install -e .
```

## Testing and Validation
```bash
# Test setup configuration
python test-setup.py

# Run evaluation script
python evaluate.py

# Check pytest configuration
pytest
```

## Git Commands (Windows)
```bash
# Check git status
git status

# View recent commits
git log --oneline -n 5

# Create new branch
git checkout -b feature-name

# Stage changes
git add .

# Commit changes
git commit -m "message"

# Push to remote
git push origin branch-name
```

## Utility Commands (Windows)
```bash
# List directory contents
dir
ls  # Also works with Git Bash

# Change directory
cd path\to\directory

# Find files
where filename
dir /s /b *pattern*

# Search in files (Windows)
findstr /s "pattern" *.py

# Environment variables
echo %VARIABLE_NAME%
set VARIABLE_NAME=value
```

## Quick Start Scripts
```bash
# Windows quick start
quick-start.bat

# Unix/Mac quick start
./quick-start.sh
```

## API Documentation
```
# Once server is running, access:
http://localhost:8000/docs  # Interactive API documentation (Swagger)
http://localhost:8000/redoc # Alternative API documentation
```

## Development Ports
- **Backend API**: http://localhost:8000
- **Frontend**: http://localhost:5173 (Vite dev server)
- **MongoDB**: localhost:27017
- **Weaviate**: localhost:8080

## Environment Setup
```bash
# Copy example environment file
copy env.example .env

# Edit .env file with API keys
notepad .env
```