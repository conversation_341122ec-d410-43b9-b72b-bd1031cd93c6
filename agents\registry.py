"""
Central registry for all available agent components.
Manages agent registration, retrieval, and execution.
"""
from .base_agent import BaseAgent
from typing import Dict, List, Any

class AgentsRegistry:
    """Central registry for all available agent components"""
    
    def __init__(self):
        self._agents: Dict[str, BaseAgent] = {}
    
    def register_agent(self, agent: BaseAgent):
        """Register an agent in the registry"""
        self._agents[agent.name] = agent
    
    def get_agent(self, name: str) -> BaseAgent:
        """Get an agent by name"""
        return self._agents.get(name)
    
    def list_agents(self) -> List[str]:
        """List all available agent names"""
        return list(self._agents.keys())
    
    def get_agents_schema(self) -> List[Dict[str, Any]]:
        """Get schema for all agents (for LLM function calling)"""
        return [agent.get_schema() for agent in self._agents.values()]
    
    async def execute_agent(self, agent_name: str, params: Dict[str, Any], 
                    context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute an agent by name with parameters"""
        agent = self.get_agent(agent_name)
        if not agent:
            raise ValueError(f"Agent '{agent_name}' not found")
        return await agent.execute(params, context)
