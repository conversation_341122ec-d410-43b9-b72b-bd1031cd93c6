import time as time_module
from functools import wraps
from typing import Callable, Any

def time_weaviate(func: Callable[..., Any]) -> Any:
    """
    Simple timing decorator for Weaviate operations.

    Args:
        func (Callable[..., Any]): The function to be decorated

    Returns:
        function: Decorated function that logs execution duration
    """
    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time_module.time()
        try:
            return func(*args, **kwargs)
        finally:
            duration = time_module.time() - start_time
            print(f"Weaviate {func.__name__}: {duration:.3f}s")

    return wrapper
