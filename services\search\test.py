# import os, sys
# # Make repo root importable when running as: python cc_multiagentic/scripts/test_recommendations_direct.py
# REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
# if REPO_ROOT not in sys.path:
#     sys.path.insert(0, REPO_ROOT)

# from recommendations import vi_recommendations  # noqa: E402

# # ==== EDIT THESE 3 LINES ====
# APP_KEY = "e2eb15c3ae064784bcb30e077ae6d3ae"   # <-- your real key
# PLACEMENT_ID = 8675                              # <-- put your real placement (int), e.g. 666 or 667
# PRODUCT_ID = "1099181-47-L"         # <-- product_id from your catalog (e.g. from console/search)
# # ============================

# def main():
#     if "PUT_REAL_PRODUCT_ID_HERE" in PRODUCT_ID:
#         raise SystemExit("Please set PRODUCT_ID to a real product_id from your catalog.")

#     res = vi_recommendations(
#         product_id=PRODUCT_ID,
#         app_key=APP_KEY,
#         placement_id=PLACEMENT_ID,
#         limit=10,
#         return_product_info=True,
#         show_best_product_images=True,
#     )

#     print("Status:", res.get("status"))
#     print("Total:", res.get("total"), "| Page:", res.get("page"), "| Limit:", res.get("limit"))

#     if "product_info" in res:
#         pi = res["product_info"] or {}
#         print("Query product:", pi.get("product_id"))

#     items = res.get("result", []) or []
#     print(f"Returned {len(items)} items (showing up to 5):")
#     for it in items[:5]:
#         pid = it.get("product_id")
#         data = it.get("data") or {}
#         print("-", pid, "|", data.get("title"), "|", data.get("price"), "|", data.get("main_image_url"))

#     if res.get("status") != "OK" and "error" in res:
#         print("Error:", res["error"])

# if __name__ == "__main__":
#     main()

# from multisearch import vi_multisearch
# from recommendations import vi_recommendations
# ms = vi_multisearch(
#     app_key="e2eb15c3ae064784bcb30e077ae6d3ae",
#     placement_id=8675,   # (search placement works fine here)
#     q="women party tops",
#     limit=1,
# )
# pid = ms["result"][0]["product_id"]
# print("Found product_id:", pid)

# res = vi_recommendations(
#     product_id=pid,
#     app_key="e2eb15c3ae064784bcb30e077ae6d3ae",
#     placement_id=8675,
#     limit=5,
# )
# print(res)

from multisearch import vi_multisearch
import json
APP_KEY = "e2eb15c3ae064784bcb30e077ae6d3ae"
INSIGHT_PLACEMENT_ID = 8675   # product insight placement

res = vi_multisearch(
    app_key=APP_KEY,
    placement_id=INSIGHT_PLACEMENT_ID,
    pid="1099181-47-L",  # any product_id from your catalog
    limit=10
)
print("Status:", res.get("status"))
print("Returned:", len(res.get("result", [])))
with open("multisearch_result.json", "w", encoding="utf-8") as f:
    json.dump(res, f, indent=2, ensure_ascii=False)

print("✅ JSON response saved to multisearch_result.json")
