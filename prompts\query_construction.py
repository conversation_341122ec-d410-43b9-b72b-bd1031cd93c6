"""
Query construction prompts for the <PERSON><PERSON>by agent.
Handles building and reconstructing search queries for both Weaviate and Visenze.
"""




def get_weaviate_query_construction_prompt(customer_requirements: str, focused_instructions: str = "") -> str:
    """
    Generate a prompt for constructing Weaviate queries with result limits.
    
    Args:
        customer_requirements (str): What the customer is looking for
        focused_instructions (str): Focused, relevant instructions for the current turn
        
    Returns:
        str: A formatted prompt for constructing structured search queries
    """
    
    return f"""
    Customer intent: "{customer_requirements}"

    {f"**INSTRUCTIONS**: {focused_instructions}" if focused_instructions else ""}

    Build a Weaviate query JSON (WEAVIATE_QUERY_SCHEMA).

    RULES:
    - Stay on intent. If the user asked for black dresses, target dresses in black. Do not drift to other categories or colours.
    - Focus on the specific requirements provided by the customer.
    - Keep filters minimal: prefer only product_category and color unless explicitly requested.
    - Start broad, then refine: result_limit 8–10 (5–8 if very specific).
    - Provide a few clear search_terms (synonyms/variants are fine). Avoid over‑filtering.
    - Use field name "color" (not "colour").
    - Add brief reasoning (≤ 2 sentences).

    AVAILABLE FIELDS (use exactly these names):
    - product_category: cap, hat, beanie, jacket, dress, scarf, etc.
    - color: black, blue, red, green, etc. (NOT "colour")
    - size: Various sizes
    - price/sale_price: Numbers
    - brand: Brand names
    - collection: Collection names
    - material: Fabric/material types
    - occasion: Formal, casual, work, etc.
    - pattern: Solid, striped, floral, etc.
    - style: Modern, classic, vintage, etc.
    - gender: Men's, women's, unisex
    - availability: In stock status
    - special_offer: Any special deals or offers

    EXAMPLES:

    Intent: "blue blazer for work"
    Response: {{
        "search_strategy": "specific_criteria",
        "search_type": "hybrid",
        "search_terms": ["blue blazer", "blue jacket", "professional jacket"],
        "primary_filters": {{
            "product_category": "jacket",
            "occasion": "work"
        }},
        "price_filters": {{}},
        "result_limit": 8,
        "reasoning": "Searching for blue jackets with work occasion filter."
    }}

    Intent: "round neck blouse"
    Response: {{
        "search_strategy": "specific_criteria",
        "search_type": "hybrid",
        "search_terms": ["round neck blouse", "crew neck blouse", "blouse"],
        "primary_filters": {{
            "product_category": "blouse"
        }},
        "price_filters": {{}},
        "result_limit": 8,
        "reasoning": "Searching for blouses with round neck focus."
    }}

    Intent: "plus-size dresses"
    Response: {{
        "search_strategy": "category_browse",
        "search_type": "hybrid",
        "search_terms": ["plus size dresses", "large dresses", "dresses"],
        "primary_filters": {{
            "product_category": "dress"
        }},
        "price_filters": {{}},
        "result_limit": 10,
        "reasoning": "Searching for plus-size dresses with broad terms."
    }}

    Respond with valid JSON matching the schema.
    """


def get_weaviate_query_reconstruction_prompt(
    original_query: str, 
    search_results: list, 
    customer_feedback: str, 
    focused_context: str = ""
) -> str:
    """
    Generate a prompt for reconstructing Weaviate queries based on customer feedback.
    
    Args:
        original_query (str): The original search query
        search_results (list): Current search results
        customer_feedback (str): Customer's feedback about the results
        focused_context (str): Focused, relevant context for the current turn
        
    Returns:
        str: A formatted prompt for reconstructing search queries
    """
    
    return f"""
    The customer's original query was: "{original_query}"

    Current search results: {len(search_results)} items found

    Customer feedback: "{customer_feedback}"

    {f"**CONTEXT**: {focused_context}" if focused_context else ""}

    Based on this feedback, reconstruct the Weaviate query to better match what the customer is looking for.

    RULES:
    - Analyse the feedback to understand what needs to change
    - Adjust search terms, filters, or strategy as needed
    - Keep the core intent but refine based on feedback
    - Provide clear reasoning for the changes

    AVAILABLE FIELDS (use exactly these names):
    - product_category: cap, hat, beanie, jacket, dress, scarf, etc.
    - color: black, blue, red, green, etc. (NOT "colour")
    - size: Various sizes
    - price/sale_price: Numbers
    - brand: Brand names
    - collection: Collection names
    - material: Fabric/material types
    - occasion: Formal, casual, work, etc.
    - pattern: Solid, striped, floral, etc.
    - style: Modern, classic, vintage, etc.
    - gender: Men's, women's, unisex
    - availability: In stock status
    - special_offer: Any special deals or offers

    Respond with valid JSON matching the WEAVIATE_QUERY_SCHEMA, including reasoning for the changes.
    """


def get_visenze_query_construction_prompt(customer_requirements: str, focused_instructions: str = "") -> str:
    """
    Generate a prompt for constructing Visenze queries with result limits.
    
    Args:
        customer_requirements (str): What the customer is looking for
        focused_instructions (str): Focused, relevant instructions for the current turn
        
    Returns:
        str: A formatted prompt for constructing structured search queries
    """
    
    return f"""
    Customer intent: "{customer_requirements}"

    {f"**INSTRUCTIONS**: {focused_instructions}" if focused_instructions else ""}

    Build a Visenze query JSON matching this exact schema:

    VISENZE_QUERY_SCHEMA = {{
        "type": "object",
        "properties": {{
            "search_strategy": {{
                "type": "string",
                "enum": ["specific_criteria", "semantic_exploration", "category_browse", "color_coordination", "occasion_based"]
            }},
            "search_type": {{
                "type": "string", 
                "enum": ["hybrid", "semantic", "keyword"]
            }},
            "search_terms": {{
                "type": "array",
                "items": {{"type": "string"}}
            }},
            "primary_filters": {{
                "type": "object",
                "properties": {{
                    "category": {{"type": "string"}},  
                    "color": {{"type": "string"}},            
                    "gender": {{"type": "string"}},
                    "material": {{"type": "string"}},
                    "style": {{"type": "string"}},
                    "occasion": {{"type": "string"}},
                    "pattern": {{"type": "string"}},
                    "brand": {{"type": "string"}}
                }}
            }},
            "price_filters": {{
                "type": "object", 
                "properties": {{
                    "min_price": {{"type": "number"}},
                    "max_price": {{"type": "number"}}
                }}
            }},
            "result_limit": {{"type": "integer"}},
            "reasoning": {{"type": "string"}}
        }},
        "required": ["search_strategy", "search_type", "search_terms", "primary_filters", "price_filters", "result_limit", "reasoning"]
    }}

    RULES:
    - Stay on intent. If the user asked for black dresses, target dresses in black. Do not drift to other categories or colours.
    - Focus on the specific requirements provided by the customer.
    - Keep filters minimal: prefer only category and color unless explicitly requested.
    - Start broad, then refine: result_limit 8–10 (5–8 if very specific).
    - Provide a few clear search_terms (synonyms/variants are fine). Avoid over‑filtering.
    - Use field name "color" (not "colour").
    - Add brief reasoning (≤ 2 sentences).

    AVAILABLE FIELDS (use exactly these names):
    - category: cap, hat, beanie, jacket, dress, scarf, etc.
    - color: black, blue, red, green, etc. (NOT "colour")
    - size: Various sizes
    - price: Numbers
    - brand: Brand names
    - collection: Collection names
    - material: Fabric/material types
    - occasion: Formal, casual, work, etc.
    - pattern: Solid, striped, floral, etc.
    - style: Modern, classic, vintage, etc.
    - gender: Men's, women's, unisex
    - availability: In stock status
    - special_offer: Any special deals or offers

    Respond with valid JSON matching the schema above, including reasoning for the changes.
    """


def get_visenze_query_reconstruction_prompt(feedback_history: list, last_query: dict, focused_context: str = "") -> str:
    """
    Generate a prompt for reconstructing Visenze queries based on feedback.
    
    Args:
        feedback_history (list): List of feedback objects from previous evaluations
        last_query (dict): The last executed Visenze query parameters
        focused_context (str): Focused, relevant context for the current turn
        
    Returns:
        str: A formatted prompt for reconstructing search queries
    """
    
    feedback_text = "\n".join([
        f"- {fb.get('reasoning', 'No reasoning provided')}" 
        for fb in feedback_history
    ]) if feedback_history else "No previous feedback available"
    
    return f"""
    Previous query: {last_query}
    
    Feedback from evaluations:
    {feedback_text}
    
    {f"**FOCUSED CONTEXT**: {focused_context}" if focused_context else ""}
    
    Reconstruct the Visenze query based on the feedback and context.
    
    Build a Visenze query JSON matching this exact schema:

    VISENZE_QUERY_SCHEMA = {{
        "type": "object",
        "properties": {{
            "search_strategy": {{
                "type": "string",
                "enum": ["specific_criteria", "semantic_exploration", "category_browse", "color_coordination", "occasion_based"]
            }},
            "search_type": {{
                "type": "string", 
                "enum": ["hybrid", "semantic", "keyword"]
            }},
            "search_terms": {{
                "type": "array",
                "items": {{"type": "string"}}
            }},
            "primary_filters": {{
                "type": "object",
                "properties": {{
                    "category": {{"type": "string"}},  
                    "color": {{"type": "string"}},            
                    "gender": {{"type": "string"}},
                    "material": {{"type": "string"}},
                    "style": {{"type": "string"}},
                    "occasion": {{"type": "string"}},
                    "pattern": {{"type": "string"}},
                    "brand": {{"type": "string"}}
                }}
            }},
            "price_filters": {{
                "type": "object", 
                "properties": {{
                    "min_price": {{"type": "number"}},
                    "max_price": {{"type": "number"}}
                }}
            }},
            "result_limit": {{"type": "integer"}},
            "reasoning": {{"type": "string"}}
        }},
        "required": ["search_strategy", "search_type", "search_terms", "primary_filters", "price_filters", "result_limit", "reasoning"]
    }}
    
    RULES:
    - Address the specific issues mentioned in the feedback
    - Maintain the core search intent while improving the query
    - Adjust filters, search terms, or result limits as needed
    - Use field name "color" (not "colour")
    - Keep reasoning concise (≤ 2 sentences)
    
    AVAILABLE FIELDS (use exactly these names):
    - category: cap, hat, beanie, jacket, dress, scarf, etc.
    - color: black, blue, red, green, etc. (NOT "colour")
    - size: Various sizes
    - price: Numbers
    - brand: Brand names
    - collection: Collection names
    - material: Fabric/material types
    - occasion: Formal, casual, work, etc.
    - pattern: Solid, striped, floral, etc.
    - style: Modern, classic, vintage, etc.
    - gender: Men's, women's, unisex
    - availability: In stock status
    - special_offer: Any special deals or offers

    Respond with valid JSON matching the schema above, including reasoning for the changes.
    """
