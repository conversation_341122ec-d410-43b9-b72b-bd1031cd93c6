import time
import functools


def async_time_history(func):
    """
    Simple timing decorator for async history operations without prometheus metrics.
    
    Args:
        func: The async function to be decorated
        
    Returns:
        function: Decorated function with timing functionality
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = await func(*args, **kwargs)
        end_time = time.perf_counter()
        print(f"History operation {func.__name__} took {end_time - start_time:.3f} seconds")
        return result
    return wrapper


def time_weaviate(func):
    """
    Simple timing decorator for Weaviate operations without prometheus metrics.
    
    Args:
        func: The function to be decorated
        
    Returns:
        function: Decorated function with timing functionality
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        print(f"Weaviate operation {func.__name__} took {end_time - start_time:.3f} seconds")
        return result
    return wrapper
