"""
Session cache service for storing search results in MongoDB.
This avoids re-querying Weaviate for products that have already been retrieved in the current session.
"""
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, asdict, field

from bson import CodecOptions, UuidRepresentation
from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorCollection,
    AsyncIOMotorDatabase,
)

from .metrics import async_time_history
from core.entities import Product


@dataclass
class CachedProduct:
    """Represents a cached product with all necessary details"""
    title: str
    brand: str
    price: float
    sale_price: float
    colour: str
    size: str
    material: str
    category: str
    style: str
    occasion: str
    description: str
    product_url: str
    available_sizes: List[str]
    special_offer: str = ""
    weaviate_id: Optional[str] = None
    cached_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for MongoDB storage"""
        data = asdict(self)
        data['cached_at'] = self.cached_at
        return data
    
    def get_display_price(self) -> float:
        """Get the display price (sale price if available, otherwise regular price)"""
        return self.sale_price if self.sale_price and self.sale_price > 0 else self.price
    
    @classmethod
    def from_product(cls, product: Product, available_sizes: List[str] = None, weaviate_id: str = None) -> 'CachedProduct':
        """Create CachedProduct from Product entity"""
        if available_sizes is None:
            available_sizes = [product.size] if product.size else []
        
        return cls(
            title=product.title,
            brand=product.brand,
            price=product.price,
            sale_price=product.sale_price,
            colour=product.colour,
            size=product.size,
            material=product.material,
            category=product.category,
            style=product.style,
            occasion=product.occasion,
            description=product.description,
            product_url=product.product_url,
            available_sizes=available_sizes,
            special_offer=getattr(product, 'special_offer', ''),
            weaviate_id=weaviate_id
        )


@dataclass
class SearchSessionCache:
    """Represents a cached search session with products and metadata"""
    session_id: str
    user_id: str
    search_terms: List[str]
    products: List[CachedProduct]
    search_timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc) + timedelta(hours=1))
    search_type: str = "hybrid"


class SessionCacheService:
    """
    Service for caching search results in MongoDB to avoid re-querying Weaviate.
    
    This service stores search results for a configurable period, allowing the agent
    to quickly retrieve product details without making additional Weaviate queries.
    """
    
    def __init__(self, mongodb_url: str, mongo_database: str, cache_collection: str = "search_cache"):
        """
        Initialise the session cache service.
        
        Args:
            mongodb_url: MongoDB connection string
            mongo_database: Name of the MongoDB database
            cache_collection: Name of the collection for storing cache data
        """
        self.mongodb_url = mongodb_url
        self.mongo_database = mongo_database
        self.cache_collection_name = cache_collection
        self.mongo_client: Optional[AsyncIOMotorClient] = None
        self.db: Optional[AsyncIOMotorDatabase] = None
        self.cache_collection: Optional[AsyncIOMotorCollection] = None
        self.connected = False
        
        # Set up codec options for proper datetime handling
        self.codec_options = CodecOptions(
            uuid_representation=UuidRepresentation.STANDARD,
        )
        
        # Flag to track if indexes have been created
        self._indexes_created = False
    
    async def connect(self) -> bool:
        """
        Establish connection to MongoDB for session cache.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Create MongoDB client with timeouts to prevent hanging connections
            self.mongo_client = AsyncIOMotorClient(
                self.mongodb_url,
                serverSelectionTimeoutMS=5000,  # 5 second timeout for server selection
                connectTimeoutMS=5000,          # 5 second timeout for connection
                socketTimeoutMS=5000,           # 5 second timeout for socket operations
                maxPoolSize=1,                  # Limit connection pool size
                minPoolSize=0                   # Start with no connections
            )
            
            self.db = self.mongo_client[self.mongo_database]
            self.cache_collection = self.db.get_collection(self.cache_collection_name)
            
            # Test the connection
            await self.mongo_client.admin.command('ping')
            self.connected = True
            
            logger = logging.getLogger(__name__)
            logger.info(f"✅ Session cache service connected to MongoDB: {self.mongo_database}.{self.cache_collection_name}")
            return True
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Failed to connect session cache service to MongoDB: {e}")
            self.connected = False
            return False
    
    async def close(self):
        """Close the MongoDB connection."""
        if self.mongo_client:
            self.mongo_client.close()
            self.connected = False
            logger = logging.getLogger(__name__)
            logger.info("✅ Session cache service connection closed")
    
    async def ensure_indexes(self):
        """
        Ensure database indexes are created. Call this after instantiation.
        This method is idempotent and can be called multiple times safely.
        """
        if not self._indexes_created:
            await self._create_indexes()
            self._indexes_created = True
    
    async def _create_indexes(self):
        """Create database indexes for efficient querying"""
        try:
            # Index on session_id and user_id for quick lookups
            await self.cache_collection.create_index([
                ("session_id", 1),
                ("user_id", 1)
            ])
            
            # Index on expires_at for automatic cleanup
            await self.cache_collection.create_index([
                ("expires_at", 1)
            ], expireAfterSeconds=0)
            
            # Index on search_terms for fuzzy matching
            await self.cache_collection.create_index([
                ("search_terms", "text")
            ])
            
            logger = logging.getLogger(__name__)
            logger.info("✅ Session cache indexes created successfully")
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.warning(f"⚠️ Failed to create some indexes: {e}")
    
    @async_time_history
    async def cache_search_results(
        self,
        session_id: str,
        user_id: str,
        search_terms: List[str],
        products: List[Product],
        search_type: str = "hybrid"
    ) -> bool:
        """
        Cache search results for a session.
        
        Args:
            session_id: Unique session identifier
            user_id: User identifier
            search_terms: List of search terms used
            products: List of Product entities found
            search_type: Type of search performed
            
        Returns:
            bool: True if caching was successful
        """
        try:
            # Ensure indexes are created before operations
            await self.ensure_indexes()
            # Convert products to cached format
            cached_products = []
            for product in products:
                # Extract available sizes if available
                available_sizes = []
                if hasattr(product, 'available_sizes') and product.available_sizes:
                    available_sizes = product.available_sizes
                elif hasattr(product, 'size') and product.size:
                    available_sizes = [product.size]
                
                cached_product = CachedProduct.from_product(
                    product=product,
                    available_sizes=available_sizes
                )
                cached_products.append(cached_product)
            
            # Create cache entry
            cache_entry = SearchSessionCache(
                session_id=session_id,
                user_id=user_id,
                search_terms=search_terms,
                products=cached_products,
                search_type=search_type
            )
            
            # Store in MongoDB
            await self.cache_collection.replace_one(
                {
                    "session_id": session_id,
                    "user_id": user_id,
                    "search_terms": search_terms
                },
                asdict(cache_entry),
                upsert=True
            )
            
            logger = logging.getLogger(__name__)
            logger.info(f"💾 CACHE STORAGE:")
            logger.info(f"   Session ID: {session_id}")
            logger.info(f"   User ID: {user_id}")
            logger.info(f"   Search Terms: {search_terms}")
            logger.info(f"   Products Cached: {len(cached_products)}")
            for i, product in enumerate(cached_products):
                logger.info(f"   [{i+1}] {product.title} - £{product.get_display_price()}")
            logger.info(f"   ✅ Cache storage successful")
            return True
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Failed to cache search results: {e}")
            return False
    
    @async_time_history
    async def get_cached_product(
        self,
        session_id: str,
        user_id: str,
        product_name: str
    ) -> Optional[CachedProduct]:
        """
        Retrieve a cached product by name from the current session.
        
        Args:
            session_id: Current session identifier
            user_id: User identifier
            product_name: Name of the product to find
            
        Returns:
            CachedProduct if found, None otherwise
        """
        try:
            # Find the session cache
            cache_entry = await self.cache_collection.find_one({
                "session_id": session_id,
                "user_id": user_id,
                "expires_at": {"$gt": datetime.now(timezone.utc)}
            })
            
            logger = logging.getLogger(__name__)
            logger.info(f"🔍 CACHE LOOKUP - Product: {product_name}")
            logger.info(f"   Session ID: {session_id}")
            logger.info(f"   User ID: {user_id}")
            
            if not cache_entry:
                logger.info(f"   ❌ No cache entry found for session")
                return None
            
            cached_products = cache_entry.get("products", [])
            logger.info(f"   📦 Found {len(cached_products)} cached products")
            
            # Search for the product in cached results
            for i, product_data in enumerate(cached_products):
                product_title = product_data.get("title", "")
                logger.info(f"   [{i+1}] Checking: {product_title}")
                
                if self._product_matches_name(product_data, product_name):
                    # Convert back to CachedProduct
                    cached_product = CachedProduct(**product_data)
                    logger.info(f"   ✅ MATCH FOUND: {product_name} -> {product_title}")
                    return cached_product
            
            logger.info(f"   ❌ No matching product found for: {product_name}")
            return None
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Error retrieving cached product: {e}")
            return None
    
    @async_time_history
    async def get_cached_search_results(
        self,
        session_id: str,
        user_id: str,
        search_terms: List[str]
    ) -> Optional[List[CachedProduct]]:
        """
        Retrieve cached search results for similar search terms.
        
        Args:
            session_id: Current session identifier
            user_id: User identifier
            search_terms: Search terms to look for
            
        Returns:
            List of CachedProduct if found, None otherwise
        """
        try:
            # First try to find any cache entry for this session (more flexible)
            cache_entry = await self.cache_collection.find_one({
                "session_id": session_id,
                "user_id": user_id,
                "expires_at": {"$gt": datetime.now(timezone.utc)}
            })
            
            if not cache_entry:
                logger = logging.getLogger(__name__)
                logger.info(f"No cache entry found for session {session_id}")
                return None
            
            # If we found a cache entry, check if the search terms are similar
            stored_search_terms = cache_entry.get("search_terms", [])
            logger = logging.getLogger(__name__)
            logger.info(f"Found cache entry with stored search terms: {stored_search_terms}")
            logger.info(f"Looking for search terms: {search_terms}")
            
            # Check if any of our search terms match the stored terms
            # Use a more flexible matching approach
            search_terms_lower = [term.lower() for term in search_terms]
            stored_terms_lower = [term.lower() for term in stored_search_terms]
            
            # Check for any overlap between search terms
            has_overlap = any(
                any(search_term in stored_term or stored_term in search_term 
                    for stored_term in stored_terms_lower)
                for search_term in search_terms_lower
            )
            
            if not has_overlap:
                logger.info(f"No search term overlap found between {search_terms} and {stored_search_terms}")
                # Still return the results if we have them, as the user might want any cached products
                logger.info(f"Returning cached products anyway for session {session_id}")
            
            # Convert back to CachedProduct objects
            cached_products = []
            for product_data in cache_entry.get("products", []):
                try:
                    cached_product = CachedProduct(**product_data)
                    cached_products.append(cached_product)
                except Exception as e:
                    logger.warning(f"Failed to convert cached product: {e}")
                    continue
            
            logger = logging.getLogger(__name__)
            logger.info(f"✅ Found {len(cached_products)} cached products for session {session_id}")
            return cached_products
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Error retrieving cached search results: {e}")
            return None
    
    def _product_matches_name(self, product_data: Dict[str, Any], product_name: str) -> bool:
        """
        Check if a product matches the given name (fuzzy matching).
        
        Args:
            product_data: Product data from cache
            product_name: Name to match against
            
        Returns:
            bool: True if product matches the name
        """
        title = product_data.get("title", "").lower()
        search_name = product_name.lower()
        
        # Exact match
        if title == search_name:
            return True
        
        # Contains match
        if search_name in title or title in search_name:
            return True
        
        # Word boundary match
        search_words = search_name.split()

        
        # Check if all search words appear in title
        return all(word in title for word in search_words if len(word) > 2)
    
    @async_time_history
    async def get_any_cached_results(
        self,
        session_id: str,
        user_id: str
    ) -> Optional[List[CachedProduct]]:
        """
        Retrieve any cached search results for a session, regardless of search terms.
        
        Args:
            session_id: Current session identifier
            user_id: User identifier
            
        Returns:
            List of CachedProduct if found, None otherwise
        """
        try:
            # Find any cache entry for this session
            cache_entry = await self.cache_collection.find_one({
                "session_id": session_id,
                "user_id": user_id,
                "expires_at": {"$gt": datetime.now(timezone.utc)}
            })
            
            if not cache_entry:
                logger = logging.getLogger(__name__)
                logger.info(f"No cache entry found for session {session_id}")
                return None
            
            # Convert back to CachedProduct objects
            cached_products = []
            for product_data in cache_entry.get("products", []):
                try:
                    cached_product = CachedProduct(**product_data)
                    cached_products.append(cached_product)
                except Exception as e:
                    logger.warning(f"Failed to convert cached product: {e}")
                    continue
            
            logger = logging.getLogger(__name__)
            logger.info(f"✅ Found {len(cached_products)} cached products for session {session_id}")
            return cached_products
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Error retrieving any cached results: {e}")
            return None

    @async_time_history
    async def cleanup_expired_cache(self) -> int:
        """
        Clean up expired cache entries.
        
        Returns:
            int: Number of entries cleaned up
        """
        try:
            result = await self.cache_collection.delete_many({
                "expires_at": {"$lt": datetime.now(timezone.utc)}
            })
            
            logger = logging.getLogger(__name__)
            logger.info(f"🧹 Cleaned up {result.deleted_count} expired cache entries")
            return result.deleted_count
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Error cleaning up expired cache: {e}")
            return 0

    @async_time_history
    async def save_cached_product(
        self,
        session_id: str,
        user_id: str,
        product: CachedProduct,
        search_terms: List[str] = None
    ) -> bool:
        """
        Save a single product to the session cache.
        
        Args:
            session_id: Current session identifier
            user_id: User identifier
            product: Product to cache
            search_terms: Optional search terms for context
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if search_terms is None:
                search_terms = [product.title.lower()]
            
            # Check if we already have a cache entry for this session
            existing_cache = await self.cache_collection.find_one({
                "session_id": session_id,
                "user_id": user_id
            })
            
            if existing_cache:
                # Update existing cache entry
                products = existing_cache.get("products", [])
                
                # Check if product already exists (by title and brand)
                product_exists = False
                for i, existing_product in enumerate(products):
                    if (existing_product.get("title") == product.title and 
                        existing_product.get("brand") == product.brand):
                        # Update existing product
                        products[i] = product.to_dict()
                        product_exists = True
                        break
                
                if not product_exists:
                    # Add new product
                    products.append(product.to_dict())
                
                # Update cache entry
                await self.cache_collection.update_one(
                    {"_id": existing_cache["_id"]},
                    {
                        "$set": {
                            "products": products,
                            "search_terms": list(set(existing_cache.get("search_terms", []) + search_terms)),
                            "search_timestamp": datetime.now(timezone.utc),
                            "expires_at": datetime.now(timezone.utc) + timedelta(hours=1)
                        }
                    }
                )
            else:
                # Create new cache entry
                cache_entry = SearchSessionCache(
                    session_id=session_id,
                    user_id=user_id,
                    search_terms=search_terms,
                    products=[product],
                    search_timestamp=datetime.now(timezone.utc),
                    expires_at=datetime.now(timezone.utc) + timedelta(hours=1),
                    search_type="product_details"
                )
                
                await self.cache_collection.insert_one(asdict(cache_entry))
            
            logger = logging.getLogger(__name__)
            logger.info(f"✅ Cached product '{product.title}' for session {session_id}")
            return True
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"❌ Failed to cache product: {e}")
            return False
