version: '3.8'

services:
  jaeger:
    image: jaegertracing/all-in-one:1.54
    container_name: shoeby-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"   # Jaeger UI
      - "14268:14268"   # Jaeger ingest (Thrift over HTTP)
      - "14250:14250"   # Jaeger ingest (gRPC)
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    networks:
      - shoeby-network

  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.101.0
    container_name: shoeby-otel-collector
    restart: unless-stopped
    depends_on:
      - jaeger
    command: ["--config=/etc/otelcol-contrib/config.yaml"]
    volumes:
      - ./observability/otel-collector-config.yaml:/etc/otelcol-contrib/config.yaml:ro
    ports:
      - "4318:4318"    # OTLP HTTP receiver for app
      - "4317:4317"    # OTLP gRPC receiver (optional)
    networks:
      - shoeby-network

networks:
  shoeby-network:
    driver: bridge

