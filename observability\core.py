import asyncio
from typing import Any, Callable, Optional

from .config import observability_config
from .types import ProviderFunctions, ProviderName


def init_observability(provider_name: ProviderName) -> None:
    observability_config.set_provider(provider_name)
    observability_config.get_provider()["init"]()


def get_observability_provider(
    provider_name: Optional[ProviderName] = None,
) -> ProviderFunctions:
    return observability_config.get_provider(provider_name)


def observe_trace(
    provider: Optional[ProviderFunctions] = None,
    **kwargs: Any,
) -> Callable[[Callable], Callable]:

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        effective_provider = provider or get_observability_provider()
        return effective_provider["observe"](
            func,
            **kwargs,
        )

    return decorator


def update_trace(
    provider: Optional[ProviderFunctions] = None,
    **kwargs: Any,
) -> bool:
    effective_provider = provider or get_observability_provider()
    return effective_provider["update_trace"](**kwargs)


async def get_git_version() -> str:
    """Get git version asynchronously."""
    try:
        process = await asyncio.create_subprocess_exec(
            "git",
            "describe",
            "--tags",
            "--always",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, _ = await process.communicate()
        return stdout.decode().strip() if stdout else "unknown"
    except Exception:
        return "unknown"
