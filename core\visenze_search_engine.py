"""
Product search functionality with Visenze integration.
Handles all search operations, query building, and result processing using Visenze API.
"""
import logging
import os
from typing import List, Optional, Dict, Any
from core.entities import Product, SearchQuery, SearchState
from services.search.multisearch import vi_multisearch
from services.search.recommendations import vi_recommendations

logger = logging.getLogger(__name__)


class VisenzeSearchEngine:
    """
    Manages all product search operations using Visenze API.
    
    Encapsulates search logic, query construction, and result processing.
    """
    
    def __init__(self, app_key: Optional[str] = None, placement_id: Optional[str] = None, max_retries: int = 3):
        """
        Initialise the search engine with Visenze connection.
        
        Args:
            app_key: Visenze application key
            placement_id: Visenze placement ID for search
            max_retries: Maximum search retry attempts for poor results
        """
        logger.info("🔧 INITIALISING VISENZE SEARCH ENGINE:")
        logger.info(f"   App Key: {app_key or 'From Environment'}")
        logger.info(f"   Placement ID: {placement_id or 'From Environment'}")
        logger.info(f"   Max Retries: {max_retries}")
        
        self.app_key = app_key or os.getenv("VISENZE_APP_KEY")
        self.placement_id = placement_id or os.getenv("VISENZE_PLACEMENT_ID_SEARCH") or os.getenv("VISENZE_PLACEMENT_ID")
        self.max_retries = max_retries
        
        if not self.app_key or not self.placement_id:
            raise ValueError(
                "ViSenze app_key and placement_id are required. "
                "Pass as args or set VISENZE_APP_KEY and VISENZE_PLACEMENT_ID[_SEARCH]."
            )
        
        logger.info("✅ Visenze search engine initialised successfully")
    
    @classmethod
    def from_config(cls, config):
        """
        Create VisenzeSearchEngine instance from AgentConfig.
        
        Args:
            config: AgentConfig instance containing Visenze connection details
            
        Returns:
            VisenzeSearchEngine instance
        """
        return cls(
            app_key=getattr(config, 'visenze_app_key', None),
            placement_id=getattr(config, 'visenze_placement_id', None),
            max_retries=config.max_search_retries
        )
    
    def search_products(self, search_query: SearchQuery) -> SearchState:
        """
        Execute a product search with automatic retry and broadening logic.
        
        Implements intelligent search strategy:
        1. Execute initial search
        2. Evaluate result quality 
        3. Broaden search if results are insufficient
        4. Retry up to maximum attempts
        
        Args:
            search_query: SearchQuery containing search parameters
            
        Returns:
            SearchState with results and execution metadata
        """
        search_state = SearchState(current_query=search_query)
        
        logger.info(f"🚀 VISENZE SEARCH: {search_query.search_type} | {len(search_query.search_terms)} terms | {len(search_query.primary_filters)} filters | Limit: {search_query.result_limit}")
        
        while search_state.retry_count < self.max_retries:
            attempt_num = search_state.retry_count + 1
            logger.info(f"🔄 Attempt {attempt_num}/{self.max_retries}")
            
            # Execute the search
            success = self._execute_search(search_state)
            
            if not success:
                search_state.retry_count += 1
                logger.warning(f"   Attempt {attempt_num} failed, retrying...")
                continue
            
            # Check if results are satisfactory
            if self._are_results_satisfactory(search_state):
                logger.info(f"✅ Search successful: {search_state.get_result_count()} products found")
                return search_state
            
            # If we have some results on the last retry, return them
            if search_state.retry_count == self.max_retries - 1 and search_state.has_results():
                logger.warning(f"⚠️ Max retries reached, returning {search_state.get_result_count()} products")
                return search_state
            
            # Broaden search for next attempt
            logger.info("📈 Broadening search...")
            old_query = search_state.current_query
            search_state.current_query = self._broaden_search_query(search_state.current_query)
            
            logger.info(f"   Filters: {old_query.primary_filters} → {search_state.current_query.primary_filters}")
            
            search_state.retry_count += 1
        
        logger.warning("❌ All search attempts failed")
        return search_state
    
    def find_complementary_products(self, primary_product: Product, limit: int = 5) -> List[Product]:
        """
        Find products that complement a given primary product using Visenze recommendations.
        
        Args:
            primary_product: The product to find complements for
            limit: Maximum number of complementary products to return
            
        Returns:
            List of complementary Product entities
        """
        logger.info(f"🔍 Finding complementary products for: {primary_product.title} ({primary_product.category})")
        
        try:
            # Use Visenze recommendations API
            response = vi_recommendations(
                product_id=getattr(primary_product, 'product_id', ''),
                app_key=self.app_key,
                placement_id=self.placement_id,
                limit=limit,
                return_product_info=True
            )
            
            if response.get("status") != "OK":
                logger.error(f"Visenze recommendations failed: {response.get('error', 'Unknown error')}")
                return []
            
            complements = []
            for item in response.get("result", []):
                product = self._map_visenze_item_to_product(item)
                if product and product.category != primary_product.category:
                    complements.append(product)
            
            logger.info(f"✅ Found {len(complements)} complementary products")
            return complements
            
        except Exception as e:
            logger.error(f"❌ Error finding complementary products: {e}")
            logger.exception("Full complementary products error traceback:")
            return []
    
    def _execute_search(self, search_state: SearchState) -> bool:
        """
        Execute a single search attempt using current query parameters.
        
        Args:
            search_state: Current search state with query parameters
            
        Returns:
            True if search executed successfully, False otherwise
        """
        try:
            query = search_state.current_query
            search_terms = " ".join(query.search_terms) if query.search_terms else ""
            
            # Build Visenze filters
            visenze_filters = self._build_visenze_filters(query.primary_filters, query.price_filters)
            
            # Log essential search parameters
            logger.info(f"🔍 VISENZE SEARCH: {query.search_type.upper()} | Terms: '{search_terms}' | Filters: {query.primary_filters} | Limit: {query.result_limit}")
            
            # Execute search using Visenze API
            logger.info(f"🔍 Calling Visenze API with:")
            logger.info(f"   Query: {search_terms}")
            logger.info(f"   Filters: {visenze_filters}")
            logger.info(f"   Limit: {query.result_limit}")
            
            # For now, use text search without filters to avoid API errors
            # TODO: Implement proper filter format once we understand Visenze's requirements
            response = vi_multisearch(
                app_key=self.app_key,
                placement_id=self.placement_id,
                q=search_terms if search_terms else None,
                # filters=visenze_filters,  # Temporarily disabled until we fix filter format
                limit=query.result_limit,
                score=True,
                return_fields_mapping=True,
                attrs_to_get=["title", "price", "color", "category", "brand", "link"]
            )
            
            logger.info(f"📤 Visenze API response: {response}")
            
            if response.get("status") != "OK":
                error_msg = response.get('error', 'Unknown error')
                logger.error(f"Visenze search failed: {error_msg}")
                logger.error(f"Full response: {response}")
                return False
            
            # Process results
            search_state.current_results = []
            items = response.get("result", [])
            
            if items:
                # Deduplicate products and aggregate sizes
                product_map = {}
                
                for item in items:
                    product = self._map_visenze_item_to_product(item)
                    if not product:
                        continue
                    
                    # Create a unique key based on title and brand
                    unique_key = f"{product.title}_{product.brand}_{product.category}_{product.colour}"
                    
                    if unique_key in product_map:
                        # Product already exists, add size if different
                        existing_product = product_map[unique_key]
                        if product.size and product.size != existing_product.size:
                            if not hasattr(existing_product, 'available_sizes'):
                                existing_product.available_sizes = [existing_product.size]
                            if product.size not in existing_product.available_sizes:
                                existing_product.available_sizes.append(product.size)
                    else:
                        # New product, initialise available sizes
                        if product.size:
                            product.available_sizes = [product.size]
                        else:
                            product.available_sizes = []
                        product_map[unique_key] = product
                
                # Convert map back to list
                search_state.current_results = list(product_map.values())
                
                # Log products found (concise format)
                logger.info(f"✅ Found {len(search_state.current_results)} products:")
                for i, product in enumerate(search_state.current_results[:5]):  # Log first 5 results
                    size_info = f" | Sizes: {', '.join(product.available_sizes)}" if hasattr(product, 'available_sizes') and product.available_sizes else ""
                    logger.info(f"   {i+1}. {product.title} - £{product.sale_price}{size_info}")
                
                if len(search_state.current_results) > 5:
                    logger.info(f"   ... and {len(search_state.current_results) - 5} more")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Visenze search execution failed: {e}")
            search_state.current_results = []
            return False
    
    def _build_visenze_filters(self, primary_filters: Dict, price_filters: Dict) -> Optional[str]:
        """
        Construct Visenze filters string.
        
        Args:
            primary_filters: Dictionary of field-value filters
            price_filters: Dictionary of price range filters
            
        Returns:
            Visenze filters string or None if no filters
        """
        filter_parts = []
        
        # Build primary filters - only include non-empty values
        for field, value in primary_filters.items():
            if value and value.strip():  # Check for non-empty, non-whitespace values
                # Map field names to Visenze format
                visenze_field = self._map_field_to_visenze(field)
                if visenze_field:
                    # Use the correct Visenze filter format
                    filter_parts.append(f"{visenze_field}:{value}")
                    logger.info(f"   Filter: {visenze_field} = {value}")
        
        # Build price filters - only include valid price ranges
        if price_filters:
            min_price = price_filters.get("min_price")
            max_price = price_filters.get("max_price")
            
            # Only add price filters if they make sense
            if min_price is not None and max_price is not None and min_price < max_price:
                filter_parts.append(f"price:[{min_price} TO {max_price}]")
                logger.info(f"   Filter: price between {min_price} and {max_price}")
            elif min_price is not None and min_price > 0:
                filter_parts.append(f"price:[{min_price} TO *]")
                logger.info(f"   Filter: price >= {min_price}")
            elif max_price is not None and max_price > 0:
                filter_parts.append(f"price:[* TO {max_price}]")
                logger.info(f"   Filter: price <= {max_price}")
        
        return " AND ".join(filter_parts) if filter_parts else None
    
    def _map_field_to_visenze(self, field: str) -> Optional[str]:
        """
        Map internal field names to Visenze field names.
        
        Args:
            field: Internal field name
            
        Returns:
            Visenze field name or None if not supported
        """
        # Start with a minimal set of commonly supported fields
        field_mapping = {
            "category": "category",
            "color": "color", 
            "brand": "brand"
        }
        return field_mapping.get(field)
    
    def _are_results_satisfactory(self, search_state: SearchState) -> bool:
        """
        Determine if search results meet quality standards.
        
        Args:
            search_state: Current search state with results
            
        Returns:
            True if results are satisfactory, False if search should be broadened
        """
        results_count = search_state.get_result_count()
        
        # No results are definitely not satisfactory
        if results_count == 0:
            return False
        
        # Good number of results are usually satisfactory
        if results_count >= 5:
            return True
        
        # For edge cases with few results, consider them satisfactory if we have any
        return results_count > 0
    
    def _broaden_search_query(self, original_query: SearchQuery) -> SearchQuery:
        """
        Create a broader version of the search query to find more results.
        
        Args:
            original_query: The original search query that needs broadening
            
        Returns:
            SearchQuery with broadened parameters
        """
        new_filters = original_query.primary_filters.copy()
        new_price_filters = original_query.price_filters.copy()
        
        # Remove filters in order of specificity (most specific first)
        if "size" in new_filters:
            del new_filters["size"]
            logger.info("Removed size filter to broaden search")
        elif "color" in new_filters:
            del new_filters["color"]
            logger.info("Removed color filter to broaden search")
        elif len(new_filters) > 1:
            # Remove first non-essential filter (keep product_category as it's important)
            filter_to_remove = next(iter(new_filters))
            if filter_to_remove != "product_category":
                del new_filters[filter_to_remove]
                logger.info(f"Removed {filter_to_remove} filter to broaden search")
        
        # Increase result limit for broader search
        new_limit = min(original_query.result_limit + 5, 20)
        
        return SearchQuery(
            search_terms=original_query.search_terms,
            primary_filters=new_filters,
            price_filters=new_price_filters,
            search_type=original_query.search_type,
            result_limit=new_limit
        )
    
    def _map_visenze_item_to_product(self, item: Dict) -> Optional[Product]:
        """
        Map Visenze API response item to Product entity.
        
        Args:
            item: Dictionary from Visenze API response
            
        Returns:
            Product entity with mapped properties or None if mapping fails
        """
        try:
            # Extract product data from Visenze response
            data = item.get("data", {})
            
            # Handle price data - it might be a dict with currency and value
            price_data = data.get("price", 0)
            if isinstance(price_data, dict):
                price = price_data.get("value", 0)
            else:
                price = price_data
            
            sale_price_data = data.get("sale_price", price)
            if isinstance(sale_price_data, dict):
                sale_price = sale_price_data.get("value", price)
            else:
                sale_price = sale_price_data
            
            return Product(
                title=data.get("title", "Unknown"),
                brand=data.get("brand", ""),
                price=price,
                sale_price=sale_price,
                colour=data.get("color", ""),
                size=data.get("size", ""),
                material=data.get("material", ""),
                category=data.get("category", ""),
                style=data.get("style", ""),
                occasion=data.get("occasion", ""),
                description=data.get("description", ""),
                product_url=data.get("link", ""),
                product_id=item.get("product_id", "")
            )
        except Exception as e:
            logger.error(f"Error mapping Visenze item to Product: {e}")
            return None
