# Shoeby Conversational Agent Project Overview

## Project Purpose
A conversational LLM agent designed for intelligent fashion retail assistance at Shoeby. The system helps customers find products, get style recommendations, and manage shopping carts through natural language conversations.

## Tech Stack

### Backend
- **Python 3.11+** - Core language
- **FastAPI 0.104+** - Web framework for API
- **Uvicorn** - ASGI server
- **Weaviate 4.4.0** - Vector database for semantic product search
- **MongoDB** - Document database for conversation history
- **Motor** - Async MongoDB driver
- **Pydantic 2.5+** - Data validation
- **LangGraph 0.0.40+** - Agent orchestration framework
- **Lang<PERSON>hain** - LLM framework components

### Frontend
- **React 18** - UI framework
- **Vite 5.0** - Build tool
- **Axios 1.6** - HTTP client

### LLM Integration
- **BrainPowa** - Primary LLM provider
- **OpenAI Client** - For API compatibility

### Observability
- **OpenTelemetry** - Tracing and monitoring
- **Langfuse** - LLM observability

## Project Structure
```
/
├── agents/           # Agent implementations (base, strategy, registry)
├── api/              # FastAPI application and routers
├── config/           # Configuration management
├── core/             # Core business logic (orchestrator, search engine)
├── frontend/         # React frontend application
├── observability/    # Monitoring and tracing
├── prompts/          # LLM prompt templates
├── services/         # Service layer (LLM, conversation, datetime)
├── tools/            # Tool implementations for agents
├── docker-compose.yml # Database services setup
├── main.py           # Main entry point for interactive mode
├── api.py            # FastAPI server entry point
└── pyproject.toml    # Python dependencies (using uv/pip)
```

## Key Components
- **ShoebyAgent**: Main agent class managing the system
- **AgentOrchestrator**: LangGraph-based orchestrator for agent coordination
- **AgentsRegistry**: Central registry for all agents
- **ToolsRegistry**: Central registry for all tools
- **SearchEngine**: Weaviate-based semantic product search
- **LLMService**: Multi-provider LLM abstraction
- **ConversationService**: MongoDB-backed conversation persistence