@echo off
REM 🚀 Shoeby Agent Quick Start Script for Windows
REM This script will set up your local development environment

echo 🛍️ Welcome to Shoeby Agent Quick Start!
echo ========================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH. Please install Python 3.8+ and try again.
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python -c "import sys; print(sys.version.split()[0])" 2^>nul') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% detected

REM Create virtual environment if it doesn't exist
if not exist "venv" (
    echo 🔧 Creating Python virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install Python dependencies
echo 📦 Installing Python dependencies...
pip install -r requirements.txt

REM Start databases with Docker Compose
echo 🐳 Starting databases with Docker Compose...
docker-compose up -d

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM Test MongoDB connection
echo 🔍 Testing MongoDB connection...
docker exec shoeby-mongodb mongosh --quiet --eval "db.runCommand('ping')" >nul 2>&1
if errorlevel 1 (
    echo ❌ MongoDB is not ready yet. Waiting a bit more...
    timeout /t 10 /nobreak >nul
    docker exec shoeby-mongodb mongosh --quiet --eval "db.runCommand('ping')" >nul 2>&1
    if errorlevel 1 (
        echo ❌ MongoDB failed to start. Check docker-compose logs.
        pause
        exit /b 1
    )
)
echo ✅ MongoDB is ready

REM Test Weaviate connection
echo 🔍 Testing Weaviate connection...
curl -s http://localhost:8080/v1/.well-known/ready >nul 2>&1
if errorlevel 1 (
    echo ❌ Weaviate is not ready yet. Waiting a bit more...
    timeout /t 10 /nobreak >nul
    curl -s http://localhost:8080/v1/.well-known/ready >nul 2>&1
    if errorlevel 1 (
        echo ❌ Weaviate failed to start. Check docker-compose logs.
        pause
        exit /b 1
    )
)
echo ✅ Weaviate is ready

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo 🔧 Creating .env file from template...
    copy env.example .env
    echo 📝 Please edit .env file with your API keys and configuration
    echo    You can do this now or later, but the agent won't work without proper API keys
)

REM Test basic Python imports
echo 🔍 Testing Python imports...
python -c "from config.settings import AgentConfig; print('✅ Configuration loaded successfully')" 2>nul
if errorlevel 1 (
    echo ❌ Python setup failed. Check the error above.
    pause
    exit /b 1
)
echo ✅ Python setup is working

echo.
echo 🎉 Setup completed successfully!
echo ================================
echo.
echo 🚀 To start the agent:
echo    venv\Scripts\activate.bat
echo    python main.py
echo.
echo 🌐 To start the API server:
echo    venv\Scripts\activate.bat
echo    python api.py
echo.
echo 🎨 To start the frontend:
echo    cd frontend
echo    npm install
echo    npm run dev
echo.
echo 📊 To view running services:
echo    docker-compose ps
echo.
echo 📝 Don't forget to:
echo    1. Edit .env file with your API keys
echo    2. Restart the agent after updating .env
echo.
echo 🆘 For help, see SETUP.md
echo.
echo Happy coding! 🎉
pause
