receivers:
  otlp:
    protocols:
      http:  # listens on 0.0.0.0:4318 by default
      grpc:  # listens on 0.0.0.0:4317 by default

processors:
  batch: {}

exporters:
  logging:
    loglevel: debug
  jaeger:
    # Use gRPC to push to <PERSON>aeger all-in-one collector inside compose
    endpoint: jaeger:14250
    tls:
      insecure: true

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [jaeger, logging]
