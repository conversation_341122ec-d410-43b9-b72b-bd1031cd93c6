"""
Basket management tools for the <PERSON><PERSON>by agent.
Handles shopping basket operations like adding, removing, and managing items.
Stores basket data in memory as JSON for persistence across requests.
Also provides dynamic responses about basket contents and updates.
"""
import json
import logging

from .base_tool import BaseTool
from typing import Dict, Any, List
from core.entities import BasketItem
from services.mongodb_utils.session_cache import SessionCacheService
from services.llm_service import LLMService

logger = logging.getLogger(__name__)

class UpdateBasketTool(BaseTool):
    """Tool for managing shopping basket operations with in-memory JSON storage"""
    
    def __init__(self, session_cache_service: SessionCacheService = None):
        super().__init__(
            name="update_basket",
            description="Add or remove items from the customer's shopping basket"
        )
        self.session_cache_service = session_cache_service
        self._basket_items: List[BasketItem] = []
        self._basket_data: Dict[str, Any] = {
            "items": [],
            "total_items": 0,
            "total_quantity": 0,
            "total_price": 0.0
        }
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute basket operations"""
        try:
            action = params.get("action", "").lower()
            product_name = params.get("product_name", "")
            size = params.get("size", "")
            quantity = params.get("quantity", 1)
            
            if action not in ["add", "remove", "update_quantity", "clear", "get_contents"]:
                return {
                    "success": False,
                    "error": "Invalid action",
                    "message": "Action must be 'add', 'remove', 'update_quantity', 'clear', or 'get_contents'"
                }
            
            if action == "add":
                return await self._add_to_basket(product_name, size, quantity, params)
            elif action == "remove":
                return await self._remove_from_basket(product_name, size, params)
            elif action == "update_quantity":
                return await self._update_quantity(product_name, size, quantity, params)
            elif action == "clear":
                return await self._clear_basket(params)
            elif action == "get_contents":
                return await self._get_basket_contents(params)
            
        except Exception as e:
            logger.error(f"BasketTool: Error executing: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Error updating basket"
            }
    
    async def _add_to_basket(self, product_name: str, size: str, quantity: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Add item to basket"""
        try:
            # Get product details from params
            product_details = params.get("product_details", {})
            price = product_details.get("price", 0)
            
            # Check if item already exists
            existing_item = None
            for item in self._basket_items:
                if (item.name.lower() == product_name.lower() and 
                    item.size == size):
                    existing_item = item
                    break
            
            if existing_item:
                # Update quantity if item already exists
                existing_item.update_quantity(existing_item.quantity + quantity)
                message = f"Updated quantity of {product_name} in your basket"
            else:
                # Add new item to basket
                new_item = BasketItem(
                    name=product_name,
                    price=price,
                    quantity=quantity,
                    size=size
                )
                self._basket_items.append(new_item)
                message = f"Added {product_name} to your basket"
            
            # Update JSON basket data
            self._update_basket_data()
            
            return {
                "success": True,
                "message": message,
                "basket_item": {
                    "name": product_name,
                    "size": size,
                    "quantity": existing_item.quantity if existing_item else quantity,
                    "price": price
                },
                "basket_status": self._basket_data.copy(),
                "basket_items": self._basket_data["items"]
            }
            
        except Exception as e:
            logger.error(f"BasketTool: Error adding to basket: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to add item to basket"
            }
    
    async def _remove_from_basket(self, product_name: str, size: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Remove item from basket"""
        try:
            # Find and remove the item
            removed = False
            for i, item in enumerate(self._basket_items):
                if (item.name.lower() == product_name.lower() and 
                    item.size == size):
                    self._basket_items.pop(i)
                    removed = True
                    break
            
            if removed:
                # Update JSON basket data
                self._update_basket_data()
                
                return {
                    "success": True,
                    "message": f"Removed {product_name} from your basket",
                    "basket_status": self._basket_data.copy(),
                    "basket_items": self._basket_data["items"]
                }
            else:
                return {
                    "success": False,
                    "error": "Item not found in basket",
                    "message": f"{product_name} was not found in your basket"
                }
            
        except Exception as e:
            logger.error(f"BasketTool: Error removing from basket: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to remove item from basket"
            }
    
    async def _update_quantity(self, product_name: str, size: str, quantity: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """Update quantity of item in basket"""
        try:
            # Find the item
            for item in self._basket_items:
                if (item.name.lower() == product_name.lower() and 
                    item.size == size):
                    if quantity <= 0:
                        # Remove item if quantity is 0 or negative
                        self._basket_items.remove(item)
                        message = f"Removed {product_name} from your basket"
                    else:
                        # Update quantity
                        item.update_quantity(quantity)
                        message = f"Updated quantity of {product_name} to {quantity}"
                    
                    # Update JSON basket data
                    self._update_basket_data()
                    
                    return {
                        "success": True,
                        "message": message,
                        "basket_status": self._basket_data.copy(),
                        "basket_items": self._basket_data["items"]
                    }
            
            return {
                "success": False,
                "error": "Item not found in basket",
                "message": f"{product_name} was not found in your basket"
            }
            
        except Exception as e:
            logger.error(f"BasketTool: Error updating quantity: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to update quantity"
            }
    
    async def _clear_basket(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Clear all items from basket"""
        try:
            item_count = len(self._basket_items)
            self._basket_items.clear()
            
            # Update JSON basket data
            self._update_basket_data()
            
            return {
                "success": True,
                "message": f"Cleared {item_count} items from your basket",
                "basket_status": self._basket_data.copy(),
                "basket_items": self._basket_data["items"]
            }
            
        except Exception as e:
            logger.error(f"BasketTool: Error clearing basket: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to clear basket"
            }
    
    async def _get_basket_contents(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Get current basket contents"""
        try:
            # Update JSON basket data
            self._update_basket_data()
            
            return {
                "success": True,
                "basket_items": self._basket_data["items"],
                "basket_status": self._basket_data.copy()
            }
            
        except Exception as e:
            logger.error(f"BasketTool: Error getting basket contents: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to get basket contents"
            }
    
    def get_basket_contents(self) -> List[BasketItem]:
        """Get basket contents as BasketItem objects (for API compatibility)"""
        return self._basket_items.copy()
    
    def get_basket_item_count(self) -> int:
        """Get total number of items in basket (for API compatibility)"""
        return len(self._basket_items)
    
    def get_basket_total(self) -> float:
        """Get total price of basket (for API compatibility)"""
        return sum(item.price * item.quantity for item in self._basket_items)
    
    def _update_basket_data(self):
        """Update the JSON basket data from current basket items"""
        try:
            self._basket_data["items"] = [
                {
                    "item": item.name,
                    "size": item.size,
                    "quantity": item.quantity
                }
                for item in self._basket_items
            ]
            self._basket_data["total_items"] = len(self._basket_items)
            self._basket_data["total_quantity"] = sum(item.quantity for item in self._basket_items)
            self._basket_data["total_price"] = sum(item.price * item.quantity for item in self._basket_items)
            
            # Debug print statement
            print(f"🛒 BASKET JSON DEBUG: {self._basket_data}")
            logger.debug(f"Updated basket data: {self._basket_data}")
        except Exception as e:
            logger.error(f"Error updating basket data: {e}")
    
    def get_basket_data(self) -> Dict[str, Any]:
        """Get current basket data as JSON-compatible dictionary"""
        self._update_basket_data()
        return self._basket_data.copy()
    
    def get_basket_json(self) -> str:
        """Get current basket data as JSON string"""
        return json.dumps(self.get_basket_data(), indent=2)
    
    def load_basket_from_json(self, json_data: str) -> bool:
        """Load basket data from JSON string"""
        try:
            data = json.loads(json_data)
            self._basket_items.clear()
            
            for item_data in data.get("items", []):
                item = BasketItem(
                    name=item_data.get("name", ""),
                    price=item_data.get("price", 0),
                    quantity=item_data.get("quantity", 1),
                    size=item_data.get("size", "")
                )
                self._basket_items.append(item)
            
            self._update_basket_data()
            return True
        except Exception as e:
            logger.error(f"Error loading basket from JSON: {e}")
            return False
    
    async def generate_confirmation_response(
        self, 
        action: str, 
        product_name: str, 
        size: str = "", 
        quantity: int = 1, 
        focused_instructions: str = "",
        llm_service: LLMService = None
    ) -> str:
        """
        Generate a confirmation response for basket operations using the confirmation prompt.
        
        Args:
            action (str): The action performed
            product_name (str): The product involved
            size (str): The size of the product (optional)
            quantity (int): The quantity involved
            focused_instructions (str): Focused instructions for the current turn
            llm_service (LLMService): LLM service for generating response
            
        Returns:
            str: Generated confirmation response
        """
        if not llm_service:
            # Fallback to simple response if no LLM service provided
            size_info = f" (size {size})" if size else ""
            quantity_info = f" x{quantity}" if quantity > 1 else ""
            return f"Basket {action} operation completed for {product_name}{size_info}{quantity_info}."
        
        try:
            from prompts.basket import get_update_basket_confirmation_prompt
            prompt = get_update_basket_confirmation_prompt(
                action, product_name, size, quantity, focused_instructions
            )
            
            response = await llm_service.generate_response(prompt)
            return response.strip()
            
        except Exception as e:
            logger.error(f"BasketTool: Error generating confirmation response: {e}")
            # Fallback to simple response
            size_info = f" (size {size})" if size else ""
            quantity_info = f" x{quantity}" if quantity > 1 else ""
            return f"Basket {action} operation completed for {product_name}{size_info}{quantity_info}."

    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema for the LLM (required by agents registry)"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "enum": ["add", "remove", "update_quantity", "clear", "get_contents"],
                        "description": "The action to perform on the basket"
                    },
                    "product_name": {
                        "type": "string",
                        "description": "The name of the product to add, remove, or update"
                    },
                    "size": {
                        "type": "string",
                        "description": "The size of the product (optional)"
                    },
                    "quantity": {
                        "type": "integer",
                        "description": "The quantity to add or update (default: 1)"
                    },
                    "product_details": {
                        "type": "object",
                        "description": "Product details including price (required for add action)",
                        "properties": {
                            "price": {
                                "type": "number",
                                "description": "The price of the product"
                            }
                        }
                    }
                },
                "required": ["action"]
            }
        }


class BasketInfoTool(BaseTool):
    """Tool for providing dynamic responses about basket contents and updates"""
    
    def __init__(self, llm_service: LLMService):
        super().__init__(
            name="basket_info",
            description="Provide information about basket contents and generate dynamic responses for basket updates"
        )
        self.llm_service = llm_service
    
    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the basket info tool"""
        try:
            basket_data = params.get("basket_data", {})
            action = params.get("action", "")
            focused_instructions = params.get("focused_instructions", "")
            product_name = params.get("product_name", "")
            previous_basket_data = params.get("previous_basket_data", {})
            
            # Debug print statement
            print(f"📋 BASKET INFO TOOL DEBUG - Received basket_data: {basket_data}")
            
            # Generate dynamic response based on action and context
            response_text = await self._generate_basket_response(
                basket_data, action, focused_instructions, product_name, previous_basket_data
            )
            
            return {
                "success": True,
                "response_text": response_text,
                "basket_data": basket_data,
                "action": action
            }
            
        except Exception as e:
            logger.error(f"BasketInfoTool: Error executing: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Error generating basket response"
            }
    
    async def _generate_basket_response(self, basket_data: Dict[str, Any], action: str, 
                                      focused_instructions: str, product_name: str = "", 
                                      previous_basket_data: Dict[str, Any] = None) -> str:
        """Generate dynamic response for basket operations"""
        try:
            # Format basket items for LLM
            basket_summary = self._format_basket_for_llm(basket_data)
            previous_summary = self._format_basket_for_llm(previous_basket_data) if previous_basket_data else "No previous basket data"
            
            # Import and use the prompt function
            from prompts.basket import get_basket_info_prompt
            prompt = get_basket_info_prompt(action, product_name, focused_instructions, basket_summary, previous_summary)
            
            response = await self.llm_service.generate_response(prompt)
            return response.strip()
            
        except Exception as e:
            logger.error(f"BasketInfoTool: Error generating response: {e}")
            # Fallback to simple response
            return f"Basket {action} operation completed successfully."
    
    def _format_basket_for_llm(self, basket_data: Dict[str, Any]) -> str:
        """Format basket data for LLM consumption"""
        if not basket_data or not basket_data.get("items"):
            return "Basket is empty"
        
        items = basket_data.get("items", [])
        total_items = basket_data.get("total_items", 0)
        total_price = basket_data.get("total_price", 0)
        
        if not items:
            return "Basket is empty"
        
        formatted_items = []
        for item in items:
            name = item.get("name", "Unknown item")
            price = item.get("price", 0)
            quantity = item.get("quantity", 1)
            size = item.get("size", "")
            
            size_info = f" (size {size})" if size else ""
            quantity_info = f" x{quantity}" if quantity > 1 else ""
            
            formatted_items.append(f"- {name}{size_info}{quantity_info} - £{price:.2f}")
        
        items_text = "\n".join(formatted_items)
        
        return f"""Basket contains {total_items} item(s) totaling £{total_price:.2f}: {items_text}"""
    
    def get_schema(self) -> Dict[str, Any]:
        """Return the tool's parameter schema for the LLM"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "basket_data": {
                        "type": "object",
                        "description": "Current basket data including items, totals, and status",
                        "properties": {
                            "items": {
                                "type": "array",
                                "description": "List of items in the basket",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string"},
                                        "price": {"type": "number"},
                                        "quantity": {"type": "integer"},
                                        "size": {"type": "string"}
                                    }
                                }
                            },
                            "total_items": {"type": "integer"},
                            "total_quantity": {"type": "integer"},
                            "total_price": {"type": "number"}
                        }
                    },
                    "action": {
                        "type": "string",
                        "enum": ["add", "remove", "update", "get_contents", "clear"],
                        "description": "The action performed on the basket"
                    },
                    "focused_instructions": {
                        "type": "string",
                        "description": "Focused, relevant context for the current turn"
                    },
                    "product_name": {
                        "type": "string",
                        "description": "Name of the product being added/removed (for add/remove actions)"
                    },
                    "previous_basket_data": {
                        "type": "object",
                        "description": "Previous basket state (for comparison in updates)"
                    }
                },
                "required": ["basket_data", "action", "focused_instructions"]
            }
        }
